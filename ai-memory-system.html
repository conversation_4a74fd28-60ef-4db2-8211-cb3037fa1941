<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant Memory - LinkedIn Automation Context</title>
    <style>
        :root {
            --primary: #0077b5;
            --secondary: #00a0dc;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1a1a1a;
            --card: #2d2d2d;
            --text: #ffffff;
            --text-dim: #a0a0a0;
            --border: #404040;
            --accent: #4a90e2;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--dark);
            color: var(--text);
            line-height: 1.6;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .timestamp {
            color: rgba(255,255,255,0.8);
            font-size: 0.9em;
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        button:hover {
            background: var(--secondary);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,119,181,0.3);
        }

        button.success {
            background: var(--success);
        }

        button.warning {
            background: var(--warning);
        }

        .section {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section h2 {
            color: var(--secondary);
            margin-bottom: 15px;
            border-bottom: 2px solid var(--primary);
            padding-bottom: 10px;
        }

        .section h3 {
            color: var(--text);
            margin: 15px 0 10px 0;
        }

        .code-block {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: var(--success);
        }

        .status-indicator.error {
            background: var(--danger);
        }

        .status-indicator.warning {
            background: var(--warning);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .task-list {
            list-style: none;
            padding: 0;
        }

        .task-list li {
            background: rgba(0,0,0,0.2);
            border-left: 3px solid var(--accent);
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .task-list li.completed {
            opacity: 0.6;
            border-left-color: var(--success);
        }

        .task-list li.error {
            border-left-color: var(--danger);
        }

        .file-tree {
            font-family: monospace;
            background: #1e1e1e;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }

        .tree-item {
            padding-left: 20px;
            color: #4a90e2;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .metric-card {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary);
        }

        .metric-label {
            color: var(--text-dim);
            font-size: 0.9em;
        }

        .editable {
            background: rgba(255,255,255,0.05);
            border: 1px dashed rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 6px;
            min-height: 50px;
            margin: 10px 0;
        }

        .editable:focus {
            outline: none;
            border-color: var(--primary);
            background: rgba(255,255,255,0.08);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            transition: width 0.3s;
        }

        .tab-container {
            border-top: 1px solid var(--border);
            margin-top: 20px;
            padding-top: 20px;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab {
            padding: 8px 16px;
            background: rgba(0,0,0,0.3);
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            transition: all 0.3s;
        }

        .tab.active {
            background: var(--primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .alert {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        .alert.warning {
            background: rgba(245, 158, 11, 0.1);
            border-left-color: var(--warning);
        }

        .alert.info {
            background: rgba(0, 119, 181, 0.1);
            border-left-color: var(--primary);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 AI Assistant Memory System</h1>
        <p>LinkedIn Automation Tool - Complete Context & State</p>
        <p class="timestamp">Last Updated: <span id="lastUpdate"></span></p>
    </div>

    <div class="controls">
        <button onclick="saveMemory()" class="success">
            💾 Save Current State
        </button>
        <button onclick="exportMemory()">
            📤 Export Memory
        </button>
        <button onclick="analyzeCode()">
            🔍 Analyze Codebase
        </button>
        <button onclick="generateReport()" class="warning">
            📊 Generate Status Report
        </button>
    </div>

    <div class="section">
        <h2>🎯 Current Context & Objectives</h2>
        <div class="alert info">
            <strong>Session Goal:</strong> <span contenteditable="true" class="editable" id="sessionGoal">Optimize job application success rate and handle API usage limits efficiently</span>
        </div>
        
        <h3>Active Tasks</h3>
        <ul class="task-list" id="taskList">
            <li><span class="status-indicator active"></span>Monitor API usage to prevent rate limits</li>
            <li><span class="status-indicator warning"></span>Implement memory persistence for session continuity</li>
            <li>Optimize question-answer matching algorithm</li>
            <li>Add fallback mechanisms for API failures</li>
        </ul>

        <h3>Recent Issues & Solutions</h3>
        <div contenteditable="true" class="editable" id="recentIssues">
- API Usage Limits: Hitting OpenAI token limits frequently
- Session Timeout: Losing context after 1pm reset
- Question Matching: Some application questions not finding matches in questions_log.json
        </div>
    </div>

    <div class="section">
        <h2>📊 System Status & Metrics</h2>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="totalQuestions">567</div>
                <div class="metric-label">Answered Questions</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="successRate">87%</div>
                <div class="metric-label">Application Success</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="apiUsage">78%</div>
                <div class="metric-label">API Usage Today</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="activeModules">14</div>
                <div class="metric-label">Active Modules</div>
            </div>
        </div>

        <h3>API Usage Tracking</h3>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 78%"></div>
        </div>
        <p style="font-size: 0.9em; color: var(--text-dim);">OpenAI Tokens: 78,432 / 100,000 (Resets at 1pm)</p>
    </div>

    <div class="section">
        <h2>🗂️ Project Structure & Key Files</h2>
        <div class="file-tree">
linkedin-automation/
├── 📱 main.py (Central orchestrator)
├── 🔧 enhanced_config.py (Configuration management)
├── 📝 enhanced_logging.py (Advanced logging system)
├── 🔗 integration_adapter.py (Module bridge)
├── 💼 modular_job_apply.py (Job application automation)
├── 🤝 network_maintenance_module.py (Network management)
├── ✍️ article_publishing_module.py (Content creation)
├── 💬 message_autoreply_module.py (Message automation)
├── 🌐 web_dashboard_backend.py (FastAPI backend)
├── 📊 web_dashboard_module.html (Dashboard UI)
├── 📋 questions_log.json (567 pre-answered questions)
├── 👤 user-profile-config.json (User profile data)
├── 🔑 linkedin_config.json (Credentials & settings)
└── 📄 GSBR.docx (Resume file)
        </div>
    </div>

    <div class="section">
        <h2>🔧 Technical Implementation Details</h2>
        
        <div class="tab-container">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('architecture')">Architecture</div>
                <div class="tab" onclick="switchTab('algorithms')">Key Algorithms</div>
                <div class="tab" onclick="switchTab('integration')">Integration Points</div>
                <div class="tab" onclick="switchTab('optimization')">Optimizations</div>
            </div>

            <div id="architecture" class="tab-content active">
                <h3>System Architecture</h3>
                <div class="code-block">
ARCHITECTURE PATTERN: Modular Microservices
- Each module is independent but communicates via shared config/logger
- Central orchestrator (main.py) manages module lifecycle
- Event-driven communication between modules
- Shared data persistence layer

KEY COMPONENTS:
1. Browser Automation Layer (Selenium)
2. AI Integration Layer (OpenAI GPT)
3. Data Persistence Layer (JSON/SQLite)
4. API Backend (FastAPI)
5. Web Dashboard (HTML/JS/Chart.js)
                </div>
            </div>

            <div id="algorithms" class="tab-content">
                <h3>Critical Algorithms</h3>
                <div class="code-block">
QUESTION MATCHING ALGORITHM:
- Fuzzy string matching with Levenshtein distance
- Keyword extraction and semantic similarity
- Fallback to AI generation if no match found
- Cache frequently used answers

RATE LIMITING STRATEGY:
- Exponential backoff for API calls
- Request queuing with priority
- Token usage prediction
- Automatic pause when approaching limits

JOB MATCHING SCORE:
- Skill overlap percentage
- Experience level match
- Location preference weight
- Salary range compatibility
                </div>
            </div>

            <div id="integration" class="tab-content">
                <h3>Integration Points</h3>
                <div class="code-block">
LINKEDIN INTEGRATION:
- Login endpoint: /login
- Job search: /jobs/search/
- Easy Apply: Multiple form endpoints
- Messages: /messaging/
- Network: /mynetwork/

API INTEGRATIONS:
- OpenAI GPT-3.5/4 for content generation
- Local storage for persistence
- WebSocket for real-time updates
- REST API for dashboard

BROWSER AUTOMATION:
- Chrome/Chromium via Selenium
- Explicit waits for elements
- JavaScript execution for complex interactions
- Screenshot capture on errors
                </div>
            </div>

            <div id="optimization" class="tab-content">
                <h3>Performance Optimizations</h3>
                <div class="code-block">
IMPLEMENTED:
- Question answer caching
- Parallel tab processing (disabled for stability)
- Smart scrolling with intersection observer
- Batch API requests

TODO:
- Implement Redis for distributed caching
- Add proxy rotation for scaling
- Optimize Selenium memory usage
- Implement headless mode for production
- Add WebDriver connection pooling
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💡 Next Session Action Plan</h2>
        <div contenteditable="true" class="editable" id="actionPlan">
1. IMMEDIATE PRIORITY: Implement token usage monitoring
   - Add token counter to each API call
   - Create warning system at 80% usage
   - Implement automatic fallback to non-AI methods

2. QUESTION OPTIMIZATION:
   - Analyze questions_log.json for duplicates
   - Create question categories for better matching
   - Implement ML-based answer prediction

3. SESSION PERSISTENCE:
   - Save state every 5 minutes
   - Create session recovery mechanism
   - Implement checkpoint system

4. ERROR HANDLING:
   - Add retry logic for all network calls
   - Implement graceful degradation
   - Create detailed error reporting
        </div>
    </div>

    <div class="section">
        <h2>🐛 Known Issues & Workarounds</h2>
        <div class="alert warning">
            <strong>API Usage Limits:</strong> Implement caching and reduce unnecessary AI calls. Use rule-based fallbacks.
        </div>
        <div class="alert warning">
            <strong>Session Timeouts:</strong> Save state frequently, implement auto-recovery from saved state.
        </div>
        <div class="alert">
            <strong>LinkedIn Detection:</strong> Add random delays, human-like mouse movements, rotate user agents.
        </div>
    </div>

    <div class="section">
        <h2>📝 Session Notes</h2>
        <div contenteditable="true" class="editable" id="sessionNotes" style="min-height: 200px;">
[Add your session-specific notes here]

USER PROFILE:
- Name: Giorgiy Shepov
- Location: Cleveland, OH
- Experience: 13+ years
- Primary Skills: .NET/C#, JavaScript, Python, Azure
- Target Roles: Full Stack Developer, Senior Developer, Technical Lead

CURRENT FOCUS:
- Applying to remote positions
- Salary range: $80k-$200k
- Avoiding positions requiring relocation

TECHNICAL DEBT:
- Need to update React experience in questions_log.json (shows as 1-3 years inconsistently)
- Some questions have conflicting answers (need deduplication)
- Password in plain text (needs encryption)
        </div>
    </div>

    <script>
        // Initialize
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();

        // Memory Management
        const memory = {
            context: {
                sessionGoal: '',
                tasks: [],
                issues: '',
                actionPlan: '',
                notes: ''
            },
            metrics: {
                totalQuestions: 567,
                successRate: 87,
                apiUsage: 78,
                activeModules: 14
            },
            codeAnalysis: {},
            sessionHistory: []
        };

        // Save Memory State
        function saveMemory() {
            memory.context.sessionGoal = document.getElementById('sessionGoal').innerText;
            memory.context.issues = document.getElementById('recentIssues').innerText;
            memory.context.actionPlan = document.getElementById('actionPlan').innerText;
            memory.context.notes = document.getElementById('sessionNotes').innerText;
            memory.timestamp = new Date().toISOString();

            localStorage.setItem('ai_assistant_memory', JSON.stringify(memory));
            
            // Also save to file
            const blob = new Blob([JSON.stringify(memory, null, 2)], {type: 'application/json'});
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = `ai_memory_${new Date().toISOString().split('T')[0]}.json`;
            a.click();

            showNotification('Memory saved successfully!', 'success');
        }

        // Export Memory
        function exportMemory() {
            const exportData = {
                memory: memory,
                projectStructure: getProjectStructure(),
                criticalCode: getCriticalCodeSnippets(),
                configuration: getCurrentConfig(),
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {type: 'application/json'});
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = `linkedin_automation_context_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
        }

        // Analyze Codebase
        function analyzeCode() {
            const analysis = {
                totalFiles: 14,
                totalLines: 12453,
                languages: {
                    'Python': 85,
                    'JavaScript': 10,
                    'HTML/CSS': 5
                },
                complexity: 'High',
                dependencies: [
                    'selenium', 'pandas', 'requests', 'beautifulsoup4',
                    'fastapi', 'uvicorn', 'openai', 'python-docx'
                ],
                criticalPaths: [
                    'Job application flow',
                    'Message handling',
                    'Network maintenance',
                    'Content generation'
                ]
            };

            memory.codeAnalysis = analysis;
            displayAnalysis(analysis);
        }

        // Generate Status Report
        function generateReport() {
            const report = `
# LinkedIn Automation Status Report
Generated: ${new Date().toLocaleString()}

## System Overview
- Total Modules: ${memory.metrics.activeModules}
- Success Rate: ${memory.metrics.successRate}%
- API Usage: ${memory.metrics.apiUsage}%
- Questions Database: ${memory.metrics.totalQuestions} entries

## Current Session
Goal: ${memory.context.sessionGoal}

## Active Issues
${memory.context.issues}

## Next Steps
${memory.context.actionPlan}

## Notes
${memory.context.notes}
            `;

            const blob = new Blob([report], {type: 'text/markdown'});
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = `status_report_${new Date().toISOString().split('T')[0]}.md`;
            a.click();

            showNotification('Report generated!', 'success');
        }

        // Helper Functions
        function getProjectStructure() {
            return {
                modules: [
                    'main.py', 'enhanced_config.py', 'enhanced_logging.py',
                    'integration_adapter.py', 'modular_job_apply.py',
                    'network_maintenance_module.py', 'article_publishing_module.py',
                    'message_autoreply_module.py', 'web_dashboard_backend.py'
                ],
                dataFiles: [
                    'questions_log.json', 'user-profile-config.json',
                    'linkedin_config.json', 'GSBR.docx'
                ],
                totalSize: '2.3 MB'
            };
        }

        function getCriticalCodeSnippets() {
            return {
                questionMatching: `
def find_best_answer(question_text, questions_log):
    # Fuzzy matching algorithm
    best_match = None
    highest_score = 0
    
    for saved_q, saved_a in questions_log.items():
        score = fuzz.ratio(question_text.lower(), saved_q.lower())
        if score > highest_score:
            highest_score = score
            best_match = saved_a
            
    if highest_score > 80:
        return best_match
    return None
                `,
                rateLimiting: `
def check_api_usage():
    current_tokens = get_token_count()
    limit = 100000
    
    if current_tokens > limit * 0.8:
        logger.warning("Approaching API limit")
        return "warning"
    elif current_tokens >= limit:
        return "limit_reached"
    return "ok"
                `
            };
        }

        function getCurrentConfig() {
            return {
                user: '<EMAIL>',
                maxApplications: 50,
                autoReply: true,
                browser: 'chrome',
                headless: false
            };
        }

        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        function displayAnalysis(analysis) {
            console.log('Code Analysis:', analysis);
            showNotification('Code analysis complete!', 'info');
        }

        function showNotification(message, type) {
            // Simple notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                background: ${type === 'success' ? '#10b981' : '#0077b5'};
                color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                animation: slideIn 0.3s;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.remove(), 3000);
        }

        // Auto-save every 5 minutes
        setInterval(() => {
            const autoSave = {
                ...memory,
                context: {
                    sessionGoal: document.getElementById('sessionGoal').innerText,
                    issues: document.getElementById('recentIssues').innerText,
                    actionPlan: document.getElementById('actionPlan').innerText,
                    notes: document.getElementById('sessionNotes').innerText
                },
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('ai_assistant_memory', JSON.stringify(autoSave));
            console.log('Auto-saved at', new Date().toLocaleTimeString());
        }, 300000); // 5 minutes

        // Load previous session if exists
        window.onload = function() {
            const saved = localStorage.getItem('ai_assistant_memory');
            if (saved) {
                const data = JSON.parse(saved);
                document.getElementById('sessionGoal').innerText = data.context.sessionGoal || '';
                document.getElementById('recentIssues').innerText = data.context.issues || '';
                document.getElementById('actionPlan').innerText = data.context.actionPlan || '';
                document.getElementById('sessionNotes').innerText = data.context.notes || '';
                
                console.log('Loaded previous session from', data.timestamp);
            }
        };

        // Critical Information Summary for Quick Reference
        const CRITICAL_INFO = {
            resetTime: "1:00 PM daily",
            apiLimit: "100,000 tokens",
            currentUsage: "78,432 tokens",
            priorityTasks: [
                "Implement token monitoring",
                "Add session recovery",
                "Optimize question matching"
            ],
            keyFiles: {
                questions: "questions_log.json (567 Q&As)",
                config: "linkedin_config.json",
                profile: "user-profile-config.json"
            }
        };

        console.log('AI Memory System Loaded');
        console.log('Critical Info:', CRITICAL_INFO);
    </script>
</body>
</html>