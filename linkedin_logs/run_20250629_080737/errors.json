[{"timestamp": "2025-06-29T08:08:20.114378", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4256315011\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 190). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:22.209479", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4250578301\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 190). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:24.310435", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258550142\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 176). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:26.406747", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4255217879\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 178). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:42.070545", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4256704402\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 180). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:44.111667", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4257010665\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 180). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:46.211213", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258200709\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 190). Other element would receive the click: <div id=\"ember392\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:08:49.729832", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4257556764\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (173, 725). Other element would receive the click: <div data-test-modal-container=\"\" data-test-modal-id=\"easy-apply-modal\" aria-hidden=\"false\" id=\"ember387\" class=\"artdeco-modal-overlay artdeco-modal-overlay--layer-default artdeco-modal-overlay--is-top-layer  ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5841d888726a <unknown>\n#1 0x5841d8331ab0 <unknown>\n#2 0x5841d838a55c <unknown>\n#3 0x5841d83883f4 <unknown>\n#4 0x5841d8385a62 <unknown>\n#5 0x5841d8385180 <unknown>\n#6 0x5841d837790a <unknown>\n#7 0x5841d83a91a2 <unknown>\n#8 0x5841d837728a <unknown>\n#9 0x5841d83a936e <unknown>\n#10 0x5841d83cefee <unknown>\n#11 0x5841d83a8f73 <unknown>\n#12 0x5841d8375aeb <unknown>\n#13 0x5841d8376751 <unknown>\n#14 0x5841d884bb7b <unknown>\n#15 0x5841d884f959 <unknown>\n#16 0x5841d8832959 <unknown>\n#17 0x5841d8850518 <unknown>\n#18 0x5841d881710f <unknown>\n#19 0x5841d8874918 <unknown>\n#20 0x5841d8874af6 <unknown>\n#21 0x5841d8886586 <unknown>\n#22 0x749577094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:09:02.816166", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 2: HTTPConnectionPool(host='localhost', port=59217): Max retries exceeded with url: /session/dbdb225d2c9a4d9b5558b9726630098f/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x79a514755f00>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}, {"timestamp": "2025-06-29T08:09:02.818333", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 3: HTTPConnectionPool(host='localhost', port=59217): Max retries exceeded with url: /session/dbdb225d2c9a4d9b5558b9726630098f/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x79a5147560b0>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}, {"timestamp": "2025-06-29T08:09:02.832251", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 4: HTTPConnectionPool(host='localhost', port=59217): Max retries exceeded with url: /session/dbdb225d2c9a4d9b5558b9726630098f/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x79a5147134c0>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}, {"timestamp": "2025-06-29T08:09:02.833932", "run_id": "20250629_080737", "error_type": "application_error", "message": "Error processing job 5: HTTPConnectionPool(host='localhost', port=59217): Max retries exceeded with url: /session/dbdb225d2c9a4d9b5558b9726630098f/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x79a514668940>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}]