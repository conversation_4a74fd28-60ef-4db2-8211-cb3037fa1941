[{"timestamp": "2025-06-27T17:33:38.803871", "run_id": "20250627_173303", "error_type": "application_error", "message": "Search failed for 'Software Engineer': Message: \nStacktrace:\n#0 0x5712749a026a <unknown>\n#1 0x57127444aab0 <unknown>\n#2 0x57127449c6f0 <unknown>\n#3 0x57127449c8e1 <unknown>\n#4 0x5712744eab94 <unknown>\n#5 0x5712744c21cd <unknown>\n#6 0x5712744e7fee <unknown>\n#7 0x5712744c1f73 <unknown>\n#8 0x57127448eaeb <unknown>\n#9 0x57127448f751 <unknown>\n#10 0x571274964b7b <unknown>\n#11 0x571274968959 <unknown>\n#12 0x57127494b959 <unknown>\n#13 0x571274969518 <unknown>\n#14 0x57127493010f <unknown>\n#15 0x57127498d918 <unknown>\n#16 0x57127498daf6 <unknown>\n#17 0x57127499f586 <unknown>\n#18 0x7933c4e94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-27T17:33:53.751861", "run_id": "20250627_173303", "error_type": "application_error", "message": "Search failed for 'Full Stack Developer': Message: \nStacktrace:\n#0 0x5712749a026a <unknown>\n#1 0x57127444aab0 <unknown>\n#2 0x57127449c6f0 <unknown>\n#3 0x57127449c8e1 <unknown>\n#4 0x5712744eab94 <unknown>\n#5 0x5712744c21cd <unknown>\n#6 0x5712744e7fee <unknown>\n#7 0x5712744c1f73 <unknown>\n#8 0x57127448eaeb <unknown>\n#9 0x57127448f751 <unknown>\n#10 0x571274964b7b <unknown>\n#11 0x571274968959 <unknown>\n#12 0x57127494b959 <unknown>\n#13 0x571274969518 <unknown>\n#14 0x57127493010f <unknown>\n#15 0x57127498d918 <unknown>\n#16 0x57127498daf6 <unknown>\n#17 0x57127499f586 <unknown>\n#18 0x7933c4e94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-27T17:34:08.845361", "run_id": "20250627_173303", "error_type": "application_error", "message": "Search failed for '.NET Developer': Message: \nStacktrace:\n#0 0x5712749a026a <unknown>\n#1 0x57127444aab0 <unknown>\n#2 0x57127449c6f0 <unknown>\n#3 0x57127449c8e1 <unknown>\n#4 0x5712744eab94 <unknown>\n#5 0x5712744c21cd <unknown>\n#6 0x5712744e7fee <unknown>\n#7 0x5712744c1f73 <unknown>\n#8 0x57127448eaeb <unknown>\n#9 0x57127448f751 <unknown>\n#10 0x571274964b7b <unknown>\n#11 0x571274968959 <unknown>\n#12 0x57127494b959 <unknown>\n#13 0x571274969518 <unknown>\n#14 0x57127493010f <unknown>\n#15 0x57127498d918 <unknown>\n#16 0x57127498daf6 <unknown>\n#17 0x57127499f586 <unknown>\n#18 0x7933c4e94ac3 <unknown>\n", "context": {}}]