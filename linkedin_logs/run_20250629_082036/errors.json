[{"timestamp": "2025-06-29T08:21:21.554205", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4252932103\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 180). Other element would receive the click: <div id=\"ember378\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:23.657281", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4250019819\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 168). Other element would receive the click: <div id=\"ember378\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:25.758075", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4256530632\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 186). Other element would receive the click: <div id=\"ember378\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:27.800882", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4258550142\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 176). Other element would receive the click: <h3 class=\"t-12 t-bold jobs-document-upload-redesign-card__file-name\n            \n            truncate\">...</h3>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:43.399255", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4257010665\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 180). Other element would receive the click: <div id=\"ember381\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:45.442377", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4258200709\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 190). Other element would receive the click: <div id=\"ember381\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:47.546257", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4259089643\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 178). Other element would receive the click: <div id=\"ember381\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:21:49.595027", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4258259355\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 178). Other element would receive the click: <div id=\"ember381\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:22:05.663465", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4259089643\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 59). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:22:07.747887", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4256704402\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 61). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:22:09.838396", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4244206424\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 61). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T08:22:11.922843", "run_id": "20250629_082036", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4255553322\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 59). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5d03c4ecc26a <unknown>\n#1 0x5d03c4976ab0 <unknown>\n#2 0x5d03c49cf55c <unknown>\n#3 0x5d03c49cd3f4 <unknown>\n#4 0x5d03c49caa62 <unknown>\n#5 0x5d03c49ca180 <unknown>\n#6 0x5d03c49bc90a <unknown>\n#7 0x5d03c49ee1a2 <unknown>\n#8 0x5d03c49bc28a <unknown>\n#9 0x5d03c49ee36e <unknown>\n#10 0x5d03c4a13fee <unknown>\n#11 0x5d03c49edf73 <unknown>\n#12 0x5d03c49baaeb <unknown>\n#13 0x5d03c49bb751 <unknown>\n#14 0x5d03c4e90b7b <unknown>\n#15 0x5d03c4e94959 <unknown>\n#16 0x5d03c4e77959 <unknown>\n#17 0x5d03c4e95518 <unknown>\n#18 0x5d03c4e5c10f <unknown>\n#19 0x5d03c4eb9918 <unknown>\n#20 0x5d03c4eb9af6 <unknown>\n#21 0x5d03c4ecb586 <unknown>\n#22 0x7c3318a94ac3 <unknown>\n", "context": {}}]