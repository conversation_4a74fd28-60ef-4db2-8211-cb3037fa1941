[{"timestamp": "2025-06-28T22:48:14.717506", "run_id": "20250628_224654", "error_type": "application_error", "message": "Error processing job 4: HTTPConnectionPool(host='localhost', port=48333): Max retries exceeded with url: /session/8ea561a36f95fc13b2c6c41d78b37889/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x724ed7668eb0>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}, {"timestamp": "2025-06-28T22:48:14.719111", "run_id": "20250628_224654", "error_type": "application_error", "message": "Error processing job 5: HTTPConnectionPool(host='localhost', port=48333): Max retries exceeded with url: /session/8ea561a36f95fc13b2c6c41d78b37889/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x724ed7669f30>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}, {"timestamp": "2025-06-28T22:48:14.721262", "run_id": "20250628_224654", "error_type": "application_error", "message": "Search failed for '.NET Developer': HTTPConnectionPool(host='localhost', port=48333): Max retries exceeded with url: /session/8ea561a36f95fc13b2c6c41d78b37889/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x724ed766a920>: Failed to establish a new connection: [Errno 111] Connection refused'))", "context": {}}]