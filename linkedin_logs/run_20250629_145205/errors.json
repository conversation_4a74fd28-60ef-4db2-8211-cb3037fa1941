[{"timestamp": "2025-06-29T14:52:48.064190", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4255217879\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 178). Other element would receive the click: <div id=\"ember395\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:52:50.161785", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4255569652\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 188). Other element would receive the click: <div id=\"ember395\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:52:52.267020", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4250803364\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 192). Other element would receive the click: <div id=\"ember395\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:52:54.320813", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4258550142\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 176). Other element would receive the click: <div id=\"ember395\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:10.271649", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4258200709\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 190). Other element would receive the click: <div id=\"ember327\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:12.366874", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4259208267\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 196). Other element would receive the click: <div id=\"ember327\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:14.469447", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4257882712\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 168). Other element would receive the click: <div id=\"ember327\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:16.565908", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4258500734\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (267, 190). Other element would receive the click: <div id=\"ember327\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:37.062447", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4256704402\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (248, 180). Other element would receive the click: <div id=\"ember439\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:39.164398", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258500734\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (248, 198). Other element would receive the click: <div id=\"ember439\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T14:53:41.209936", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4256049833\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (248, 186). Other element would receive the click: <div id=\"ember439\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5e21348f826a <unknown>\n#1 0x5e21343a2ab0 <unknown>\n#2 0x5e21343fb55c <unknown>\n#3 0x5e21343f93f4 <unknown>\n#4 0x5e21343f6a62 <unknown>\n#5 0x5e21343f6180 <unknown>\n#6 0x5e21343e890a <unknown>\n#7 0x5e213441a1a2 <unknown>\n#8 0x5e21343e828a <unknown>\n#9 0x5e213441a36e <unknown>\n#10 0x5e213443ffee <unknown>\n#11 0x5e2134419f73 <unknown>\n#12 0x5e21343e6aeb <unknown>\n#13 0x5e21343e7751 <unknown>\n#14 0x5e21348bcb7b <unknown>\n#15 0x5e21348c0959 <unknown>\n#16 0x5e21348a3959 <unknown>\n#17 0x5e21348c1518 <unknown>\n#18 0x5e213488810f <unknown>\n#19 0x5e21348e5918 <unknown>\n#20 0x5e21348e5af6 <unknown>\n#21 0x5e21348f7586 <unknown>\n#22 0x721b48094ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T15:15:28.064995", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4252586516\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 206). Other element would receive the click: <div id=\"ember380\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5c74584d926a <unknown>\n#1 0x5c7457f83ab0 <unknown>\n#2 0x5c7457fdc55c <unknown>\n#3 0x5c7457fda3f4 <unknown>\n#4 0x5c7457fd7a62 <unknown>\n#5 0x5c7457fd7180 <unknown>\n#6 0x5c7457fc990a <unknown>\n#7 0x5c7457ffb1a2 <unknown>\n#8 0x5c7457fc928a <unknown>\n#9 0x5c7457ffb36e <unknown>\n#10 0x5c7458020fee <unknown>\n#11 0x5c7457ffaf73 <unknown>\n#12 0x5c7457fc7aeb <unknown>\n#13 0x5c7457fc8751 <unknown>\n#14 0x5c745849db7b <unknown>\n#15 0x5c74584a1959 <unknown>\n#16 0x5c7458484959 <unknown>\n#17 0x5c74584a2518 <unknown>\n#18 0x5c745846910f <unknown>\n#19 0x5c74584c6918 <unknown>\n#20 0x5c74584c6af6 <unknown>\n#21 0x5c74584d8586 <unknown>\n#22 0x76fdfce94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T15:15:30.157321", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4253185885\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 208). Other element would receive the click: <div id=\"ember380\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5c74584d926a <unknown>\n#1 0x5c7457f83ab0 <unknown>\n#2 0x5c7457fdc55c <unknown>\n#3 0x5c7457fda3f4 <unknown>\n#4 0x5c7457fd7a62 <unknown>\n#5 0x5c7457fd7180 <unknown>\n#6 0x5c7457fc990a <unknown>\n#7 0x5c7457ffb1a2 <unknown>\n#8 0x5c7457fc928a <unknown>\n#9 0x5c7457ffb36e <unknown>\n#10 0x5c7458020fee <unknown>\n#11 0x5c7457ffaf73 <unknown>\n#12 0x5c7457fc7aeb <unknown>\n#13 0x5c7457fc8751 <unknown>\n#14 0x5c745849db7b <unknown>\n#15 0x5c74584a1959 <unknown>\n#16 0x5c7458484959 <unknown>\n#17 0x5c74584a2518 <unknown>\n#18 0x5c745846910f <unknown>\n#19 0x5c74584c6918 <unknown>\n#20 0x5c74584c6af6 <unknown>\n#21 0x5c74584d8586 <unknown>\n#22 0x76fdfce94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T15:15:32.260163", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258550142\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 192). Other element would receive the click: <div id=\"ember380\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5c74584d926a <unknown>\n#1 0x5c7457f83ab0 <unknown>\n#2 0x5c7457fdc55c <unknown>\n#3 0x5c7457fda3f4 <unknown>\n#4 0x5c7457fd7a62 <unknown>\n#5 0x5c7457fd7180 <unknown>\n#6 0x5c7457fc990a <unknown>\n#7 0x5c7457ffb1a2 <unknown>\n#8 0x5c7457fc928a <unknown>\n#9 0x5c7457ffb36e <unknown>\n#10 0x5c7458020fee <unknown>\n#11 0x5c7457ffaf73 <unknown>\n#12 0x5c7457fc7aeb <unknown>\n#13 0x5c7457fc8751 <unknown>\n#14 0x5c745849db7b <unknown>\n#15 0x5c74584a1959 <unknown>\n#16 0x5c7458484959 <unknown>\n#17 0x5c74584a2518 <unknown>\n#18 0x5c745846910f <unknown>\n#19 0x5c74584c6918 <unknown>\n#20 0x5c74584c6af6 <unknown>\n#21 0x5c74584d8586 <unknown>\n#22 0x76fdfce94ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T15:15:34.327052", "run_id": "20250629_145205", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4255569652\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (259, 204). Other element would receive the click: <div id=\"ember380\" class=\"mt3 flex-wrap artdeco-entity-lockup artdeco-entity-lockup--size-4 ember-view\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x5c74584d926a <unknown>\n#1 0x5c7457f83ab0 <unknown>\n#2 0x5c7457fdc55c <unknown>\n#3 0x5c7457fda3f4 <unknown>\n#4 0x5c7457fd7a62 <unknown>\n#5 0x5c7457fd7180 <unknown>\n#6 0x5c7457fc990a <unknown>\n#7 0x5c7457ffb1a2 <unknown>\n#8 0x5c7457fc928a <unknown>\n#9 0x5c7457ffb36e <unknown>\n#10 0x5c7458020fee <unknown>\n#11 0x5c7457ffaf73 <unknown>\n#12 0x5c7457fc7aeb <unknown>\n#13 0x5c7457fc8751 <unknown>\n#14 0x5c745849db7b <unknown>\n#15 0x5c74584a1959 <unknown>\n#16 0x5c7458484959 <unknown>\n#17 0x5c74584a2518 <unknown>\n#18 0x5c745846910f <unknown>\n#19 0x5c74584c6918 <unknown>\n#20 0x5c74584c6af6 <unknown>\n#21 0x5c74584d8586 <unknown>\n#22 0x76fdfce94ac3 <unknown>\n", "context": {}}]