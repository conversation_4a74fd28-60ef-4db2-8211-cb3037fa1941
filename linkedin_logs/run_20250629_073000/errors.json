[{"timestamp": "2025-06-29T07:30:48.255090", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 1: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:30:52.471301", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 2: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:30:56.750064", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 3: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:31:00.991318", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 4: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:31:05.198429", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 5: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:31:18.277728", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 1: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:31:20.358886", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4257010665\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 61). Other element would receive the click: <a class=\"wsMvqdsCQwHMdXuvCLlSAkmVYButhGSUc \" target=\"_self\" tabindex=\"0\" href=\"https://www.linkedin.com/company/vanguard-devops-llc/life\" data-test-app-aware-link=\"\">...</a>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:22.445275", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4256704402\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 61). Other element would receive the click: <a class=\"wsMvqdsCQwHMdXuvCLlSAkmVYButhGSUc \" target=\"_self\" tabindex=\"0\" href=\"https://www.linkedin.com/company/vanguard-devops-llc/life\" data-test-app-aware-link=\"\">...</a>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:24.497638", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258200709\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 71). Other element would receive the click: <a class=\"wsMvqdsCQwHMdXuvCLlSAkmVYButhGSUc \" target=\"_self\" tabindex=\"0\" href=\"https://www.linkedin.com/company/vanguard-devops-llc/life\" data-test-app-aware-link=\"\">...</a>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:26.599239", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4256294697\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 49). Other element would receive the click: <div class=\"jobs-search__job-details--wrapper\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:39.052341", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 1: 'ModularJobApplicator' object has no attribute 'find_apply_button'", "context": {}}, {"timestamp": "2025-06-29T07:31:41.129856", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 2: Message: element click intercepted: Element <div data-job-id=\"4256704402\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-1\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 61). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:43.207293", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 3: Message: element click intercepted: Element <div data-job-id=\"4257039467\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-2\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 59). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:45.288170", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 4: Message: element click intercepted: Element <div data-job-id=\"4258096665\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-3\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 59). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}, {"timestamp": "2025-06-29T07:31:47.369442", "run_id": "20250629_073000", "error_type": "application_error", "message": "Error processing job 5: Message: element click intercepted: Element <div data-job-id=\"4255553322\" class=\"display-flex job-card-container relative job-card-list\n        job-card-container--clickable\n        \n        job-card-list--underline-title-on-hover  jobs-search-two-pane__job-card-container--viewport-tracking-4\" data-view-name=\"job-card\">...</div> is not clickable at point (165, 59). Other element would receive the click: <div class=\"display-flex align-items-center flex-1\">...</div>\n  (Session info: chrome=138.0.7204.49)\nStacktrace:\n#0 0x58311614d26a <unknown>\n#1 0x583115bf7ab0 <unknown>\n#2 0x583115c5055c <unknown>\n#3 0x583115c4e3f4 <unknown>\n#4 0x583115c4ba62 <unknown>\n#5 0x583115c4b180 <unknown>\n#6 0x583115c3d90a <unknown>\n#7 0x583115c6f1a2 <unknown>\n#8 0x583115c3d28a <unknown>\n#9 0x583115c6f36e <unknown>\n#10 0x583115c94fee <unknown>\n#11 0x583115c6ef73 <unknown>\n#12 0x583115c3baeb <unknown>\n#13 0x583115c3c751 <unknown>\n#14 0x583116111b7b <unknown>\n#15 0x583116115959 <unknown>\n#16 0x5831160f8959 <unknown>\n#17 0x583116116518 <unknown>\n#18 0x5831160dd10f <unknown>\n#19 0x58311613a918 <unknown>\n#20 0x58311613aaf6 <unknown>\n#21 0x58311614c586 <unknown>\n#22 0x7e1afa494ac3 <unknown>\n", "context": {}}]