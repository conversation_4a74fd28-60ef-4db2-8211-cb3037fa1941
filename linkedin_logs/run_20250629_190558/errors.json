[{"timestamp": "2025-06-29T19:08:17.771186", "run_id": "20250629_190558", "error_type": "job_application_error", "message": "unmatched ')' (modular_job_apply.py, line 203)", "context": {}}, {"timestamp": "2025-06-29T19:11:18.170105", "run_id": "20250629_190558", "error_type": "job_application_error", "message": "'ModularJobApplicator' object has no attribute 'run'", "context": {}}, {"timestamp": "2025-06-29T19:13:01.723644", "run_id": "20250629_190558", "error_type": "job_application_error", "message": "'ModularJobApplicator' object has no attribute 'run'", "context": {}}, {"timestamp": "2025-06-29T19:13:04.398631", "run_id": "20250629_190558", "error_type": "job_application_error", "message": "'ModularJobApplicator' object has no attribute 'run'", "context": {}}, {"timestamp": "2025-06-29T19:13:18.086658", "run_id": "20250629_190558", "error_type": "job_application_error", "message": "'ModularJobApplicator' object has no attribute 'run'", "context": {}}]