#!/bin/bash
# LinkedIn Jobs - Vault Setup Helper

echo "=============================================="
echo " LinkedIn Jobs Automation - Vault Setup"
echo "=============================================="
echo ""

VAULT_DIR="/home/<USER>/Documents/Source/secrets-vault"

if [ ! -d "$VAULT_DIR" ]; then
    echo "❌ Vault directory not found at: $VAULT_DIR"
    exit 1
fi

echo "📁 Vault found at: $VAULT_DIR"
echo ""

# Check if vault is initialized
cd "$VAULT_DIR"

echo "🔐 Adding LinkedIn secrets to vault..."
echo ""
echo "This will prompt you for:"
echo "  1. Vault master password (to unlock)"
echo "  2. LinkedIn email"
echo "  3. LinkedIn password"
echo "  4. GitHub SSH key location (optional)"
echo ""

# Add LinkedIn Email
echo "--- Adding LINKEDIN_EMAIL ---"
read -p "Enter your LinkedIn email: " linkedin_email
./secrets-vault.py add LINKEDIN_EMAIL "$linkedin_email" --category LINKEDIN

# Add LinkedIn Password
echo ""
echo "--- Adding LINKEDIN_PASSWORD ---"
read -sp "Enter your LinkedIn password: " linkedin_password
echo ""
./secrets-vault.py add LINKEDIN_PASSWORD "$linkedin_password" --category LINKEDIN

# Add GitHub SSH Key (optional)
echo ""
echo "--- Adding GITHUB_SSH_KEY (optional) ---"
read -p "Add GitHub SSH key? (y/n): " add_ssh
if [ "$add_ssh" = "y" ] || [ "$add_ssh" = "Y" ]; then
    SSH_KEY_FILE="$HOME/.ssh/id_ed25519"
    if [ -f "$SSH_KEY_FILE" ]; then
        echo "Found SSH key at: $SSH_KEY_FILE"
        SSH_KEY_CONTENT=$(cat "$SSH_KEY_FILE")
        echo "$SSH_KEY_CONTENT" | ./secrets-vault.py add GITHUB_SSH_KEY --category GITHUB --stdin
        echo "✅ SSH key added"
    else
        echo "⚠️  SSH key not found at $SSH_KEY_FILE"
    fi
fi

echo ""
echo "=============================================="
echo "✅ Vault Setup Complete!"
echo "=============================================="
echo ""
echo "📋 Verify your setup:"
echo "   cd $VAULT_DIR"
echo "   ./secrets-vault.py list --category LINKEDIN"
echo ""
echo "🚀 Test LinkedIn integration:"
echo "   cd /home/<USER>/Documents/Source/lin/linkedin-jobs"
echo "   python3 vault_setup.py"
echo ""
