#!/usr/bin/env python3
"""
Setup script to ensure dashboard works properly
"""
import os
import sys
import shutil

def setup_dashboard():
    """Setup dashboard files and dependencies"""
    print("🔧 Setting up LinkedIn Automation Dashboard...")
    
    # 1. Check for required files
    required_files = {
        'main.py': 'Main entry point',
        'linkedin_config.json': 'Configuration file',
        'web_dashboard_module.html': 'Dashboard UI'
    }
    
    missing_files = []
    for filename, description in required_files.items():
        if os.path.exists(filename):
            print(f"✅ Found {filename} - {description}")
        else:
            print(f"❌ Missing {filename} - {description}")
            missing_files.append(filename)
    
    # 2. Create templates directory if needed
    if not os.path.exists('templates'):
        os.makedirs('templates')
        print("📁 Created templates directory")
        
    # 3. Copy dashboard HTML to templates
    if os.path.exists('web_dashboard_module.html'):
        shutil.copy('web_dashboard_module.html', 'templates/dashboard.html')
        print("📄 Copied dashboard HTML to templates")
    
    # 4. Check Python dependencies
    print("\n📦 Checking Python dependencies...")
    required_packages = ['fastapi', 'uvicorn', 'selenium']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is not installed")
            print(f"   Run: pip install {package}")
    
    # 5. Create start script
    start_script = '''#!/usr/bin/env python3
"""Quick start script for dashboard"""
import subprocess
import sys
import time
import webbrowser

print("🚀 Starting LinkedIn Automation Dashboard...")
time.sleep(1)
webbrowser.open('http://localhost:8000/dashboard')
subprocess.run([sys.executable, 'main.py'])
'''
    
    with open('start_dashboard.py', 'w') as f:
        f.write(start_script)
    os.chmod('start_dashboard.py', 0o755)
    print("\n✅ Created start_dashboard.py script")
    
    # 6. Instructions
    print("\n" + "="*50)
    print("Dashboard Setup Complete!")
    print("="*50)
    print("\nTo start the dashboard:")
    print("1. Regular start: python main.py")
    print("2. Quick start: python start_dashboard.py")
    print("3. CLI mode: python main.py --cli")
    print("\nDashboard will open at: http://localhost:8000/dashboard")
    print("="*50)

if __name__ == "__main__":
    setup_dashboard()