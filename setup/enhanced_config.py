#!/usr/bin/env python3
"""
Enhanced Configuration Management for LinkedIn Automation
Centralizes all configurable values in JSON
"""
import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field, asdict
from pathlib import Path

@dataclass
class DelayConfig:
    """All delay/timing configurations"""
    page_load: float = 10.0
    between_applications: tuple = (2, 5)  # min, max seconds
    form_fill_delay: float = 0.5
    click_delay: float = 0.3
    typing_delay: float = 0.1  # delay between keystrokes
    network_action_delay: tuple = (1, 3)
    scroll_delay: float = 0.5
    modal_wait: float = 2.0
    
@dataclass
class PathConfig:
    """All file path configurations"""
    resume_path: str = ""
    cover_letter_template: str = ""
    log_directory: str = "linkedin_logs"
    screenshot_directory: str = "screenshots"
    data_directory: str = "data"
    
@dataclass
class SearchConfig:
    """Job search configurations"""
    keywords: List[str] = field(default_factory=lambda: [
        "web developer", ".NET Developer", "C# Developer", 
        "Full Stack Developer", "Software Engineer"
    ])
    locations: List[str] = field(default_factory=lambda: ["Remote", "United States"])
    experience_levels: List[str] = field(default_factory=lambda: [
        "Entry level", "Associate", "Mid-Senior level"
    ])
    job_types: List[str] = field(default_factory=lambda: [
        "Full-time", "Contract", "Part-time"
    ])
    date_posted: str = "Past month"  # "Past 24 hours", "Past week", "Past month"
    easy_apply_only: bool = True
    
@dataclass
class NetworkConfig:
    """Network expansion configurations"""
    max_connections_per_run: int = 50
    max_invitations_accept: int = 100
    connection_message_templates: List[str] = field(default_factory=lambda: [
        "Hi {name}, I'd love to connect and expand our professional networks!",
        "Hello {name}, I came across your profile and would like to connect.",
    ])
    birthday_message_template: str = "Happy Birthday {name}! Wishing you a fantastic day!"
    congrats_message_template: str = "Congratulations on your new position, {name}!"
    
@dataclass
class AutoReplyConfig:
    """Message auto-reply configurations"""
    enabled: bool = False
    check_interval_minutes: int = 30
    reply_templates: Dict[str, str] = field(default_factory=lambda: {
        "job_inquiry": "Thank you for reaching out about this opportunity. Could you please share the job description so I can provide you with a tailored resume?",
        "default": "Thank you for your message. I'll get back to you soon.",
    })
    keywords_job_inquiry: List[str] = field(default_factory=lambda: [
        "opportunity", "position", "role", "job", "opening", "hiring"
    ])
    
@dataclass
class AIConfig:
    """AI provider configurations"""
    openai_api_key: str = ""
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 500
    use_ai_for_cover_letters: bool = True
    use_ai_for_form_fields: bool = True
    
@dataclass
class BrowserConfig:
    """Browser configurations"""
    browser_type: str = "chrome"  # chrome, edge, firefox
    headless: bool = False
    window_size: tuple = (1920, 1080)
    user_agent: Optional[str] = None
    chrome_binary_path: str = "/usr/bin/chromium-browser"
    chromedriver_path: str = "/usr/bin/chromedriver"
    disable_images: bool = False
    disable_javascript: bool = False
    
@dataclass
class ApplicationConfig:
    """Application behavior configurations"""
    auto_use_defaults: bool = False
    skip_applied_jobs: bool = True
    max_applications_per_run: int = 100
    save_job_descriptions: bool = True
    take_screenshots_on_error: bool = True
    pause_on_captcha: bool = True
    
@dataclass
class LinkedInConfig:
    """Main configuration class"""
    username: str = ""
    password: str = ""
    delays: DelayConfig = field(default_factory=DelayConfig)
    paths: PathConfig = field(default_factory=PathConfig)
    search: SearchConfig = field(default_factory=SearchConfig)
    network: NetworkConfig = field(default_factory=NetworkConfig)
    auto_reply: AutoReplyConfig = field(default_factory=AutoReplyConfig)
    ai: AIConfig = field(default_factory=AIConfig)
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    application: ApplicationConfig = field(default_factory=ApplicationConfig)
    
    @classmethod
    def load(cls, config_file: str = "linkedin_config.json") -> 'LinkedInConfig':
        """Load configuration from JSON file"""
        if not os.path.exists(config_file):
            # Create default config
            config = cls()
            config.save(config_file)
            return config
            
        with open(config_file, 'r') as f:
            data = json.load(f)
            
        # Convert nested dictionaries to dataclass instances
        if 'delays' in data and isinstance(data['delays'], dict):
            data['delays'] = DelayConfig(**data['delays'])
        if 'paths' in data and isinstance(data['paths'], dict):
            data['paths'] = PathConfig(**data['paths'])
        if 'search' in data and isinstance(data['search'], dict):
            data['search'] = SearchConfig(**data['search'])
        if 'network' in data and isinstance(data['network'], dict):
            data['network'] = NetworkConfig(**data['network'])
        if 'auto_reply' in data and isinstance(data['auto_reply'], dict):
            data['auto_reply'] = AutoReplyConfig(**data['auto_reply'])
        if 'ai' in data and isinstance(data['ai'], dict):
            data['ai'] = AIConfig(**data['ai'])
        if 'browser' in data and isinstance(data['browser'], dict):
            data['browser'] = BrowserConfig(**data['browser'])
        if 'application' in data and isinstance(data['application'], dict):
            data['application'] = ApplicationConfig(**data['application'])
            
        return cls(**data)
        
    def save(self, config_file: str = "linkedin_config.json"):
        """Save configuration to JSON file"""
        # Convert to dictionary with nested dataclasses
        data = {}
        for key, value in asdict(self).items():
            if hasattr(value, '__dict__') and not isinstance(value, (str, int, float, bool, list, dict)):
                data[key] = asdict(value)
            else:
                data[key] = value
                
        with open(config_file, 'w') as f:
            json.dump(data, f, indent=2)
            
    def get_delay(self, delay_type: str) -> float:
        """Get a specific delay value"""
        return getattr(self.delays, delay_type, 1.0)
        
    def get_random_delay(self, delay_type: str) -> float:
        """Get a random delay within range"""
        import random
        delay = getattr(self.delays, delay_type, (1, 3))
        if isinstance(delay, tuple):
            return random.uniform(delay[0], delay[1])
        return delay
        
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        if not self.username:
            issues.append("LinkedIn username is not set")
        if not self.password:
            issues.append("LinkedIn password is not set")
        if self.paths.resume_path and not os.path.exists(self.paths.resume_path):
            issues.append(f"Resume file not found: {self.paths.resume_path}")
        if self.ai.use_ai_for_cover_letters and not self.ai.openai_api_key:
            issues.append("OpenAI API key required for AI cover letters")
        if not self.search.keywords:
            issues.append("No search keywords configured")
            
        return issues
        
    def create_directories(self):
        """Create all configured directories"""
        for path_attr in ['log_directory', 'screenshot_directory', 'data_directory']:
            path = getattr(self.paths, path_attr)
            if path:
                Path(path).mkdir(parents=True, exist_ok=True)


class ConfigManager:
    """Helper class for managing configuration"""
    
    def __init__(self, config_file: str = "linkedin_config.json"):
        self.config_file = config_file
        self.config = LinkedInConfig.load(config_file)
        
    def update_credentials(self, username: str, password: str):
        """Update login credentials"""
        self.config.username = username
        self.config.password = password
        self.save()
        
    def add_search_keyword(self, keyword: str):
        """Add a search keyword"""
        if keyword not in self.config.search.keywords:
            self.config.search.keywords.append(keyword)
            self.save()
            
    def remove_search_keyword(self, keyword: str):
        """Remove a search keyword"""
        if keyword in self.config.search.keywords:
            self.config.search.keywords.remove(keyword)
            self.save()
            
    def update_ai_key(self, api_key: str):
        """Update OpenAI API key"""
        self.config.ai.openai_api_key = api_key
        self.save()
        
    def save(self):
        """Save current configuration"""
        self.config.save(self.config_file)
        
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        return {
            "username": self.config.username,
            "has_password": bool(self.config.password),
            "has_api_key": bool(self.config.ai.openai_api_key),
            "search_keywords": self.config.search.keywords,
            "easy_apply_only": self.config.search.easy_apply_only,
            "auto_reply_enabled": self.config.auto_reply.enabled,
            "browser": self.config.browser.browser_type,
            "max_applications": self.config.application.max_applications_per_run
        }
        
    def export_config(self, export_file: str):
        """Export configuration (without sensitive data)"""
        data = asdict(self.config)
        # Remove sensitive data
        data['password'] = ""
        data['ai']['openai_api_key'] = ""
        
        with open(export_file, 'w') as f:
            json.dump(data, f, indent=2)
            
    def import_config(self, import_file: str, preserve_credentials: bool = True):
        """Import configuration from file"""
        # Save current credentials if needed
        current_username = self.config.username
        current_password = self.config.password
        current_api_key = self.config.ai.openai_api_key
        
        # Load new config
        with open(import_file, 'r') as f:
            data = json.load(f)
            
        self.config = LinkedInConfig.load(import_file)
        
        # Restore credentials if needed
        if preserve_credentials:
            self.config.username = current_username
            self.config.password = current_password
            self.config.ai.openai_api_key = current_api_key
            
        self.save()


# Example usage and default config creation
if __name__ == "__main__":
    # Create default configuration file
    config = LinkedInConfig()
    config.save("linkedin_config_default.json")
    print("Default configuration saved to linkedin_config_default.json")
    
    # Example of loading and modifying config
    manager = ConfigManager()
    print("\nConfiguration Summary:")
    print(json.dumps(manager.get_config_summary(), indent=2))