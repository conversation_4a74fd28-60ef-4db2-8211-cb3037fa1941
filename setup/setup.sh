#!/bin/bash
# LinkedIn Jobs - First Time Setup

echo "=============================================="
echo " LinkedIn Jobs Automation - First Time Setup"
echo "=============================================="
echo ""

# Check if config files exist
CONFIG_EXISTS=false
PROFILE_EXISTS=false

if [ -f "linkedin_config.json" ]; then
    CONFIG_EXISTS=true
fi

if [ -f "user-profile-config.json" ]; then
    PROFILE_EXISTS=true
fi

# Copy template files if they don't exist
if [ "$CONFIG_EXISTS" = false ]; then
    echo "📄 Creating linkedin_config.json from template..."
    cp linkedin_config.example.json linkedin_config.json
    echo "✅ Created linkedin_config.json"
else
    echo "ℹ️  linkedin_config.json already exists (skipping)"
fi

if [ "$PROFILE_EXISTS" = false ]; then
    echo "📄 Creating user-profile-config.json from template..."
    cp user-profile-config.example.json user-profile-config.json
    echo "✅ Created user-profile-config.json"
else
    echo "ℹ️  user-profile-config.json already exists (skipping)"
fi

echo ""
echo "=============================================="
echo " Configuration Files Setup"
echo "=============================================="
echo ""

if [ "$CONFIG_EXISTS" = false ] || [ "$PROFILE_EXISTS" = false ]; then
    echo "📝 Next Steps:"
    echo ""
    echo "1. Edit configuration files:"
    if [ "$CONFIG_EXISTS" = false ]; then
        echo "   - linkedin_config.json (browser settings, resume path)"
    fi
    if [ "$PROFILE_EXISTS" = false ]; then
        echo "   - user-profile-config.json (your profile, job preferences)"
    fi
    echo ""
fi

echo "2. Add secrets to vault (REQUIRED):"
echo "   ./setup_vault_secrets.sh"
echo ""
echo "3. Verify setup:"
echo "   python3 vault_setup.py"
echo ""
echo "4. Run the application:"
echo "   python3 main.py"
echo ""

echo "=============================================="
echo "🔐 Security Reminder"
echo "=============================================="
echo ""
echo "✅ DO: Store passwords and API keys in the vault"
echo "❌ DON'T: Put passwords in config files"
echo "📖 Guide: See docs/VAULT_QUICKSTART.md for details"
echo ""
