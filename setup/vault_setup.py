#!/usr/bin/env python3
"""
LinkedIn Jobs Automation - Secrets Vault Integration

Auto-loads LinkedIn credentials, SSH keys, and GitHub tokens from the centralized vault.
"""

import os
import sys
from pathlib import Path

# ===== SECRETS VAULT INTEGRATION =====
# Default: assume vault unavailable until proven otherwise
VAULT_AVAILABLE = False
vault = None

try:
    # Add vault to path
    vault_path = Path(__file__).parent.parent.parent / 'secrets-vault'
    if str(vault_path) not in sys.path:
        sys.path.insert(0, str(vault_path))

    # Try regular import first (this may fail in static analysis tools)
    try:
        from vault_integration import VaultSecrets  # type: ignore
    except Exception:
        # Attempt to dynamically load vault_integration.py if present in expected vault_path
        VaultSecrets = None
        vault_file = vault_path / "vault_integration.py"
        if vault_file.exists():
            import importlib.util
            spec = importlib.util.spec_from_file_location("vault_integration", str(vault_file))
            if spec is not None and spec.loader is not None:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)  # type: ignore
                VaultSecrets = getattr(module, "VaultSecrets", None)

    if not VaultSecrets:
        # No vault integration found -> raise to trigger environment fallback below
        raise ImportError("vault_integration module not found in secrets-vault")

    # Initialize vault and load secrets
    vault = VaultSecrets()

    # LinkedIn Credentials
    LINKEDIN_EMAIL = vault.get('LINKEDIN_EMAIL', 'LINKEDIN')
    LINKEDIN_PASSWORD = vault.get('LINKEDIN_PASSWORD', 'LINKEDIN')

    # GitHub Configuration
    GITHUB_TOKEN = vault.get('GITHUB_TOKEN', 'GITHUB')
    GITHUB_SSH_KEY = vault.get('GITHUB_SSH_KEY', 'GITHUB')

    # OpenAI/AI Services
    OPENAI_API_KEY = vault.get('OPENAI_API_KEY', 'AI')

    # Other API Keys
    QDRANT_API_KEY = vault.get('QDRANT_API_KEY', 'AI')
    ANTHROPIC_API_KEY = vault.get('ANTHROPIC_API_KEY', 'AI')

    VAULT_AVAILABLE = True
    print("🔐 Loaded LinkedIn automation secrets from vault")

    # Validate critical secrets
    if LINKEDIN_EMAIL and LINKEDIN_PASSWORD:
        print(f"✅ LinkedIn credentials loaded for: {LINKEDIN_EMAIL}")
    else:
        print("⚠️  Missing LinkedIn credentials")

    if GITHUB_TOKEN:
        print("✅ GitHub token loaded")
    else:
        print("⚠️  Missing GitHub token")

    if OPENAI_API_KEY:
        print("✅ OpenAI API key loaded")
    else:
        print("⚠️  Missing OpenAI API key")

except Exception as e:
    # Any import/initialization error falls back to environment variables
    print(f"⚠️  Vault not available: {e}")
    print("💡 Using environment variables as fallback")
    VAULT_AVAILABLE = False

    # Fallback to environment variables
    LINKEDIN_EMAIL = os.getenv('LINKEDIN_EMAIL')
    LINKEDIN_PASSWORD = os.getenv('LINKEDIN_PASSWORD')
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
    GITHUB_SSH_KEY = os.getenv('GITHUB_SSH_KEY')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    QDRANT_API_KEY = os.getenv('QDRANT_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')


class LinkedInVaultConfig:
    """Configuration manager for LinkedIn automation with vault integration"""
    
    @property
    def linkedin_credentials(self):
        """Get LinkedIn login credentials"""
        return {
            'email': LINKEDIN_EMAIL,
            'password': LINKEDIN_PASSWORD
        }
    
    @property
    def github_credentials(self):
        """Get GitHub credentials"""
        return {
            'token': GITHUB_TOKEN,
            'ssh_key': GITHUB_SSH_KEY
        }
    
    @property
    def ai_credentials(self):
        """Get AI service credentials"""
        return {
            'openai': OPENAI_API_KEY,
            'qdrant': QDRANT_API_KEY,
            'anthropic': ANTHROPIC_API_KEY
        }
    
    @property
    def is_configured(self):
        """Check if all required credentials are available"""
        linkedin_ok = bool(LINKEDIN_EMAIL and LINKEDIN_PASSWORD)
        return linkedin_ok
    
    def validate_setup(self):
        """Validate and print setup status"""
        if VAULT_AVAILABLE:
            print("🔐 Using Secrets Vault for credential management")
        else:
            print("📋 Using environment variables for credentials")
            
        if self.is_configured:
            print("✅ LinkedIn automation fully configured")
        else:
            missing = []
            if not (LINKEDIN_EMAIL and LINKEDIN_PASSWORD):
                missing.append("LinkedIn credentials")
            
            print(f"❌ Missing: {', '.join(missing)}")
            
            if not VAULT_AVAILABLE:
                print("\n💡 To set up Secrets Vault:")
                print("   cd /home/<USER>/Documents/Source/secrets-vault")
                print("   python3 secrets-vault.py add LINKEDIN_EMAIL '<EMAIL>' --category LINKEDIN")
                print("   python3 secrets-vault.py add LINKEDIN_PASSWORD 'your-password' --category LINKEDIN")
                print("   python3 secrets-vault.py add GITHUB_TOKEN 'ghp_xxx' --category GITHUB")
    
    def get_secret(self, key: str, category: str | None = None):
        """Get a specific secret by key"""
        if VAULT_AVAILABLE and VaultSecrets:
            try:
                vault = VaultSecrets()
                return vault.get(key, category)
            except:
                pass
        return os.getenv(key)


# Initialize configuration
config = LinkedInVaultConfig()


# Utility function for easy import
def get_secret(key: str, category: str | None = None):
    """
    Get a secret from vault or environment
    
    Usage:
        from setup.vault_setup import get_secret
        linkedin_email = get_secret('LINKEDIN_EMAIL', 'LINKEDIN')
    """
    return config.get_secret(key, category)


if __name__ == "__main__":
    print("\n" + "="*60)
    print("LinkedIn Jobs Automation - Vault Status")
    print("="*60 + "\n")
    
    config.validate_setup()
    
    if config.is_configured:
        print("\n🚀 Ready to start LinkedIn automation")
        print(f"📧 Using account: {LINKEDIN_EMAIL}")
    else:
        print("\n❌ Cannot start - missing required credentials")
        print("\n📝 Quick Setup Guide:")
        print("1. Navigate to vault: cd /home/<USER>/Documents/Source/secrets-vault")
        print("2. Add secrets:")
        print("   ./secrets-vault.py add LINKEDIN_EMAIL 'your-email' --category LINKEDIN")
        print("   ./secrets-vault.py add LINKEDIN_PASSWORD 'your-password' --category LINKEDIN")
        print("3. Run this script again to verify")
