# 🔐 Vault Integration - Quick Start

## What is This?

Your LinkedIn Jobs Automation now uses a **centralized secrets vault** to securely manage all passwords, API keys, and tokens. No more hardcoded credentials!

## 🚀 Quick Setup (5 minutes)

### Step 1: Add Your Secrets to the Vault

Run the automated setup script:

```bash
./setup/setup_vault_secrets.sh
```

This will prompt you for:
- Vault master password
- LinkedIn email
- LinkedIn password
- GitHub SSH key (optional)

### Step 2: Verify Integration

```bash
python3 setup/vault_setup.py
```

This will prompt you for:

You should see:
```
🔐 Loaded LinkedIn automation secrets from vault
✅ LinkedIn credentials loaded for: <EMAIL>
✅ Ready to start LinkedIn automation
```

### Step 3: Update Your Code

In your main application file, add:

```python
from vault_setup import config, get_secret

# Get credentials from vault
linkedin_email = get_secret('LINKEDIN_EMAIL', 'LINKEDIN')
linkedin_password = get_secret('LINKEDIN_PASSWORD', 'LINKEDIN')

# Or use config object
creds = config.linkedin_credentials
```

## 📋 Manual Setup (Alternative)

If you prefer manual setup:

```bash
cd /home/<USER>/Documents/Source/secrets-vault

# Add LinkedIn credentials
./secrets-vault.py add LINKEDIN_EMAIL '<EMAIL>' --category LINKEDIN
./secrets-vault.py add LINKEDIN_PASSWORD 'your-password' --category LINKEDIN

# Add GitHub token (if needed for API)
./secrets-vault.py add GITHUB_TOKEN 'ghp_xxxxx' --category GITHUB

# Add AI service keys (if using)
./secrets-vault.py add OPENAI_API_KEY 'sk-xxxxx' --category AI
```

## 🔍 Verify Your Secrets

List all LinkedIn secrets:

```bash
cd /home/<USER>/Documents/Source/secrets-vault
./secrets-vault.py list --category LINKEDIN
```

## ✅ What's Protected

| Secret Type | Vault Key | Category |
|-------------|-----------|----------|
| LinkedIn Email | `LINKEDIN_EMAIL` | LINKEDIN |
| LinkedIn Password | `LINKEDIN_PASSWORD` | LINKEDIN |
| GitHub Token | `GITHUB_TOKEN` | GITHUB |
| GitHub SSH Key | `GITHUB_SSH_KEY` | GITHUB |
| OpenAI API | `OPENAI_API_KEY` | AI |
| Anthropic API | `ANTHROPIC_API_KEY` | AI |

## 🛠️ Common Commands

```bash
# List secrets by category
./secrets-vault.py list --category LINKEDIN

# Add a new secret
./secrets-vault.py add SECRET_NAME 'value' --category CATEGORY

# Update existing secret
./secrets-vault.py update SECRET_NAME 'new_value'

# Delete a secret
./secrets-vault.py delete SECRET_NAME

# Search for secrets
./secrets-vault.py search linkedin
```

## 🔐 Security Benefits

✅ No credentials in source code  
✅ Encrypted vault storage  
✅ No accidental git commits of secrets  
✅ Easy credential rotation  
✅ Centralized management across all projects  

## 📚 Full Documentation

See `VAULT_INTEGRATION.md` (in the same docs/ folder) for complete documentation.

## ❓ Troubleshooting

**Vault not found?**
```bash
ls -la /home/<USER>/Documents/Source/secrets-vault
```

**Secrets not loading?**
```bash
# Check vault status
cd /home/<USER>/Documents/Source/secrets-vault
./secrets-vault.py list

# Test integration
cd /home/<USER>/Documents/Source/lin/linkedin-jobs
python3 vault_setup.py
```

**Need to reset?**
```bash
# Re-run setup
./setup/setup_vault_secrets.sh
```

## 🎯 Next Steps

1. ✅ Run `./setup/setup_vault_secrets.sh`
2. ✅ Verify with `python3 setup/vault_setup.py`
3. ✅ Update your application code to use vault
4. ✅ Remove hardcoded credentials from config files
5. ✅ Test your application

---

**Need help?** Check `VAULT_INTEGRATION.md` (in the same docs/ folder) for detailed documentation.
