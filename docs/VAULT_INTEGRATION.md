# Vault Integration for LinkedIn Jobs Automation

## Overview

This project uses the centralized **Secrets Vault** located at `/home/<USER>/Documents/Source/secrets-vault` to securely manage all sensitive credentials, API keys, and passwords.

## Managed Secrets

### LinkedIn Credentials
- `LINKEDIN_EMAIL` - Your LinkedIn account email
- `LINKEDIN_PASSWORD` - Your LinkedIn account password

### GitHub Configuration
- `GITHUB_TOKEN` - GitHub Personal Access Token (for API access)
- `GITHUB_SSH_KEY` - SSH private key for Git operations

### AI Services
- `OPENAI_API_KEY` - OpenAI API key for GPT models
- `ANTHROPIC_API_KEY` - Anthropic Claude API key
- `QDRANT_API_KEY` - Qdrant vector database API key

## Quick Start

### 1. Add Secrets to Vault

```bash
cd /home/<USER>/Documents/Source/secrets-vault

# LinkedIn credentials
./secrets-vault.py add LINKEDIN_EMAIL '<EMAIL>' --category LINKEDIN
./secrets-vault.py add LINKEDIN_PASSWORD 'your-password' --category LINKEDIN

# GitHub (optional - only if using API)
./secrets-vault.py add GITHUB_TOKEN 'ghp_xxxxxxxxxxxxx' --category GITHUB

# AI Services (if using)
./secrets-vault.py add OPENAI_API_KEY 'sk-xxxxxxxxxxxxx' --category AI
```

### 2. Verify Vault Integration

```bash
cd /home/<USER>/Documents/Source/lin/linkedin-jobs
python3 setup/vault_setup.py
```

You should see:
```
🔐 Loaded LinkedIn automation secrets from vault
✅ LinkedIn credentials loaded for: <EMAIL>
✅ Ready to start LinkedIn automation
```

### 3. Use in Your Code

```python
from vault_setup import config, get_secret

# Get LinkedIn credentials
linkedin_email = get_secret('LINKEDIN_EMAIL', 'LINKEDIN')
linkedin_password = get_secret('LINKEDIN_PASSWORD', 'LINKEDIN')

# Or use the config object
creds = config.linkedin_credentials
print(f"Email: {creds['email']}")
```

## Integration Points

### Main Application (`main.py` or similar)

Add at the top of your main script:

```python
from vault_setup import config

# Validate secrets are available
config.validate_setup()

if not config.is_configured:
    print("❌ Missing required credentials!")
    exit(1)

# Get credentials
linkedin_creds = config.linkedin_credentials
```

### Configuration Files

Update your config loading to use vault:

```python
from vault_setup import get_secret

# Instead of hardcoding or reading from config files:
# email = config['linkedin']['email']  # OLD

# Use vault:
email = get_secret('LINKEDIN_EMAIL', 'LINKEDIN')  # NEW
password = get_secret('LINKEDIN_PASSWORD', 'LINKEDIN')  # NEW
```

## Security Benefits

✅ **No hardcoded credentials** in source code  
✅ **Encrypted storage** in the vault  
✅ **Centralized management** across all projects  
✅ **No accidental git commits** of secrets  
✅ **Easy credential rotation**  
✅ **Audit trail** of secret access  

## Vault Commands Reference

```bash
# List all secrets
./secrets-vault.py list

# List secrets by category
./secrets-vault.py list --category LINKEDIN

# Add a secret
./secrets-vault.py add SECRET_NAME 'secret_value' --category CATEGORY

# Update a secret
./secrets-vault.py update SECRET_NAME 'new_value'

# Delete a secret
./secrets-vault.py delete SECRET_NAME

# Export secrets (for backup)
./secrets-vault.py export --category LINKEDIN

# Search secrets
./secrets-vault.py search linkedin
```

## Troubleshooting

### Vault Not Found

If you see "❌ Secrets vault not found!", check:

1. Vault exists at: `/home/<USER>/Documents/Source/secrets-vault`
2. The `secrets-vault.py` file is present
3. Permissions are correct

### Secrets Not Loading

1. Verify secrets are in the vault:
   ```bash
   cd /home/<USER>/Documents/Source/secrets-vault
   ./secrets-vault.py list --category LINKEDIN
   ```

2. Check vault integration:
   ```bash
   cd /home/<USER>/Documents/Source/lin/linkedin-jobs
   python3 setup/vault_setup.py
   ```

3. Fall back to environment variables temporarily:
   ```bash
   export LINKEDIN_EMAIL="<EMAIL>"
   export LINKEDIN_PASSWORD="your-password"
   ```

## Migration from Config Files

If you currently store credentials in JSON files:

```bash
# 1. Extract credentials from config file
grep -i email linkedin_config.json
grep -i password linkedin_config.json

# 2. Add to vault
cd /home/<USER>/Documents/Source/secrets-vault
./secrets-vault.py add LINKEDIN_EMAIL 'extracted-email' --category LINKEDIN
./secrets-vault.py add LINKEDIN_PASSWORD 'extracted-password' --category LINKEDIN

# 3. Remove from config file
# Edit linkedin_config.json and remove sensitive data

# 4. Update .gitignore to exclude old config
echo "linkedin_config.json" >> .gitignore
```

## Next Steps

1. ✅ Add your LinkedIn credentials to vault
2. ✅ Update main application to use `vault_setup.py`
3. ✅ Remove hardcoded credentials from config files
4. ✅ Test the integration
5. ✅ Commit the vault integration files (not the secrets!)

## Related Files

- `vault_setup.py` - Vault integration module for this project
- `.gitignore` - Ensures secrets never get committed
- `linkedin_config.json` - Can now store non-sensitive settings only
