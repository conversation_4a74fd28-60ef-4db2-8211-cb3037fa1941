# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

**Main execution:**
```bash
python3 main.py                    # Launch web dashboard (default)
python3 main.py --cli              # Use command-line interface
python3 main.py --apply            # Run job application directly
```

**Testing/Development:**
```bash
python3 test_job_utils.py          # Test job application utilities
```

**Configuration:**
- Main config: `linkedin_config.json` (contains credentials and settings)
- Questions database: `questions_log.json` (stores form field answers)

## Architecture

**Main Components:**
- `main.py`: Entry point and platform orchestrator with menu system
- `modular_job_apply.py`: Core job application automation using LinkedIn selectors
- `job_application_utils.py`: Utility classes for form handling and element interaction
- `web_dashboard_backend.py`: Web interface backend

**Module Pattern:**
Each automation feature (network, messages, content) follows the pattern:
- Takes browser_manager, data_manager, config, logger as constructor args
- Implements run() or similar execution method
- Logs actions to data_manager and file logs

**Configuration Structure:**
- Browser settings (Chrome path, headless mode)
- Application limits and delays for rate limiting
- User profile data for form filling
- Custom selectors for LinkedIn UI changes

**Key Files:**
- `linkedin_config.json`: Main configuration (contains credentials)
- `logs/dashboard.log`: Runtime logs showing start/stop events
- `questions_log.json`: Form field answers cache

**LinkedIn Integration:**
- Uses Selenium WebDriver with Chrome
- Implements selectors that adapt to LinkedIn UI changes
- Handles multi-step application forms with validation
- Supports Easy Apply filtering and job card extraction