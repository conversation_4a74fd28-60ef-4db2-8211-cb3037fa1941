#!/usr/bin/env python3
"""
Article Publishing Module for LinkedIn Automation
Researches trending topics and creates posts/articles
"""
import time
import random
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import json
import re
from bs4 import BeautifulSoup

@dataclass
class ContentIdea:
    """Represents a content idea"""
    topic: str
    category: str
    keywords: List[str]
    trending_score: float
    target_audience: str
    content_type: str  # 'post', 'article', 'poll', 'image_post'
    
@dataclass
class LinkedInPost:
    """Represents a LinkedIn post"""
    content: str
    hashtags: List[str]
    content_type: str
    media_url: Optional[str] = None
    poll_options: Optional[List[str]] = None
    scheduled_time: Optional[datetime] = None

class ContentCreator:
    """Creates and publishes content on LinkedIn"""
    
    def __init__(self, browser_manager, ai_provider, config, logger):
        self.browser = browser_manager
        self.ai = ai_provider
        self.config = config
        self.logger = logger
        self.trending_topics_cache = {}
        
    def create_and_publish_content(self):
        """Main content creation and publishing routine"""
        self.logger.info("Starting content creation process")
        
        # Research trending topics
        topics = self.research_trending_topics()
        
        # Select best topic
        if topics:
            selected_topic = self.select_topic(topics)
            
            # Generate content
            if selected_topic:
                post = self.generate_content(selected_topic)
                
                # Publish content
                if post:
                    self.publish_content(post)
                    
                    # Track performance (for future analytics)
                    self.track_content_performance(post)
        
        self.logger.info("Content creation process complete")
        
    def research_trending_topics(self) -> List[ContentIdea]:
        """Research trending topics in the industry"""
        topics = []
        
        # Get trending topics from multiple sources
        topics.extend(self._get_linkedin_trending())
        topics.extend(self._get_industry_news())
        topics.extend(self._analyze_network_interests())
        topics.extend(self._get_hashtag_trends())
        
        # Score and rank topics
        scored_topics = self._score_topics(topics)
        
        return scored_topics[:10]  # Return top 10 topics
        
    def _get_linkedin_trending(self) -> List[ContentIdea]:
        """Get trending topics from LinkedIn"""
        trending = []
        
        try:
            # Navigate to LinkedIn News
            self.browser.driver.get("https://www.linkedin.com/feed/news/")
            time.sleep(3)
            
            # Extract trending stories
            news_items = self.browser.driver.find_elements(
                By.XPATH, "//div[contains(@class,'news-module')]//a"
            )
            
            for item in news_items[:10]:
                try:
                    title = item.text.strip()
                    if title:
                        # Extract keywords
                        keywords = self._extract_keywords(title)
                        
                        idea = ContentIdea(
                            topic=title,
                            category="trending",
                            keywords=keywords,
                            trending_score=0.8,
                            target_audience="general",
                            content_type="post"
                        )
                        trending.append(idea)
                        
                except Exception as e:
                    self.logger.debug(f"Error parsing news item: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error getting LinkedIn trending: {e}")
            
        return trending
        
    def _get_industry_news(self) -> List[ContentIdea]:
        """Get industry-specific news and trends"""
        industry_topics = []
        
        # Define tech news sources
        tech_keywords = [
            "artificial intelligence", "machine learning", "cloud computing",
            "cybersecurity", "blockchain", "devops", "microservices",
            "kubernetes", "react", "python", ".NET", "software development",
            "tech trends", "digital transformation", "remote work"
        ]
        
        for keyword in tech_keywords[:5]:  # Limit API calls
            try:
                # Simulate getting news (in real implementation, use news API)
                idea = ContentIdea(
                    topic=f"Latest developments in {keyword}",
                    category="technology",
                    keywords=[keyword] + self._extract_keywords(keyword),
                    trending_score=random.uniform(0.5, 0.9),
                    target_audience="tech professionals",
                    content_type="article" if len(keyword) > 10 else "post"
                )
                industry_topics.append(idea)
                
            except Exception as e:
                self.logger.debug(f"Error getting news for {keyword}: {e}")
                
        return industry_topics
        
    def _analyze_network_interests(self) -> List[ContentIdea]:
        """Analyze what your network is talking about"""
        network_topics = []
        
        try:
            # Navigate to feed
            self.browser.driver.get("https://www.linkedin.com/feed/")
            time.sleep(3)
            
            # Analyze recent posts
            posts = self.browser.driver.find_elements(
                By.XPATH, "//article[contains(@class,'feed-shared-update')]"
            )
            
            # Extract topics from posts
            topic_frequency = {}
            
            for post in posts[:20]:
                try:
                    text = post.text.lower()
                    
                    # Extract hashtags
                    hashtags = re.findall(r'#(\w+)', text)
                    for tag in hashtags:
                        topic_frequency[tag] = topic_frequency.get(tag, 0) + 1
                        
                    # Extract key phrases
                    for keyword in self.config.ai.personality_traits:
                        if keyword.lower() in text:
                            topic_frequency[keyword] = topic_frequency.get(keyword, 0) + 1
                            
                except Exception:
                    continue
                    
            # Convert to ContentIdeas
            for topic, freq in sorted(topic_frequency.items(), key=lambda x: x[1], reverse=True)[:5]:
                idea = ContentIdea(
                    topic=f"Insights on {topic}",
                    category="network_interest",
                    keywords=[topic],
                    trending_score=min(freq / 10, 1.0),
                    target_audience="your network",
                    content_type="post"
                )
                network_topics.append(idea)
                
        except Exception as e:
            self.logger.error(f"Error analyzing network interests: {e}")
            
        return network_topics
        
    def _get_hashtag_trends(self) -> List[ContentIdea]:
        """Get trending hashtags in your industry"""
        trending_hashtags = [
            "#TechTrends2024", "#AIRevolution", "#CloudComputing",
            "#CyberSecurity", "#RemoteWork", "#DigitalTransformation",
            "#SoftwareDevelopment", "#Innovation", "#FutureOfWork",
            "#DataScience", "#MachineLearning", "#DevOps"
        ]
        
        hashtag_topics = []
        for hashtag in trending_hashtags[:5]:
            idea = ContentIdea(
                topic=f"Thoughts on {hashtag}",
                category="hashtag_trend",
                keywords=[hashtag.replace('#', '')],
                trending_score=random.uniform(0.6, 0.9),
                target_audience="tech community",
                content_type="post"
            )
            hashtag_topics.append(idea)
            
        return hashtag_topics
        
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Simple keyword extraction
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'}
        words = re.findall(r'\w+', text.lower())
        keywords = [w for w in words if len(w) > 3 and w not in stop_words]
        return list(set(keywords))[:5]
        
    def _score_topics(self, topics: List[ContentIdea]) -> List[ContentIdea]:
        """Score and rank topics based on relevance and trending score"""
        # Add relevance scoring based on your expertise
        expertise_keywords = [
            'software', 'development', 'programming', 'tech', 'cloud',
            'api', 'web', 'mobile', 'data', 'security', '.net', 'python'
        ]
        
        for topic in topics:
            relevance_score = 0
            for keyword in expertise_keywords:
                if keyword in topic.topic.lower() or keyword in topic.keywords:
                    relevance_score += 0.1
                    
            # Combined score
            topic.trending_score = (topic.trending_score * 0.7) + (relevance_score * 0.3)
            
        # Sort by score
        return sorted(topics, key=lambda x: x.trending_score, reverse=True)
        
    def select_topic(self, topics: List[ContentIdea]) -> Optional[ContentIdea]:
        """Select the best topic to write about"""
        # Check recent posts to avoid repetition
        recent_topics = self._get_recent_post_topics()
        
        for topic in topics:
            # Check if topic is too similar to recent posts
            is_duplicate = False
            for recent in recent_topics:
                similarity = self._calculate_similarity(topic.topic, recent)
                if similarity > 0.7:
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                self.logger.info(f"Selected topic: {topic.topic}")
                return topic
                
        # If all topics are similar, return the highest scored one
        return topics[0] if topics else None
        
    def _get_recent_post_topics(self) -> List[str]:
        """Get topics of recent posts to avoid duplication"""
        # In a real implementation, this would check your post history
        return []
        
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        # Simple word overlap similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
            
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
        
    def generate_content(self, topic: ContentIdea) -> LinkedInPost:
        """Generate content based on selected topic"""
        self.logger.info(f"Generating content for: {topic.topic}")
        
        if topic.content_type == "post":
            return self._generate_post(topic)
        elif topic.content_type == "article":
            return self._generate_article(topic)
        elif topic.content_type == "poll":
            return self._generate_poll(topic)
        else:
            return self._generate_post(topic)
            
    def _generate_post(self, topic: ContentIdea) -> LinkedInPost:
        """Generate a LinkedIn post"""
        # Use AI to generate content
        prompt = f"""Write a professional LinkedIn post about: {topic.topic}
        
        Target audience: {topic.target_audience}
        Keywords to include: {', '.join(topic.keywords)}
        
        The post should be:
        - Engaging and thought-provoking
        - 150-300 words
        - Include a call to action or question for engagement
        - Professional but conversational tone
        - Written from the perspective of a {', '.join(self.config.ai.personality_traits[:3])}
        
        Do not include hashtags in the main content."""
        
        content = self.ai.generate_answer("LinkedIn Post", "post", prompt)
        
        # Generate appropriate hashtags
        hashtags = self._generate_hashtags(topic)
        
        # Add hashtags to content
        full_content = f"{content}\n\n{' '.join(hashtags)}"
        
        return LinkedInPost(
            content=full_content,
            hashtags=hashtags,
            content_type="post"
        )
        
    def _generate_article(self, topic: ContentIdea) -> LinkedInPost:
        """Generate a LinkedIn article"""
        # For articles, create a summary post with link to full article
        prompt = f"""Write a compelling LinkedIn article introduction about: {topic.topic}
        
        This is a summary that will accompany a full article. It should:
        - Hook the reader in the first line
        - Briefly outline what the article covers
        - Include 2-3 key takeaways
        - End with a call to action to read the full article
        - Be 100-150 words
        
        Target audience: {topic.target_audience}"""
        
        intro = self.ai.generate_answer("Article Introduction", "article", prompt)
        
        # Generate article content (simplified version)
        article_prompt = f"""Write a professional article about: {topic.topic}
        
        Structure:
        1. Introduction
        2. 3-4 main points with examples
        3. Conclusion with actionable takeaways
        
        Length: 500-800 words
        Tone: Professional, informative, engaging"""
        
        article_content = self.ai.generate_answer("Article Content", "article", article_prompt)
        
        # For now, we'll post the introduction with a note about the full article
        content = f"{intro}\n\n[Full article below in comments]"
        
        hashtags = self._generate_hashtags(topic)
        
        return LinkedInPost(
            content=f"{content}\n\n{' '.join(hashtags)}",
            hashtags=hashtags,
            content_type="article"
        )
        
    def _generate_poll(self, topic: ContentIdea) -> LinkedInPost:
        """Generate a LinkedIn poll"""
        prompt = f"""Create a LinkedIn poll about: {topic.topic}
        
        Generate:
        1. A compelling question (max 140 characters)
        2. 2-4 poll options (max 30 characters each)
        3. Brief context/introduction (50-100 words)
        
        Make it thought-provoking and relevant to {topic.target_audience}"""
        
        poll_content = self.ai.generate_answer("Poll Creation", "poll", prompt)
        
        # Parse the response to extract question and options
        lines = poll_content.split('\n')
        question = lines[0] if lines else topic.topic + "?"
        
        # Default poll options if parsing fails
        poll_options = [
            "Strongly agree",
            "Somewhat agree", 
            "Neutral",
            "Disagree"
        ]
        
        hashtags = self._generate_hashtags(topic)
        
        return LinkedInPost(
            content=f"{question}\n\n{' '.join(hashtags)}",
            hashtags=hashtags,
            content_type="poll",
            poll_options=poll_options
        )
        
    def _generate_hashtags(self, topic: ContentIdea) -> List[str]:
        """Generate relevant hashtags"""
        base_hashtags = ["#" + kw.capitalize() for kw in topic.keywords[:3]]
        
        # Add category-specific hashtags
        category_hashtags = {
            "technology": ["#TechNews", "#Innovation", "#DigitalTransformation"],
            "trending": ["#Trending", "#CurrentEvents", "#ThoughtLeadership"],
            "network_interest": ["#Community", "#Networking", "#ProfessionalGrowth"],
            "hashtag_trend": ["#TrendingNow", "#MustRead", "#Insights"]
        }
        
        additional = category_hashtags.get(topic.category, ["#LinkedIn", "#Professional"])
        
        all_hashtags = base_hashtags + additional[:2]
        
        # Ensure common professional hashtags
        if "#Technology" not in all_hashtags and "tech" in topic.topic.lower():
            all_hashtags.append("#Technology")
            
        return all_hashtags[:5]  # Limit to 5 hashtags
        
    def publish_content(self, post: LinkedInPost):
        """Publish content to LinkedIn"""
        try:
            # Navigate to home feed
            self.browser.driver.get("https://www.linkedin.com/feed/")
            time.sleep(3)
            
            if post.content_type == "poll":
                self._publish_poll(post)
            else:
                self._publish_regular_post(post)
                
            self.logger.info(f"Successfully published {post.content_type}")
            
            # Log the published content
            self._log_published_content(post)
            
        except Exception as e:
            self.logger.error(f"Error publishing content: {e}")
            
    def _publish_regular_post(self, post: LinkedInPost):
        """Publish a regular post or article"""
        # Click start post button
        start_post = WebDriverWait(self.browser.driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(@class,'share-box-feed-entry__trigger')]")
            )
        )
        self.browser.safe_click(start_post)
        time.sleep(2)
        
        # Type content
        post_input = WebDriverWait(self.browser.driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//div[@contenteditable='true'][@role='textbox']")
            )
        )
        
        # Type with natural delay
        post_input.click()
        for char in post.content:
            post_input.send_keys(char)
            time.sleep(random.uniform(0.05, 0.1))
            
        # Add media if specified
        if post.media_url:
            self._add_media(post.media_url)
            
        # Wait before posting
        time.sleep(2)
        
        # Click post button
        post_btn = self.browser.driver.find_element(
            By.XPATH, "//button[contains(@class,'share-actions__primary-action')]"
        )
        self.browser.safe_click(post_btn)
        
        time.sleep(3)
        
    def _publish_poll(self, post: LinkedInPost):
        """Publish a poll"""
        # Click start post
        start_post = WebDriverWait(self.browser.driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(@class,'share-box-feed-entry__trigger')]")
            )
        )
        self.browser.safe_click(start_post)
        time.sleep(2)
        
        # Click create poll option
        try:
            poll_option = self.browser.driver.find_element(
                By.XPATH, "//button[contains(@aria-label,'Create a poll')]"
            )
            self.browser.safe_click(poll_option)
            time.sleep(2)
            
            # Enter poll question
            question_input = self.browser.driver.find_element(
                By.XPATH, "//input[@placeholder='Ask a question']"
            )
            question_input.send_keys(post.content.split('\n')[0])
            
            # Enter poll options
            option_inputs = self.browser.driver.find_elements(
                By.XPATH, "//input[contains(@placeholder,'Option')]"
            )
            
            if post.poll_options:
                for i, option in enumerate(post.poll_options[:len(option_inputs)]):
                    option_inputs[i].send_keys(option)
                
            # Set duration (1 week default)
            duration_dropdown = self.browser.driver.find_element(
                By.XPATH, "//select[contains(@id,'poll-duration')]"
            )
            duration_dropdown.send_keys("1 week")
            
            # Post poll
            post_btn = self.browser.driver.find_element(
                By.XPATH, "//button[contains(text(),'Post')]"
            )
            self.browser.safe_click(post_btn)
            
        except Exception as e:
            self.logger.error(f"Error creating poll: {e}")
            # Fallback to regular post
            self._publish_regular_post(post)
            
    def _add_media(self, media_url: str):
        """Add media to post"""
        try:
            # Click add photo button
            photo_btn = self.browser.driver.find_element(
                By.XPATH, "//button[contains(@aria-label,'Add a photo')]"
            )
            self.browser.safe_click(photo_btn)
            time.sleep(2)
            
            # Handle file upload or URL
            # Implementation depends on media source
            
        except Exception as e:
            self.logger.error(f"Error adding media: {e}")
            
    def track_content_performance(self, post: LinkedInPost):
        """Track performance metrics of published content"""
        # This would track views, likes, comments, shares
        # Store in analytics database for future optimization
        performance_data = {
            "post_content": post.content[:200],
            "hashtags": post.hashtags,
            "content_type": post.content_type,
            "published_time": datetime.now().isoformat(),
            "initial_metrics": {
                "views": 0,
                "likes": 0,
                "comments": 0,
                "shares": 0
            }
        }
        
        # Save to tracking file
        tracking_file = f"{self.config.paths.data_directory}/content_performance.json"
        
        try:
            with open(tracking_file, 'r') as f:
                tracking_data = json.load(f)
        except:
            tracking_data = []
            
        tracking_data.append(performance_data)
        
        with open(tracking_file, 'w') as f:
            json.dump(tracking_data, f, indent=2)
            
    def _log_published_content(self, post: LinkedInPost):
        """Log published content for records"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "content": post.content,
            "hashtags": post.hashtags,
            "content_type": post.content_type,
            "status": "published"
        }
        
        log_file = f"{self.config.paths.log_directory}/published_content.json"
        
        try:
            with open(log_file, 'r') as f:
                log_data = json.load(f)
        except:
            log_data = []
            
        log_data.append(log_entry)
        
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
            
    def schedule_content(self, posts: List[LinkedInPost], schedule: Dict[str, List[str]]):
        """Schedule content for future publishing"""
        # This would integrate with a scheduling system
        # For now, we'll save scheduled posts for manual publishing
        
        scheduled_file = f"{self.config.paths.data_directory}/scheduled_posts.json"
        
        scheduled_posts = []
        for post in posts:
            scheduled_posts.append({
                "content": post.content,
                "hashtags": post.hashtags,
                "content_type": post.content_type,
                "scheduled_time": post.scheduled_time.isoformat() if post.scheduled_time else None,
                "status": "scheduled"
            })
            
        with open(scheduled_file, 'w') as f:
            json.dump(scheduled_posts, f, indent=2)
            
        self.logger.info(f"Scheduled {len(posts)} posts for future publishing")
        
    def analyze_best_posting_times(self) -> Dict[str, float]:
        """Analyze when your posts get the most engagement"""
        # This would analyze historical data
        # For now, return general best times
        return {
            "Monday_9AM": 0.8,
            "Tuesday_10AM": 0.85,
            "Wednesday_11AM": 0.9,
            "Thursday_10AM": 0.88,
            "Friday_9AM": 0.75,
            "Saturday_10AM": 0.6,
            "Sunday_7PM": 0.7
        }