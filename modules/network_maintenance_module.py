#!/usr/bin/env python3
"""
Network Maintenance Module for LinkedIn Automation
Handles birthday wishes, congratulations, and network relationship maintenance
"""
import time
import random
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json
import re

@dataclass
class NetworkContact:
    """Represents a network contact"""
    name: str
    profile_url: str
    event_type: str  # 'birthday', 'new_job', 'work_anniversary', 'promotion'
    event_date: Optional[str] = None
    last_interaction: Optional[str] = None
    company: Optional[str] = None
    position: Optional[str] = None

class NetworkMaintainer:
    """Manages network relationship maintenance"""
    
    def __init__(self, browser_manager, data_manager, config, logger):
        self.browser = browser_manager
        self.data = data_manager
        self.config = config
        self.logger = logger
        self.interactions_count = 0
        self.daily_limits = {
            'messages': 50,
            'profile_views': 100,
            'interactions': 150
        }
        self.previous_interactions = {}
        
    def maintain_network(self):
        """Main network maintenance routine"""
        self.logger.info("Starting network maintenance")
        
        # Check notifications for events
        events = self.check_network_events()
        
        # Process each event
        for event in events:
            if self.interactions_count >= self.daily_limits['interactions']:
                self.logger.warning("Reached daily interaction limit")
                break
                
            self.process_network_event(event)
            
            # Random delay between interactions
            delay = random.uniform(*self.config.delays.network_action_delay)
            time.sleep(delay)
            
        # Check milestone connections
        self.check_milestone_connections()
        
        # Engage with network content
        self.engage_with_network_content()
        
        self.logger.info(f"Network maintenance complete. Total interactions: {self.interactions_count}")
        
    def check_network_events(self) -> List[NetworkContact]:
        """Check for birthdays, job changes, and other events"""
        events = []
        
        try:
            # Navigate to notifications
            self.browser.driver.get("https://www.linkedin.com/notifications/")
            time.sleep(3)
            
            # Look for different event types
            events.extend(self._check_birthdays())
            events.extend(self._check_job_changes())
            events.extend(self._check_work_anniversaries())
            
        except Exception as e:
            self.logger.error(f"Error checking network events: {e}")
            
        return events
        
    def _check_birthdays(self) -> List[NetworkContact]:
        """Check for birthday notifications"""
        birthdays = []
        
        try:
            # Navigate to My Network page
            self.browser.driver.get("https://www.linkedin.com/mynetwork/")
            time.sleep(3)
            
            # Look for birthday section
            birthday_section = self.browser.driver.find_elements(
                By.XPATH, 
                "//section[contains(.,'Birthday')]//a[contains(@href,'/in/')]"
            )
            
            for elem in birthday_section[:10]:  # Limit to 10 birthdays
                try:
                    name = elem.text.strip()
                    profile_url = elem.get_attribute('href')
                    
                    if name and profile_url:
                        contact = NetworkContact(
                            name=name,
                            profile_url=profile_url,
                            event_type='birthday',
                            event_date=datetime.now().strftime("%Y-%m-%d")
                        )
                        birthdays.append(contact)
                        self.logger.info(f"Found birthday: {name}")
                        
                except Exception as e:
                    self.logger.debug(f"Error parsing birthday element: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error checking birthdays: {e}")
            
        return birthdays
        
    def _check_job_changes(self) -> List[NetworkContact]:
        """Check for job change notifications"""
        job_changes = []
        
        try:
            # Look for job change notifications
            job_notifications = self.browser.driver.find_elements(
                By.XPATH,
                "//span[contains(text(),'started a new position')]/../.."
            )
            
            for notification in job_notifications[:10]:
                try:
                    # Extract name and profile URL
                    name_elem = notification.find_element(By.XPATH, ".//a[contains(@href,'/in/')]")
                    name = name_elem.text.strip()
                    profile_url = name_elem.get_attribute('href')
                    
                    # Try to extract company and position
                    text = notification.text
                    company_match = re.search(r'at\s+(.+?)(?:\s|$)', text)
                    company = company_match.group(1) if company_match else None
                    
                    contact = NetworkContact(
                        name=name,
                        profile_url=profile_url,
                        event_type='new_job',
                        event_date=datetime.now().strftime("%Y-%m-%d"),
                        company=company
                    )
                    job_changes.append(contact)
                    self.logger.info(f"Found job change: {name} at {company}")
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing job change: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error checking job changes: {e}")
            
        return job_changes
        
    def _check_work_anniversaries(self) -> List[NetworkContact]:
        """Check for work anniversary notifications"""
        anniversaries = []
        
        try:
            notifications = self.browser.driver.find_elements(
                By.XPATH,
                "//span[contains(text(),'work anniversary')]/../.."
            )
            
            for notification in notifications[:10]:
                try:
                    name_elem = notification.find_element(By.XPATH, ".//a[contains(@href,'/in/')]")
                    name = name_elem.text.strip()
                    profile_url = name_elem.get_attribute('href')
                    
                    contact = NetworkContact(
                        name=name,
                        profile_url=profile_url,
                        event_type='work_anniversary',
                        event_date=datetime.now().strftime("%Y-%m-%d")
                    )
                    anniversaries.append(contact)
                    self.logger.info(f"Found work anniversary: {name}")
                    
                except Exception as e:
                    self.logger.debug(f"Error parsing anniversary: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error checking anniversaries: {e}")
            
        return anniversaries
        
    def process_network_event(self, contact: NetworkContact):
        """Process a network event by sending appropriate message"""
        try:
            # Navigate to profile
            self.browser.driver.get(contact.profile_url)
            time.sleep(2)
            
            # Click message button
            message_btn = WebDriverWait(self.browser.driver, 10).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//button[contains(@aria-label,'Message') or contains(text(),'Message')]")
                )
            )
            self.browser.safe_click(message_btn)
            time.sleep(2)
            
            # Compose and send message
            message = self.compose_message(contact)
            self.send_message(message)
            
            # Log the interaction
            self.data.log_network_action(
                action_type=f"message_{contact.event_type}",
                profile_name=contact.name,
                profile_url=contact.profile_url,
                message=message
            )
            
            self.interactions_count += 1
            self.logger.info(f"Sent {contact.event_type} message to {contact.name}")
            
        except Exception as e:
            self.logger.error(f"Error processing event for {contact.name}: {e}")
            
    def compose_message(self, contact: NetworkContact) -> str:
        """Compose appropriate message based on event type"""
        templates = {
            'birthday': [
                "Happy Birthday {name}! 🎉 Wishing you a fantastic day filled with joy and success!",
                "Happy Birthday {name}! 🎂 Hope your special day is amazing!",
                "Many happy returns {name}! 🎈 Wishing you all the best on your birthday!"
            ],
            'new_job': [
                "Congratulations on your new role at {company}, {name}! 🎉 Wishing you great success in this exciting new chapter!",
                "Congrats on the new position {name}! 👏 {company} is lucky to have you!",
                "Exciting news about your move to {company}, {name}! Best wishes for continued success!"
            ],
            'work_anniversary': [
                "Happy work anniversary {name}! 🎊 Your dedication and contributions are truly appreciated!",
                "Congratulations on another year of success {name}! 🌟 Here's to many more!",
                "Happy anniversary {name}! Time flies when you're making an impact! 💼"
            ],
            'promotion': [
                "Huge congratulations on your promotion {name}! 🚀 Well deserved!",
                "Fantastic news about your promotion {name}! 🌟 Your hard work is paying off!",
                "Congrats on leveling up {name}! 🎯 Excited to see your continued success!"
            ]
        }
        
        # Get appropriate template
        template_list = templates.get(contact.event_type, templates['new_job'])
        template = random.choice(template_list)
        
        # Format message
        message = template.format(
            name=contact.name.split()[0],  # Use first name
            company=contact.company or "your new company"
        )
        
        # Add personalization based on previous interactions
        if hasattr(self, 'previous_interactions') and contact.name in self.previous_interactions:
            message += "\n\nHope you've been well since we last connected!"
            
        return message
        
    def send_message(self, message: str):
        """Send a message in the message window"""
        try:
            # Find message input
            message_input = WebDriverWait(self.browser.driver, 10).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[@contenteditable='true'][@role='textbox']")
                )
            )
            
            # Type message with natural delay
            message_input.click()
            for char in message:
                message_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
                
            # Send message
            send_btn = self.browser.driver.find_element(
                By.XPATH, "//button[@type='submit'][contains(@class,'msg-form__send-button')]"
            )
            self.browser.safe_click(send_btn)
            
            time.sleep(2)
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            
    def check_milestone_connections(self):
        """Check for milestone connections (1st, 100th, 500th, etc.)"""
        try:
            # Navigate to connections page
            self.browser.driver.get("https://www.linkedin.com/mynetwork/invite-connect/connections/")
            time.sleep(3)
            
            # Get connection count
            count_elem = self.browser.driver.find_element(
                By.XPATH, "//h1[contains(@class,'mn-connections__header')]"
            )
            count_text = count_elem.text
            count_match = re.search(r'(\d+)', count_text)
            
            if count_match:
                connection_count = int(count_match.group(1))
                
                # Check for milestones
                milestones = [100, 500, 1000, 2000, 5000, 10000]
                for milestone in milestones:
                    if connection_count == milestone:
                        self.celebrate_milestone(milestone)
                        break
                        
        except Exception as e:
            self.logger.error(f"Error checking milestones: {e}")
            
    def celebrate_milestone(self, milestone: int):
        """Post about reaching a connection milestone"""
        try:
            self.logger.info(f"Reached {milestone} connections milestone!")
            
            # Navigate to home feed
            self.browser.driver.get("https://www.linkedin.com/feed/")
            time.sleep(3)
            
            # Click start post
            start_post = WebDriverWait(self.browser.driver, 10).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//button[contains(@class,'share-box-feed-entry__trigger')]")
                )
            )
            self.browser.safe_click(start_post)
            time.sleep(2)
            
            # Compose milestone post
            post_text = f"""🎉 Excited to have reached {milestone} connections on LinkedIn! 🎉

Thank you to everyone in my network for the valuable connections, insights, and opportunities we've shared.

Looking forward to continuing to grow and learn together!

#Networking #LinkedIn #Milestone #Grateful"""
            
            # Type post
            post_input = self.browser.driver.find_element(
                By.XPATH, "//div[@contenteditable='true'][@role='textbox']"
            )
            post_input.send_keys(post_text)
            
            # Post
            post_btn = self.browser.driver.find_element(
                By.XPATH, "//button[contains(@class,'share-actions__primary-action')]"
            )
            self.browser.safe_click(post_btn)
            
            self.logger.info(f"Posted about {milestone} connections milestone")
            
        except Exception as e:
            self.logger.error(f"Error posting milestone: {e}")
            
    def engage_with_network_content(self):
        """Like and comment on network posts"""
        try:
            # Navigate to feed
            self.browser.driver.get("https://www.linkedin.com/feed/")
            time.sleep(3)
            
            # Find posts from connections
            posts = self.browser.driver.find_elements(
                By.XPATH, "//article[contains(@class,'feed-shared-update')]"
            )
            
            engaged_count = 0
            max_engagements = 10
            
            for post in posts[:20]:  # Check first 20 posts
                if engaged_count >= max_engagements:
                    break
                    
                try:
                    # Check if it's from a connection
                    author = post.find_element(
                        By.XPATH, ".//span[contains(@class,'feed-shared-actor__name')]"
                    ).text
                    
                    # Random engagement (like or comment)
                    if random.random() < 0.7:  # 70% chance to engage
                        if random.random() < 0.8:  # 80% like, 20% comment
                            self.like_post(post)
                        else:
                            self.comment_on_post(post, author)
                            
                        engaged_count += 1
                        self.interactions_count += 1
                        
                        # Small delay between engagements
                        time.sleep(random.uniform(2, 5))
                        
                except Exception as e:
                    self.logger.debug(f"Error engaging with post: {e}")
                    
            self.logger.info(f"Engaged with {engaged_count} posts")
            
        except Exception as e:
            self.logger.error(f"Error engaging with content: {e}")
            
    def like_post(self, post_element):
        """Like a post"""
        try:
            like_btn = post_element.find_element(
                By.XPATH, ".//button[contains(@aria-label,'Like')]"
            )
            
            # Check if already liked
            if 'true' not in like_btn.get_attribute('aria-pressed'):
                self.browser.safe_click(like_btn)
                self.logger.debug("Liked a post")
                
        except Exception as e:
            self.logger.debug(f"Error liking post: {e}")
            
    def comment_on_post(self, post_element, author: str):
        """Comment on a post with contextual response"""
        try:
            # Click comment button
            comment_btn = post_element.find_element(
                By.XPATH, ".//button[contains(@aria-label,'Comment')]"
            )
            self.browser.safe_click(comment_btn)
            time.sleep(2)
            
            # Generate contextual comment
            comments = [
                "Great insights! Thanks for sharing 👍",
                "Very interesting perspective! 🎯",
                "Thanks for sharing this valuable content!",
                "Excellent point! Appreciate you sharing this.",
                "This is really insightful! Thanks for posting.",
                "Couldn't agree more! Well said 👏",
                "Thanks for sharing your expertise on this!",
                "Really valuable perspective here!"
            ]
            
            comment = random.choice(comments)
            
            # Type comment
            comment_input = post_element.find_element(
                By.XPATH, ".//div[@contenteditable='true'][@role='textbox']"
            )
            comment_input.click()
            comment_input.send_keys(comment)
            
            # Post comment
            post_comment_btn = post_element.find_element(
                By.XPATH, ".//button[contains(@class,'comments-comment-box__submit-button')]"
            )
            self.browser.safe_click(post_comment_btn)
            
            self.logger.debug(f"Commented on {author}'s post")
            
        except Exception as e:
            self.logger.debug(f"Error commenting: {e}")
            
    def track_interaction_history(self):
        """Track interaction history to avoid duplicate messages"""
        history_file = self.config.paths.data_directory + "/interaction_history.json"
        
        try:
            with open(history_file, 'r') as f:
                history = json.load(f)
        except:
            history = {}
            
        # Update history with today's interactions
        today = datetime.now().strftime("%Y-%m-%d")
        if today not in history:
            history[today] = []
            
        # Add interactions (implement based on actual tracking needs)
        
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
            
    def get_personalized_greeting(self, contact_name: str, contact_info: Dict) -> str:
        """Generate personalized greeting based on contact information"""
        first_name = contact_name.split()[0]
        
        # Time-based greeting
        hour = datetime.now().hour
        if hour < 12:
            time_greeting = "Good morning"
        elif hour < 17:
            time_greeting = "Good afternoon"
        else:
            time_greeting = "Good evening"
            
        # Add personalization based on shared connections, companies, etc.
        greeting = f"{time_greeting} {first_name}!"
        
        if contact_info.get('shared_connections'):
            greeting += f" I see we have {contact_info['shared_connections']} mutual connections."
            
        return greeting