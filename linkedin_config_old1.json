{"username": "<EMAIL>", "password": "Exit532493(*)", "browser": {"chrome_binary_location": "/usr/bin/google-chrome-stable", "headless": false, "window_size": [1920, 1080], "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "resume_path": "/home/<USER>/Documents/Source/lin/linkedin-jobs/GSBR.docx", "openai_api_key": "********************************************************************************************************************************************************************", "user_profile": {"name": "<PERSON><PERSON><PERSON><PERSON>", "phone": "************", "location": "Cleveland, OH", "linkedin": "https://linkedin.com/in/gior<PERSON><PERSON>-shepov", "github": "https://github.com/george-shepov", "portfolio": "", "years_experience": "10+", "skills": ["Python", "JavaScript", ".NET", "C#", "React", "<PERSON><PERSON>", "Angular", "Azure", "AWS", "<PERSON>er", "Kubernetes", "Microservices", "SQL", "NoSQL", "REST APIs", "GraphQL", "DevOps"], "education": {"degree": "MBA", "field": "Management, Information Systems", "school": "Cleveland State University", "year": "2009"}, "work_authorization": "US Citizen", "salary_range": [80000, 200000], "willing_to_relocate": false, "remote_preference": "Remote or Hybrid", "notice_period": "2 weeks", "languages": {"English": "Native", "Russian": "Native"}}, "search": {"keywords": ["Software Engineer", "Full Stack Developer", ".NET Developer", "Senior Developer", "Technical Lead", "Solutions Architect"], "locations": ["Remote", "Cleveland, OH", "United States"], "experience_levels": ["Mid-Senior", "Senior", "Lead", "Principal"], "job_types": ["Full-time", "Contract", "Remote"]}, "application_settings": {"auto_apply": true, "apply_to_external": true, "max_applications_per_session": 50, "delay_between_applications": 30, "skip_questionnaires": false, "answer_questionnaires_with_ai": true}, "job_sites": {"linkedin": {"username": "<EMAIL>", "password": "Exit532493(*)"}, "indeed": {"username": "<EMAIL>", "password": ""}, "glassdoor": {"username": "<EMAIL>", "password": ""}}, "application": {"max_applications_per_run": 50}, "network": {"max_connections_per_run": 20}, "auto_reply": {"enabled": true}}