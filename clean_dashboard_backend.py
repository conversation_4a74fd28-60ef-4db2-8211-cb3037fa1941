#!/usr/bin/env python3
"""
Enhanced Web Dashboard Backend for LinkedIn Automation
Features: Pause/Resume controls, Question handling, Real-time updates
"""
import os
import json
import asyncio
import subprocess
import threading
import time
import queue
import signal
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import uvicorn
import logging

# Enhanced models
@dataclass
class ModuleState:
    """Enhanced module state tracking"""
    running: bool = False
    paused: bool = False
    process: Optional[subprocess.Popen] = None
    start_time: Optional[datetime] = None
    pause_time: Optional[datetime] = None
    stats: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.stats is None:
            self.stats = {}

class QuestionManager:
    """Manages questions from job application process"""
    
    def __init__(self):
        self.pending_questions = queue.Queue()
        self.responses = {}
        self.question_counter = 0
        
    def add_question(self, question_text: str, options: Optional[List[str]] = None, 
                    context: Optional[Dict[str, Any]] = None) -> str:
        """Add a question that needs user response"""
        self.question_counter += 1
        question_id = f"q_{self.question_counter}_{int(time.time())}"
        
        question = {
            "id": question_id,
            "text": question_text,
            "options": options or [],
            "context": context or {},
            "timestamp": datetime.now().isoformat(),
            "status": "pending"
        }
        
        self.pending_questions.put(question)
        return question_id
        
    def get_pending_questions(self) -> List[Dict[str, Any]]:
        """Get all pending questions"""
        questions = []
        temp_queue = queue.Queue()
        
        # Extract all questions without losing them
        while not self.pending_questions.empty():
            q = self.pending_questions.get()
            questions.append(q)
            if q["status"] == "pending":
                temp_queue.put(q)
                
        # Put unanswered questions back
        while not temp_queue.empty():
            self.pending_questions.put(temp_queue.get())
            
        return questions
        
    def answer_question(self, question_id: str, response: str) -> bool:
        """Provide answer to a question"""
        self.responses[question_id] = {
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
        
        # Mark question as answered
        questions = []
        found = False
        
        while not self.pending_questions.empty():
            q = self.pending_questions.get()
            if q["id"] == question_id:
                q["status"] = "answered"
                q["response"] = response
                found = True
            questions.append(q)
            
        # Put back unanswered questions only
        for q in questions:
            if q["status"] == "pending":
                self.pending_questions.put(q)
                
        return found

# Pydantic models for API
class ModuleCommand(BaseModel):
    action: str  # start, stop, pause, resume
    module: str

class QuestionResponse(BaseModel):
    question_id: str
    response: str

class DashboardAPI:
    """Enhanced API backend for the web dashboard"""
    
    def __init__(self):
        self.app = FastAPI(title="LinkedIn Automation Dashboard API")
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # Enhanced module states
        self.modules = {
            'job_apply': ModuleState(),
            'network': ModuleState(),
            'messages': ModuleState(),
            'content': ModuleState()
        }
        
        # Question manager
        self.question_manager = QuestionManager()
        
        # WebSocket connections
        self.websocket_connections: List[WebSocket] = []
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup templates
        if os.path.exists("templates"):
            self.templates = Jinja2Templates(directory="templates")
        else:
            self.templates = None
            
        # Setup routes
        self._setup_routes()
        
        # Start background monitoring
        self._start_background_monitoring()
        
    def _load_config(self):
        """Load config from JSON file with fallback"""
        config_file = 'linkedin_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        else:
            # Return default config
            return {
                "username": "",
                "password": "",
                "browser": {"chrome_binary_location": "/usr/bin/google-chrome-stable"},
                "resume_path": "",
                "openai_api_key": "",
                "search": {
                    "keywords": ["Software Engineer", "Developer"],
                    "easy_apply_only": True
                },
                "auto_reply": {"enabled": False},
                "application": {"max_applications_per_run": 50},
                "network": {"max_connections_per_run": 20},
                "delays": {
                    "page_load": 3,
                    "between_applications": 30
                }
            }
            
    def _setup_logger(self):
        """Enhanced logger setup"""
        Path("logs").mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/dashboard.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _start_background_monitoring(self):
        """Start background task for real-time updates"""
        def monitor_modules():
            while True:
                try:
                    self._update_module_stats()
                    self._broadcast_status_update()
                    time.sleep(5)  # Update every 5 seconds
                except Exception as e:
                    self.logger.error(f"Background monitoring error: {e}")
                    time.sleep(10)
                    
        monitor_thread = threading.Thread(target=monitor_modules, daemon=True)
        monitor_thread.start()
    
    def _update_module_stats(self):
        """Update statistics for all modules"""
        for module_name, state in self.modules.items():
            if state.running and state.process:
                # Check if process is still alive
                if state.process.poll() is not None:
                    # Process has ended
                    state.running = False
                    state.process = None
                    self.logger.info(f"Module {module_name} has stopped")
    
    def _broadcast_status_update(self):
        """Broadcast status updates to WebSocket clients"""
        if not self.websocket_connections:
            return
            
        status_data = {
            "timestamp": datetime.now().isoformat(),
            "modules": {
                name: {
                    "running": state.running,
                    "paused": state.paused,
                    "stats": state.stats
                }
                for name, state in self.modules.items()
            },
            "pending_questions": len(self.question_manager.get_pending_questions())
        }
        
        # Remove disconnected clients
        active_connections = []
        for websocket in self.websocket_connections:
            try:
                # We'll handle actual sending in the WebSocket endpoint
                active_connections.append(websocket)
            except:
                pass
        
        self.websocket_connections = active_connections
        
    def _setup_routes(self):
        """Setup all API routes"""
        
        @self.app.get("/")
        async def get_dashboard_stats():
            """Get current dashboard statistics"""
            return self.get_current_stats()
        
        @self.app.get("/dashboard")
        async def serve_dashboard(request: Request):
            """Serve the dashboard HTML"""
            if self.templates and os.path.exists("templates/dashboard.html"):
                return self.templates.TemplateResponse("dashboard.html", {"request": request})
            elif os.path.exists("web_dashboard_module.html"):
                with open("web_dashboard_module.html", "r") as f:
                    content = f.read()
                return HTMLResponse(content=content)
            else:
                raise HTTPException(status_code=404, detail="Dashboard HTML file not found")
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                while True:
                    # Send periodic updates
                    status_data = {
                        "timestamp": datetime.now().isoformat(),
                        "modules": {
                            name: {
                                "running": state.running,
                                "paused": state.paused,
                                "stats": state.stats
                            }
                            for name, state in self.modules.items()
                        },
                        "pending_questions": len(self.question_manager.get_pending_questions())
                    }
                    
                    await websocket.send_json(status_data)
                    await asyncio.sleep(5)  # Send update every 5 seconds
                    
            except WebSocketDisconnect:
                if websocket in self.websocket_connections:
                    self.websocket_connections.remove(websocket)
        
        @self.app.post("/api/modules/control")
        async def control_module(command: ModuleCommand):
            """Enhanced module control with pause/resume"""
            module_name = command.module
            action = command.action
            
            if module_name not in self.modules:
                raise HTTPException(status_code=404, detail="Module not found")
                
            state = self.modules[module_name]
            
            if action == "start":
                if state.running:
                    return {"status": "already_running"}
                    
                # Start module
                cmd = self._get_module_command(module_name)
                if cmd:
                    process = subprocess.Popen(cmd, shell=True)
                    state.running = True
                    state.paused = False
                    state.process = process
                    state.start_time = datetime.now()
                    
                    self.logger.info(f"Started {module_name} module")
                    return {"status": "started", "pid": process.pid}
                else:
                    return {"status": "error", "message": "Module command not found"}
                    
            elif action == "stop":
                if state.running and state.process:
                    state.process.terminate()
                    time.sleep(1)
                    if state.process.poll() is None:
                        state.process.kill()
                        
                state.running = False
                state.paused = False
                state.process = None
                
                self.logger.info(f"Stopped {module_name} module")
                return {"status": "stopped"}
                
            elif action == "pause":
                if state.running and not state.paused:
                    # Send pause signal to process
                    if state.process:
                        try:
                            os.kill(state.process.pid, signal.SIGUSR1)
                            state.paused = True
                            state.pause_time = datetime.now()
                            
                            self.logger.info(f"Paused {module_name} module")
                            return {"status": "paused"}
                        except:
                            return {"status": "error", "message": "Could not pause module"}
                            
                return {"status": "not_running_or_already_paused"}
                
            elif action == "resume":
                if state.running and state.paused:
                    # Send resume signal to process
                    if state.process:
                        try:
                            os.kill(state.process.pid, signal.SIGUSR2)
                            state.paused = False
                            state.pause_time = None
                            
                            self.logger.info(f"Resumed {module_name} module")
                            return {"status": "resumed"}
                        except:
                            return {"status": "error", "message": "Could not resume module"}
                            
                return {"status": "not_paused"}
                
            else:
                raise HTTPException(status_code=400, detail="Invalid action")
        
        @self.app.get("/api/questions")
        async def get_pending_questions():
            """Get questions waiting for user input"""
            return {
                "questions": self.question_manager.get_pending_questions(),
                "count": len(self.question_manager.get_pending_questions())
            }
        
        @self.app.post("/api/questions/answer")
        async def answer_question(response: QuestionResponse):
            """Answer a pending question"""
            success = self.question_manager.answer_question(
                response.question_id, 
                response.response
            )
            
            if success:
                return {"status": "answered"}
            else:
                raise HTTPException(status_code=404, detail="Question not found")
        
        @self.app.get("/api/modules/status")
        async def get_modules_status():
            """Get enhanced module statuses"""
            return {
                module: {
                    "running": state.running,
                    "paused": state.paused,
                    "pid": state.process.pid if state.process else None,
                    "start_time": state.start_time.isoformat() if state.start_time else None,
                    "pause_time": state.pause_time.isoformat() if state.pause_time else None,
                    "stats": state.stats
                }
                for module, state in self.modules.items()
            }
        
        @self.app.get("/api/applications")
        async def get_applications(limit: int = 50, offset: int = 0):
            """Get job applications with pagination"""
            return self.get_applications_data(limit, offset)
        
        @self.app.get("/api/network")
        async def get_network_stats():
            """Get network statistics"""
            return self.get_network_data()
        
        @self.app.get("/api/config")
        async def get_config():
            """Get current configuration"""
            return self.get_config_summary()
        
        @self.app.post("/api/config")
        async def update_config(config_update: Dict[str, Any]):
            """Update configuration"""
            return self.update_configuration(config_update)
        
        @self.app.get("/api/logs/{log_type}")
        async def get_logs(log_type: str, lines: int = 100):
            """Get recent logs"""
            return self.get_recent_logs(log_type, lines)
        
        @self.app.post("/api/emergency-stop")
        async def emergency_stop():
            """Stop all modules immediately"""
            stopped = []
            for module_name, state in self.modules.items():
                if state.running and state.process:
                    try:
                        state.process.terminate()
                        time.sleep(0.5)
                        if state.process.poll() is None:
                            state.process.kill()
                        stopped.append(module_name)
                    except:
                        pass
                        
                state.running = False
                state.paused = False
                state.process = None
                
            self.logger.warning(f"Emergency stop executed - stopped: {stopped}")
            return {"status": "emergency_stopped", "modules_stopped": stopped}
    
    def _get_module_command(self, module_name: str) -> Optional[str]:
        """Get command to start a specific module"""
        commands = {
            'job_apply': 'python enhanced_job_apply.py' if os.path.exists('enhanced_job_apply.py') else 'python main.py --apply',
            'network': 'python main.py --network',
            'messages': 'python main.py --messages',
            'content': 'python main.py --publish'
        }
        return commands.get(module_name)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current dashboard statistics"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "modules": {
                name: {
                    "running": state.running,
                    "paused": state.paused
                }
                for name, state in self.modules.items()
            },
            "applications": self.get_applications_summary(),
            "network": self.get_network_summary(),
            "pending_questions": len(self.question_manager.get_pending_questions())
        }
        
        return stats
    
    def get_applications_summary(self) -> Dict[str, Any]:
        """Get applications summary"""
        total_applications = 0
        recent_applications = []
        
        log_files = [
            'applications_log.json',
            'data/applications_log.json',
            'logs/applications.json'
        ]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            total_applications += len(data)
                            recent_applications.extend(data[-5:])
                except:
                    pass
                    
        return {
            "total": total_applications,
            "recent": recent_applications[-10:]
        }
    
    def get_network_summary(self) -> Dict[str, Any]:
        """Get network summary"""
        return {
            "connections_sent": 0,
            "connections_accepted": 0,
            "messages_sent": 0
        }
    
    def get_applications_data(self, limit: int, offset: int) -> Dict[str, Any]:
        """Get paginated applications data"""
        applications = []
        
        data_files = [
            'applications_log.json',
            'data/applications_log.json',
            'logs/applications.json'
        ]
        
        for filename in data_files:
            if os.path.exists(filename):
                try:
                    with open(filename, 'r') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            applications.extend(data)
                except:
                    pass
                    
        total = len(applications)
        paginated = applications[offset:offset + limit]
        
        return {
            "applications": paginated,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
    
    def get_network_data(self) -> Dict[str, Any]:
        """Get network statistics"""
        actions = []
        
        if os.path.exists('network_actions.json'):
            try:
                with open('network_actions.json', 'r') as f:
                    actions = json.load(f)
            except:
                pass
                
        return {
            "total_connections": len([a for a in actions if a.get("action_type") == "connect"]),
            "invitations_accepted": len([a for a in actions if a.get("action_type") == "accept"]),
            "messages_sent": len([a for a in actions if a.get("action_type", "").startswith("message_")]),
            "recent_actions": actions[-20:] if actions else []
        }
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        search = self.config.get('search', {})
        auto_reply = self.config.get('auto_reply', {})
        application = self.config.get('application', {})
        network = self.config.get('network', {})
        delays = self.config.get('delays', {})
        
        return {
            "username": self.config.get('username', ''),
            "has_password": bool(self.config.get('password')),
            "has_api_key": bool(self.config.get('openai_api_key')),
            "search_keywords": search.get('keywords', []),
            "easy_apply_only": search.get('easy_apply_only', True),
            "auto_reply_enabled": auto_reply.get('enabled', False),
            "max_applications": application.get('max_applications_per_run', 50),
            "max_connections": network.get('max_connections_per_run', 20),
            "delays": delays
        }
    
    def update_configuration(self, config_update: Dict[str, Any]) -> Dict[str, str]:
        """Update configuration settings"""
        try:
            # Update various config sections
            for key, value in config_update.items():
                if key in ["search_keywords"]:
                    if 'search' not in self.config:
                        self.config['search'] = {}
                    self.config['search']['keywords'] = value
                elif key in ["max_applications"]:
                    if 'application' not in self.config:
                        self.config['application'] = {}
                    self.config['application']['max_applications_per_run'] = value
                elif key in ["max_connections"]:
                    if 'network' not in self.config:
                        self.config['network'] = {}
                    self.config['network']['max_connections_per_run'] = value
                elif key in ["auto_reply_enabled"]:
                    if 'auto_reply' not in self.config:
                        self.config['auto_reply'] = {}
                    self.config['auto_reply']['enabled'] = value
                else:
                    self.config[key] = value
                
            # Save configuration
            with open('linkedin_config.json', 'w') as f:
                json.dump(self.config, f, indent=2)
                
            return {"status": "success", "message": "Configuration updated"}
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    def get_recent_logs(self, log_type: str, lines: int) -> List[str]:
        """Get recent log entries"""
        log_files = {
            'main': 'logs/dashboard.log',
            'applications': 'logs/applications.log',
            'network': 'logs/network.log',
            'errors': 'logs/errors.log'
        }
        
        log_file = log_files.get(log_type, 'logs/dashboard.log')
        
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    all_lines = f.readlines()
                    return [line.strip() for line in all_lines[-lines:]]
            except:
                pass
                
        return [f"No logs found for {log_type}"]
    
 