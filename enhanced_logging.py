#!/usr/bin/env python3
"""
Enhanced Logging System for LinkedIn Automation
Provides detailed logging with timestamps, run separation, and multiple log types
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field, asdict
from pathlib import Path

@dataclass
class JobApplication:
    """Detailed job application record"""
    job_id: str
    job_title: str
    company: str
    location: str
    job_url: str
    job_description_summary: str
    full_description: str
    resume_used: str
    cover_letter: str
    applied_date: str
    status: str
    salary_range: Optional[str] = None
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    skills_matched: List[str] = field(default_factory=list)
    
@dataclass 
class NetworkAction:
    """Network action record"""
    action_type: str  # 'connect', 'accept', 'message', 'maintain'
    profile_name: str
    profile_url: str
    message_sent: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    status: str = 'success'

class EnhancedLogger:
    """Enhanced logging system with multiple log types and run tracking"""
    
    def __init__(self, base_dir: str = "linkedin_logs"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # Create run-specific directory
        self.run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.base_dir / f"run_{self.run_id}"
        self.run_dir.mkdir(exist_ok=True)
        
        # Setup different log files
        self.setup_loggers()
        
        # Track current run stats
        self.run_stats = {
            "run_id": self.run_id,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "jobs_applied": 0,
            "jobs_skipped": 0,
            "errors": 0,
            "network_actions": 0
        }
        
    def setup_loggers(self):
        """Setup different logger instances"""
        # Main logger
        self.main_logger = self._create_logger(
            'main', 
            self.run_dir / 'main.log',
            self.base_dir / 'continuous_main.log'
        )
        
        # Application logger
        self.app_logger = self._create_logger(
            'applications',
            self.run_dir / 'applications.log', 
            self.base_dir / 'continuous_applications.log'
        )
        
        # Error logger
        self.error_logger = self._create_logger(
            'errors',
            self.run_dir / 'errors.log',
            self.base_dir / 'continuous_errors.log',
            level=logging.ERROR
        )
        
        # Network logger
        self.network_logger = self._create_logger(
            'network',
            self.run_dir / 'network.log',
            self.base_dir / 'continuous_network.log'
        )
        
    def _create_logger(self, name: str, run_file: Path, continuous_file: Path, 
                      level=logging.INFO) -> logging.Logger:
        """Create a logger with both run-specific and continuous handlers"""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # Clear existing handlers
        logger.handlers = []
        
        # Formatter with detailed timestamp
        formatter = logging.Formatter(
            '%(asctime)s | %(name)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Run-specific handler
        run_handler = logging.FileHandler(run_file)
        run_handler.setFormatter(formatter)
        logger.addHandler(run_handler)
        
        # Continuous handler
        cont_handler = logging.FileHandler(continuous_file)
        cont_handler.setFormatter(formatter)
        logger.addHandler(cont_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
        
    def start_run(self):
        """Mark the start of a new run"""
        self.main_logger.info(f"{'='*60}")
        self.main_logger.info(f"STARTING NEW RUN - ID: {self.run_id}")
        self.main_logger.info(f"{'='*60}")
        
    def end_run(self):
        """Mark the end of a run and save summary"""
        self.run_stats["end_time"] = datetime.now().isoformat()
        
        # Calculate duration
        start = datetime.fromisoformat(self.run_stats["start_time"])
        end = datetime.fromisoformat(self.run_stats["end_time"])
        duration = (end - start).total_seconds()
        self.run_stats["duration_seconds"] = duration
        self.run_stats["duration_formatted"] = f"{duration/3600:.2f} hours"
        
        # Log summary
        self.main_logger.info(f"{'='*60}")
        self.main_logger.info(f"RUN COMPLETE - ID: {self.run_id}")
        self.main_logger.info(f"Duration: {self.run_stats['duration_formatted']}")
        self.main_logger.info(f"Jobs Applied: {self.run_stats['jobs_applied']}")
        self.main_logger.info(f"Jobs Skipped: {self.run_stats['jobs_skipped']}")
        self.main_logger.info(f"Errors: {self.run_stats['errors']}")
        self.main_logger.info(f"Network Actions: {self.run_stats['network_actions']}")
        self.main_logger.info(f"{'='*60}")
        
        # Save run summary
        summary_file = self.run_dir / 'run_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(self.run_stats, f, indent=2)
            
        # Update master summary
        self._update_master_summary()
        
    def _update_master_summary(self):
        """Update the master summary file with all runs"""
        master_file = self.base_dir / 'master_summary.json'
        
        if master_file.exists():
            with open(master_file, 'r') as f:
                master_data = json.load(f)
        else:
            master_data = {"runs": [], "total_applications": 0, "total_errors": 0}
            
        master_data["runs"].append(self.run_stats)
        master_data["total_applications"] += self.run_stats["jobs_applied"]
        master_data["total_errors"] += self.run_stats["errors"]
        
        with open(master_file, 'w') as f:
            json.dump(master_data, f, indent=2)
            
    def log_job_application(self, job: JobApplication):
        """Log a detailed job application"""
        self.run_stats["jobs_applied"] += 1
        
        # Create detailed log entry
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "run_id": self.run_id,
            **asdict(job)
        }
        
        # Log to application file
        self.app_logger.info(f"Applied to: {job.job_title} at {job.company}")
        
        # Save detailed JSON
        apps_file = self.run_dir / 'applications.json'
        if apps_file.exists():
            with open(apps_file, 'r') as f:
                apps = json.load(f)
        else:
            apps = []
            
        apps.append(log_entry)
        
        with open(apps_file, 'w') as f:
            json.dump(apps, f, indent=2)
            
        # Also update continuous applications file
        cont_file = self.base_dir / 'all_applications.json'
        if cont_file.exists():
            with open(cont_file, 'r') as f:
                all_apps = json.load(f)
        else:
            all_apps = []
            
        all_apps.append(log_entry)
        
        with open(cont_file, 'w') as f:
            json.dump(all_apps, f, indent=2)
            
    def log_skipped_job(self, job_url: str, reason: str, job_title: str = "", 
                       company: str = ""):
        """Log a skipped job"""
        self.run_stats["jobs_skipped"] += 1
        
        entry = {
            "timestamp": datetime.now().isoformat(),
            "run_id": self.run_id,
            "job_url": job_url,
            "job_title": job_title,
            "company": company,
            "reason": reason
        }
        
        self.main_logger.info(f"Skipped job: {job_title or job_url} - Reason: {reason}")
        
        # Save to skipped file
        skipped_file = self.run_dir / 'skipped_jobs.json'
        if skipped_file.exists():
            with open(skipped_file, 'r') as f:
                skipped = json.load(f)
        else:
            skipped = []
            
        skipped.append(entry)
        
        with open(skipped_file, 'w') as f:
            json.dump(skipped, f, indent=2)
            
    def log_error(self, error_msg: str, error_type: str = "general", 
                  context: Optional[Dict[str, Any]] = None):
        """Log an error with context"""
        self.run_stats["errors"] += 1
        
        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "run_id": self.run_id,
            "error_type": error_type,
            "message": error_msg,
            "context": context or {}
        }
        
        self.error_logger.error(f"{error_type}: {error_msg}")
        
        # Save detailed error
        errors_file = self.run_dir / 'errors.json'
        if errors_file.exists():
            with open(errors_file, 'r') as f:
                errors = json.load(f)
        else:
            errors = []
            
        errors.append(error_entry)
        
        with open(errors_file, 'w') as f:
            json.dump(errors, f, indent=2)
            
    def log_network_action(self, action: NetworkAction):
        """Log a network action"""
        self.run_stats["network_actions"] += 1
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "run_id": self.run_id,
            **asdict(action)
        }
        
        self.network_logger.info(
            f"{action.action_type}: {action.profile_name}"
        )
        
        # Save network actions
        network_file = self.run_dir / 'network_actions.json'
        if network_file.exists():
            with open(network_file, 'r') as f:
                actions = json.load(f)
        else:
            actions = []
            
        actions.append(log_entry)
        
        with open(network_file, 'w') as f:
            json.dump(actions, f, indent=2)
            
    def info(self, message: str):
        """General info logging"""
        self.main_logger.info(message)
        
    def warning(self, message: str):
        """Warning logging"""
        self.main_logger.warning(message)
        
    def debug(self, message: str):
        """Debug logging"""
        self.main_logger.debug(message)
        
    def extract_job_description_summary(self, full_description: str, 
                                      max_length: int = 500) -> str:
        """Extract key points from job description"""
        # Simple extraction - can be enhanced with NLP
        lines = full_description.split('\n')
        summary_lines = []
        current_length = 0
        
        # Prioritize lines with keywords
        priority_keywords = [
            'required', 'must have', 'qualification', 'responsibility',
            'experience', 'skill', 'years', 'degree'
        ]
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check for priority keywords
            has_priority = any(keyword in line.lower() for keyword in priority_keywords)
            
            if has_priority or current_length < max_length:
                summary_lines.append(line)
                current_length += len(line)
                
            if current_length >= max_length:
                break
                
        return '\n'.join(summary_lines[:10])  # Max 10 lines
        
    def get_run_summary(self) -> Dict[str, Any]:
        """Get current run summary"""
        return self.run_stats.copy()
        
    def get_recent_applications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent job applications"""
        apps_file = self.base_dir / 'all_applications.json'
        if not apps_file.exists():
            return []
            
        with open(apps_file, 'r') as f:
            apps = json.load(f)
            
        return apps[-limit:]
        
    def search_applications(self, company: Optional[str] = None, job_title: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search through applications"""
        apps_file = self.base_dir / 'all_applications.json'
        if not apps_file.exists():
            return []
            
        with open(apps_file, 'r') as f:
            apps = json.load(f)
            
        results = []
        for app in apps:
            if company and company.lower() not in app.get('company', '').lower():
                continue
            if job_title and job_title.lower() not in app.get('job_title', '').lower():
                continue
            results.append(app)
            
        return results