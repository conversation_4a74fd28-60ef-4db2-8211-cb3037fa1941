#!/usr/bin/env python3
"""
Working Web Dashboard Backend for LinkedIn Automation
Simple, clean dashboard that actually works
"""
import os
import json
import subprocess
import threading
import time
import queue
import signal
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, field
from fastapi import FastAPI, HTTPException, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn
import logging

# Models
@dataclass
class ModuleState:
    running: bool = False
    paused: bool = False
    process: Optional[subprocess.Popen] = None
    start_time: Optional[datetime] = None
    stats: Dict[str, Any] = field(default_factory=dict)

class ModuleCommand(BaseModel):
    action: str  # start, stop, pause, resume
    module: str

class QuestionResponse(BaseModel):
    question_id: str
    response: str

class QuestionManager:
    def __init__(self):
        self.pending_questions = []
        self.responses = {}
        self.question_counter = 0
        
    def add_question(self, question_text: str, options: Optional[List[str]] = None) -> str:
        self.question_counter += 1
        question_id = f"q_{self.question_counter}_{int(time.time())}"
        
        question = {
            "id": question_id,
            "text": question_text,
            "options": options or [],
            "timestamp": datetime.now().isoformat(),
            "status": "pending"
        }
        
        self.pending_questions.append(question)
        return question_id
        
    def get_pending_questions(self) -> List[Dict[str, Any]]:
        return [q for q in self.pending_questions if q["status"] == "pending"]
        
    def answer_question(self, question_id: str, response: str) -> bool:
        for q in self.pending_questions:
            if q["id"] == question_id:
                q["status"] = "answered"
                q["response"] = response
                self.responses[question_id] = response
                return True
        return False

class DashboardAPI:
    def __init__(self):
        self.app = FastAPI(title="LinkedIn Automation Dashboard")
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # Module states
        self.modules = {
            'job_apply': ModuleState(),
            'network': ModuleState(),
            'messages': ModuleState(),
            'content': ModuleState()
        }
        
        self.question_manager = QuestionManager()
        self.websocket_connections: List[WebSocket] = []
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_routes()
        self._start_monitoring()
        
    def _load_config(self):
        config_file = 'linkedin_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {
            "username": "",
            "password": "",
            "search": {"keywords": ["Software Engineer"]},
            "application": {"max_applications_per_run": 50}
        }
            
    def _setup_logger(self):
        Path("logs").mkdir(exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/dashboard.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _start_monitoring(self):
        def monitor():
            while True:
                try:
                    for module_name, state in self.modules.items():
                        if state.running and state.process:
                            if state.process.poll() is not None:
                                state.running = False
                                state.process = None
                                self.logger.info(f"Module {module_name} stopped")
                    time.sleep(5)
                except Exception as e:
                    self.logger.error(f"Monitor error: {e}")
                    time.sleep(10)
                    
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        
    def _setup_routes(self):
        @self.app.get("/")
        async def root():
            return {"message": "LinkedIn Automation Dashboard API"}
        
        @self.app.get("/dashboard")
        async def serve_dashboard():
            return HTMLResponse(content=self.get_dashboard_html())
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                while True:
                    status_data = {
                        "timestamp": datetime.now().isoformat(),
                        "modules": {
                            name: {
                                "running": state.running,
                                "paused": state.paused,
                                "stats": state.stats
                            }
                            for name, state in self.modules.items()
                        },
                        "pending_questions": len(self.question_manager.get_pending_questions())
                    }
                    
                    await websocket.send_json(status_data)
                    await asyncio.sleep(5)
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
        
        @self.app.post("/api/modules/control")
        async def control_module(command: ModuleCommand):
            module_name = command.module
            action = command.action
            
            if module_name not in self.modules:
                raise HTTPException(status_code=404, detail="Module not found")
                
            state = self.modules[module_name]
            
            if action == "start":
                if state.running:
                    return {"status": "already_running"}
                    
                cmd = self._get_module_command(module_name)
                if cmd:
                    process = subprocess.Popen(cmd, shell=True)
                    state.running = True
                    state.paused = False
                    state.process = process
                    state.start_time = datetime.now()
                    
                    self.logger.info(f"Started {module_name}")
                    return {"status": "started", "pid": process.pid}
                else:
                    return {"status": "error", "message": "Command not found"}
                    
            elif action == "stop":
                if state.running and state.process:
                    state.process.terminate()
                    time.sleep(1)
                    if state.process.poll() is None:
                        state.process.kill()
                        
                state.running = False
                state.paused = False
                state.process = None
                
                self.logger.info(f"Stopped {module_name}")
                return {"status": "stopped"}
                
            elif action == "pause":
                if state.running and not state.paused and state.process:
                    try:
                        os.kill(state.process.pid, signal.SIGUSR1)
                        state.paused = True
                        self.logger.info(f"Paused {module_name}")
                        return {"status": "paused"}
                    except:
                        return {"status": "error", "message": "Could not pause"}
                return {"status": "not_running"}
                
            elif action == "resume":
                if state.running and state.paused and state.process:
                    try:
                        os.kill(state.process.pid, signal.SIGUSR2)
                        state.paused = False
                        self.logger.info(f"Resumed {module_name}")
                        return {"status": "resumed"}
                    except:
                        return {"status": "error", "message": "Could not resume"}
                return {"status": "not_paused"}
        
        @self.app.get("/api/modules/status")
        async def get_status():
            return {
                module: {
                    "running": state.running,
                    "paused": state.paused,
                    "pid": state.process.pid if state.process else None,
                    "start_time": state.start_time.isoformat() if state.start_time else None
                }
                for module, state in self.modules.items()
            }
        
        @self.app.get("/api/questions")
        async def get_questions():
            return {
                "questions": self.question_manager.get_pending_questions(),
                "count": len(self.question_manager.get_pending_questions())
            }
        
        @self.app.post("/api/questions/answer")
        async def answer_question(response: QuestionResponse):
            success = self.question_manager.answer_question(response.question_id, response.response)
            if success:
                return {"status": "answered"}
            else:
                raise HTTPException(status_code=404, detail="Question not found")
        
        @self.app.post("/api/emergency-stop")
        async def emergency_stop():
            stopped = []
            for module_name, state in self.modules.items():
                if state.running and state.process:
                    try:
                        state.process.terminate()
                        stopped.append(module_name)
                    except:
                        pass
                state.running = False
                state.paused = False
                state.process = None
                
            self.logger.warning(f"Emergency stop: {stopped}")
            return {"status": "emergency_stopped", "modules_stopped": stopped}
    
    def _get_module_command(self, module_name: str) -> Optional[str]:
        commands = {
            'job_apply': 'python enhanced_job_apply.py' if os.path.exists('enhanced_job_apply.py') else 'python main.py --apply',
            'network': 'python main.py --network',
            'messages': 'python main.py --messages',
            'content': 'python main.py --publish'
        }
        return commands.get(module_name)
    
    def get_dashboard_html(self) -> str:
        return """
<!DOCTYPE html>
<html>
<head>
    <title>LinkedIn Automation Dashboard</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: #f5f5f5; 
            color: #333;
        }
        .header { 
            background: linear-gradient(135deg, #0077b5, #005885); 
            color: white; 
            padding: 2rem; 
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 2rem; 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem; 
        }
        .stat-card { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            border-left: 4px solid #0077b5;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #0077b5;
            margin: 0.5rem 0;
        }
        .controls { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            margin-bottom: 2rem; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .module-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 1rem; 
            margin-top: 1rem;
        }
        .module-card { 
            background: #f8f9fa; 
            padding: 1.5rem; 
            border-radius: 10px; 
            border: 1px solid #dee2e6;
        }
        .btn { 
            padding: 0.75rem 1.5rem; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 0.25rem; 
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 10px rgba(0,0,0,0.2); }
        .status { 
            display: inline-block; 
            padding: 0.25rem 0.75rem; 
            border-radius: 15px; 
            font-size: 0.875rem; 
            font-weight: bold; 
            margin-bottom: 1rem;
        }
        .status-running { background: #d4edda; color: #155724; }
        .status-stopped { background: #f8d7da; color: #721c24; }
        .status-paused { background: #fff3cd; color: #856404; }
        .questions-panel { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            margin-bottom: 2rem; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #ffc107;
            display: none;
        }
        .question {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        .emergency-btn {
            background: #dc3545;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: block;
            margin: 2rem auto;
            transition: all 0.3s ease;
        }
        .emergency-btn:hover {
            background: #c82333;
            transform: scale(1.05);
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            display: none;
        }
        .notification.success { background: #28a745; }
        .notification.error { background: #dc3545; }
        .notification.info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 LinkedIn Automation Dashboard</h1>
        <p>Control your automation modules with pause/resume support</p>
    </div>
    
    <div class="container">
        <!-- Status Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📝 Applications</h3>
                <div class="stat-number" id="totalApplications">0</div>
                <small>Total submitted</small>
            </div>
            <div class="stat-card">
                <h3>🤝 Network</h3>
                <div class="stat-number" id="networkConnections">0</div>
                <small>Connections made</small>
            </div>
            <div class="stat-card">
                <h3>❓ Questions</h3>
                <div class="stat-number" id="pendingQuestions">0</div>
                <small>Awaiting response</small>
            </div>
            <div class="stat-card">
                <h3>⚡ Status</h3>
                <div class="stat-number" id="systemStatus">Ready</div>
                <small>System status</small>
            </div>
        </div>
        
        <!-- Questions Panel -->
        <div class="questions-panel" id="questionsPanel">
            <h3>❓ Job Application Questions</h3>
            <div id="questionsList"></div>
        </div>
        
        <!-- Module Controls -->
        <div class="controls">
            <h3>🎮 Module Controls</h3>
            <div class="module-grid">
                <div class="module-card">
                    <h4>📝 Job Applications</h4>
                    <div>Status: <span class="status status-stopped" id="jobApplyStatus">Stopped</span></div>
                    <div>
                        <button class="btn btn-success" onclick="controlModule('job_apply', 'start')">▶️ Start</button>
                        <button class="btn btn-warning" onclick="controlModule('job_apply', 'pause')">⏸️ Pause</button>
                        <button class="btn btn-secondary" onclick="controlModule('job_apply', 'resume')">⏯️ Resume</button>
                        <button class="btn btn-danger" onclick="controlModule('job_apply', 'stop')">⏹️ Stop</button>
                    </div>
                </div>
                
                <div class="module-card">
                    <h4>🤝 Network Expansion</h4>
                    <div>Status: <span class="status status-stopped" id="networkStatus">Stopped</span></div>
                    <div>
                        <button class="btn btn-success" onclick="controlModule('network', 'start')">▶️ Start</button>
                        <button class="btn btn-warning" onclick="controlModule('network', 'pause')">⏸️ Pause</button>
                        <button class="btn btn-secondary" onclick="controlModule('network', 'resume')">⏯️ Resume</button>
                        <button class="btn btn-danger" onclick="controlModule('network', 'stop')">⏹️ Stop</button>
                    </div>
                </div>
                
                <div class="module-card">
                    <h4>💬 Messages</h4>
                    <div>Status: <span class="status status-stopped" id="messagesStatus">Stopped</span></div>
                    <div>
                        <button class="btn btn-success" onclick="controlModule('messages', 'start')">▶️ Start</button>
                        <button class="btn btn-warning" onclick="controlModule('messages', 'pause')">⏸️ Pause</button>
                        <button class="btn btn-secondary" onclick="controlModule('messages', 'resume')">⏯️ Resume</button>
                        <button class="btn btn-danger" onclick="controlModule('messages', 'stop')">⏹️ Stop</button>
                    </div>
                </div>
                
                <div class="module-card">
                    <h4>📄 Content</h4>
                    <div>Status: <span class="status status-stopped" id="contentStatus">Stopped</span></div>
                    <div>
                        <button class="btn btn-success" onclick="controlModule('content', 'start')">▶️ Start</button>
                        <button class="btn btn-warning" onclick="controlModule('content', 'pause')">⏸️ Pause</button>
                        <button class="btn btn-secondary" onclick="controlModule('content', 'resume')">⏯️ Resume</button>
                        <button class="btn btn-danger" onclick="controlModule('content', 'stop')">⏹️ Stop</button>
                    </div>
                </div>
            </div>
        </div>
        
        <button class="emergency-btn" onclick="emergencyStop()">
            🚨 EMERGENCY STOP ALL MODULES
        </button>
    </div>
    
    <div class="notification" id="notification"></div>
    
    <script>
        const API_BASE = window.location.origin;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            setInterval(loadDashboardData, 5000); // Refresh every 5 seconds
        });
        
        // Load dashboard data
        async function loadDashboardData() {
            try {
                const statusResponse = await fetch(`${API_BASE}/api/modules/status`);
                const statusData = await statusResponse.json();
                updateModuleStatuses(statusData);
                
                const questionsResponse = await fetch(`${API_BASE}/api/questions`);
                const questionsData = await questionsResponse.json();
                updateQuestions(questionsData.questions);
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }
        
        // Control module function
        async function controlModule(module, action) {
            try {
                showNotification(`${action}ing ${module}...`, 'info');
                
                const response = await fetch(`${API_BASE}/api/modules/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ module: module, action: action })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showNotification(`${module} ${action} successful!`, 'success');
                    loadDashboardData();
                } else {
                    showNotification(`Error: ${result.detail || result.message}`, 'error');
                }
                
            } catch (error) {
                console.error(`Error ${action} ${module}:`, error);
                showNotification(`Error ${action}ing ${module}`, 'error');
            }
        }
        
        // Emergency stop
        async function emergencyStop() {
            if (!confirm('Are you sure you want to STOP ALL modules immediately?')) return;
            
            try {
                const response = await fetch(`${API_BASE}/api/emergency-stop`, { method: 'POST' });
                if (response.ok) {
                    const result = await response.json();
                    showNotification(`Emergency stop executed!`, 'info');
                    loadDashboardData();
                }
            } catch (error) {
                console.error('Emergency stop error:', error);
                showNotification('Emergency stop failed', 'error');
            }
        }
        
        // Update module status displays
        function updateModuleStatuses(statuses) {
            const moduleIds = {
                'job_apply': 'jobApplyStatus',
                'network': 'networkStatus',
                'messages': 'messagesStatus',
                'content': 'contentStatus'
            };
            
            for (const [module, elementId] of Object.entries(moduleIds)) {
                const element = document.getElementById(elementId);
                const status = statuses[module];
                
                if (status) {
                    let statusText = 'Stopped';
                    let statusClass = 'status-stopped';
                    
                    if (status.running && status.paused) {
                        statusText = 'Paused';
                        statusClass = 'status-paused';
                    } else if (status.running) {
                        statusText = 'Running';
                        statusClass = 'status-running';
                    }
                    
                    element.textContent = statusText;
                    element.className = `status ${statusClass}`;
                }
            }
        }
        
        // Update questions
        function updateQuestions(questions) {
            const questionsPanel = document.getElementById('questionsPanel');
            const questionsList = document.getElementById('questionsList');
            const pendingCount = document.getElementById('pendingQuestions');
            
            pendingCount.textContent = questions.length;
            
            if (questions.length > 0) {
                questionsPanel.style.display = 'block';
                
                questionsList.innerHTML = questions.map(q => `
                    <div class="question">
                        <h5>${q.text}</h5>
                        ${q.options && q.options.length > 0 ? `
                            <div style="margin: 1rem 0;">
                                ${q.options.map(option => `
                                    <button class="btn btn-secondary" style="margin-right: 0.5rem;" 
                                            onclick="answerQuestion('${q.id}', '${option}')">
                                        ${option}
                                    </button>
                                `).join('')}
                            </div>
                        ` : `
                            <div style="margin: 1rem 0;">
                                <input type="text" id="answer_${q.id}" placeholder="Enter your answer..." 
                                       style="padding: 0.5rem; width: 300px; margin-right: 0.5rem;">
                                <button class="btn btn-success" onclick="answerQuestionText('${q.id}')">
                                    Submit Answer
                                </button>
                            </div>
                        `}
                    </div>
                `).join('');
                
            } else {
                questionsPanel.style.display = 'none';
            }
        }
        
        // Answer question
        async function answerQuestion(questionId, answer) {
            try {
                const response = await fetch(`${API_BASE}/api/questions/answer`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question_id: questionId, response: answer })
                });
                
                if (response.ok) {
                    showNotification('Answer submitted!', 'success');
                    loadDashboardData();
                } else {
                    showNotification('Error submitting answer', 'error');
                }
                
            } catch (error) {
                console.error('Error answering question:', error);
                showNotification('Error submitting answer', 'error');
            }
        }
        
        // Answer question with text input
        async function answerQuestionText(questionId) {
            const input = document.getElementById(`answer_${questionId}`);
            const answer = input.value.trim();
            
            if (!answer) {
                showNotification('Please enter an answer', 'error');
                return;
            }
            
            await answerQuestion(questionId, answer);
        }
        
        // Show notification
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
        """

class DashboardServer:
    def __init__(self, config=None, logger=None):
        self.api = DashboardAPI()
        
    def run(self, host: str = "127.0.0.1", port: int = 8000):
        print(f"🚀 Starting LinkedIn Automation Dashboard")
        print(f"📊 Dashboard URL: http://{host}:{port}/dashboard")
        print(f"📡 API docs: http://{host}:{port}/docs")
        print("Press Ctrl+C to stop")
        uvicorn.run(self.api.app, host=host, port=port, log_level="info")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="LinkedIn Automation Dashboard")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    
    args = parser.parse_args()
    
    api = DashboardAPI()
    print(f"🚀 Starting LinkedIn Automation Dashboard")
    print(f"📊 Dashboard URL: http://{args.host}:{args.port}/dashboard")
    print(f"📡 API docs: http://{args.host}:{args.port}/docs")
    print("Press Ctrl+C to stop")
    
    uvicorn.run(api.app, host=args.host, port=args.port, log_level="info")