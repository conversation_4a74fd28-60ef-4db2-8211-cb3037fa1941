#!/usr/bin/env python3
"""
Enhanced Modular Job Application Module - Refactored Version
Cleaner implementation using utilities module
"""
import os
import sys
import time
import json
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Import utilities
from job_application_utils import (
    ElementInteractor,
    FormAnalyzer,
    FieldMatcher,
    ApplicationValidator,
    LinkedInSelectors,
    ApplicationConfig
)

# Import enhanced modules if available
try:
    from enhanced_logging import EnhancedLogger, JobApplication
    ENHANCED_MODULES = True
except ImportError:
    ENHANCED_MODULES = False

# Define fallback classes for enhanced_config
class LinkedInConfig:
    """Fallback LinkedInConfig class"""
    pass

class ConfigManager:
    """Fallback ConfigManager class"""
    pass

# Try to import actual implementations if available
try:
    from enhanced_config import LinkedInConfig, ConfigManager  # type: ignore
except ImportError:
    pass  # Use fallback classes defined above


class ModularJobApplicator:
    """Refactored job application handler with cleaner architecture"""
    
    # Updated selectors for current LinkedIn UI
    UPDATED_SELECTORS = {
        "job_cards": [
            "li[data-occludable-job-id]",
            "div[data-job-id]", 
            ".job-card-container",
            ".jobs-search-results__list-item"
        ],
        "search_keywords": [
            "input[aria-label='Search by title, skill, or company']",
            "input[placeholder*='Search by title']",
            "input.jobs-search-box__text-input"
        ],
        "search_location": [
            "input[aria-label='Location']",
            "input[placeholder*='Location']",
            "input[aria-label*='City, state, or zip code']"
        ],
        "job_title": [
            "h3.job-card-list__title",
            "a[data-control-name='job_card_title']",
            ".job-card-square__title",
            "h3.base-search-card__title"
        ],
        "job_company": [
            "h4.job-card-container__company-name",
            "a[data-control-name='job_card_company']",
            ".job-card-container__primary-description",
            "h4.base-search-card__subtitle"
        ]
    }

    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
        self.driver = None
        self.wait = None
        
        # Initialize utilities
        self.app_config = self._load_app_config()
        self.selectors = LinkedInSelectors()
        
        # Stats tracking
        self.stats = {
            'applied': 0,
            'errors': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Application state
        if self.config is None:
            self.demo_mode = True
        elif isinstance(self.config, dict):
            self.demo_mode = self.config.get('application', {}).get('demo_mode', True)
        else:
            self.demo_mode = getattr(self.config, 'demo_mode', True)
        self.warning_detected = False
        self.consecutive_errors = 0
        self.last_application_time = None
        
        # Load questions database
        self.questions_db = self._load_questions_db()
        
    def _load_app_config(self) -> ApplicationConfig:
        """Load application configuration"""
        config_dict = {}
        
        if isinstance(self.config, dict):
            config_dict = {
                'delays': {
                    'click': self.config.get('delays', {}).get('click_delay', [0.5, 1.0]),
                    'typing': self.config.get('delays', {}).get('typing_delay', [0.05, 0.15]),
                    'page_load': self.config.get('delays', {}).get('page_load', 3.0),
                    'form_submit': self.config.get('delays', {}).get('form_submit', 2.0)
                },
                'retries': {
                    'click': self.config.get('retries', {}).get('max_click_attempts', 3),
                    'form': self.config.get('retries', {}).get('max_form_attempts', 2)
                },
                'features': {
                    'dismiss_modals': self.config.get('features', {}).get('auto_dismiss_modals', True),
                    'auto_scroll': self.config.get('features', {}).get('auto_scroll', True),
                    'use_js_fallback': self.config.get('features', {}).get('javascript_fallback', True)
                }
            }
        
        return ApplicationConfig.from_dict(config_dict)
    
    def _load_questions_db(self) -> Dict[str, str]:
        """Load saved questions and answers"""
        questions_file = self.get('questions_log_path', 'data/questions_log.json')
        
        if questions_file and os.path.exists(questions_file):
            try:
                with open(questions_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.log_error(f"Failed to load questions database: {e}")
        
        return {}
    
    def _save_question_answer(self, question: str, answer: str):
        """Save a question-answer pair"""
        self.questions_db[question] = answer
        
        questions_file = self.get('questions_log_path', 'data/questions_log.json')
        if questions_file:
            os.makedirs(os.path.dirname(questions_file), exist_ok=True)
            
            try:
                with open(questions_file, 'w') as f:
                    json.dump(self.questions_db, f, indent=2)
            except Exception as e:
                self.log_error(f"Failed to save question: {e}")
    
    # Logging methods
    def log_info(self, msg: str):
        """Log info message"""
        if self.logger and hasattr(self.logger, 'info'):
            self.logger.info(msg)
        else:
            print(f"[INFO] {msg}")
    
    def log_error(self, msg: str):
        """Log error message"""
        if self.logger and hasattr(self.logger, 'error'):
            self.logger.error(msg)
        else:
            print(f"[ERROR] {msg}")
    
    def log_warning(self, msg: str):
        """Log warning message"""
        if self.logger and hasattr(self.logger, 'warning'):
            self.logger.warning(msg)
        else:
            print(f"[WARNING] {msg}")
    
    # Configuration access
    def get(self, key: str, default=None):
        """Get config value safely"""
        if isinstance(self.config, dict):
            return self.config.get(key, default)
        else:
            return getattr(self.config, key, default)
    
    # Browser management
    def start_browser(self):
        """Initialize browser with configuration"""
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        opts = Options()
        
        # Chrome binary location
        chrome_binary = self.get('chrome_binary_location', '/usr/bin/google-chrome-stable')
        if chrome_binary and os.path.exists(chrome_binary):
            opts.binary_location = chrome_binary
        
        # Browser options
        if self.get('headless', False):
            opts.add_argument('--headless=new')
        
        opts.add_argument('--disable-gpu')
        opts.add_argument('--no-sandbox')
        opts.add_argument('--disable-dev-shm-usage')
        opts.add_argument('--window-size=1920,1080')
        
        # Start browser
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=opts)
        self.driver.maximize_window()
        self.wait = WebDriverWait(self.driver, self.app_config.page_timeout)
        
        # Initialize utilities with driver
        self.interactor = ElementInteractor(self.driver, self.app_config, self.logger)
        self.analyzer = FormAnalyzer(self.driver, self.logger)
        self.validator = ApplicationValidator(self.driver, self.selectors)
        
        # Initialize field matcher with user data
        user_data = self._get_user_data()
        self.matcher = FieldMatcher(user_data, self.questions_db)
        
        self.log_info("Browser initialized successfully")
        return self.driver
    
    def _get_user_data(self) -> Dict[str, Any]:
        """Get user data for form filling"""
        # Handle case when config is None
        if self.config is None:
            return {
                'username': '',
                'email': '',
                'phone': '',
                'name': '',
                'location': '',
                'years_experience': '10',
                'salary': '80000',
                'notice_period': '2 weeks',
                'willing_to_relocate': 'No',
                'requires_sponsorship': 'No',
                'user_profile': {}
            }
        
        user_profile = self.get('user_profile', {}) or {}
        return {
            'username': self.get('username', ''),
            'email': self.get('username', ''),
            'phone': user_profile.get('phone', ''),
            'name': user_profile.get('name', ''),
            'location': user_profile.get('location', ''),
            'years_experience': user_profile.get('years_experience', '10'),
            'salary': user_profile.get('expected_salary', '80000'),
            'notice_period': user_profile.get('notice_period', '2 weeks'),
            'willing_to_relocate': user_profile.get('willing_to_relocate', 'No'),
            'requires_sponsorship': user_profile.get('requires_sponsorship', 'No'),
            'user_profile': user_profile
        }
    
    def login(self, username: str, password: str) -> bool:
        """LinkedIn login"""
        try:
            if self.driver is None:
                self.log_error("Browser not initialized. Call start_browser() first.")
                return False
                
            if self.wait is None:
                self.log_error("WebDriverWait not initialized. Call start_browser() first.")
                return False
                
            self.log_info("Navigating to LinkedIn login...")
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(2)
            
            # Enter credentials
            username_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            password_field = self.driver.find_element(By.ID, "password")
            
            self.interactor.safe_send_keys(username_field, username)
            self.interactor.safe_send_keys(password_field, password)
            
            # Submit
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            self.interactor.safe_click(login_button)
            
            # Wait for navigation
            time.sleep(5)
            
            # Check if logged in
            if "feed" in self.driver.current_url or "in/" in self.driver.current_url:
                self.log_info("Login successful!")
                return True
            else:
                self.log_error("Login failed - unexpected URL")
                return False
                
        except Exception as e:
            self.log_error(f"Login error: {str(e)}")
            return False
    
    def set_mode(self, demo: bool = True):
        """Set application mode"""
        self.demo_mode = demo
        mode = "DEMO" if demo else "LIVE"
        self.log_info(f"Running in {mode} mode")
        
        if not demo:
            print("\n" + "="*60)
            print("⚠️  WARNING: LIVE MODE - REAL APPLICATIONS WILL BE SUBMITTED")
            print("="*60)
            response = input("Are you SURE you want to submit real applications? (yes/no): ")
            if response.lower() != 'yes':
                self.log_info("Switching back to DEMO mode")
                self.demo_mode = True
                return False
        
        return True
    
    def navigate_to_jobs(self):
        """Navigate to LinkedIn jobs page"""
        if self.driver is None:
            self.log_error("Browser not initialized. Call start_browser() first.")
            return False
            
        self.log_info("Navigating to Jobs page...")
        self.driver.get("https://www.linkedin.com/jobs/")
        time.sleep(self.app_config.page_load_delay)
        return True
    
    # def search_jobs(self, keywords: str, location: str = ""):
    #     """Perform job search"""
    #     try:
    #         self.log_info(f"Searching for: {keywords} in {location}")
            
    #         # Find search fields
    #         keyword_field = self.wait.until(
    #             EC.presence_of_element_located((By.CSS_SELECTOR, "input[aria-label*='Search by title']"))
    #         )
    #         location_field = self.driver.find_element(By.CSS_SELECTOR, "input[aria-label*='City, state']")
            
    #         # Clear and fill
    #         self.interactor.safe_send_keys(keyword_field, keywords)
    #         self.interactor.safe_send_keys(location_field, location)
            
    #         # Search
    #         keyword_field.send_keys(Keys.RETURN)
    #         time.sleep(self.app_config.page_load_delay)
            
    #         # Apply Easy Apply filter
    #         if self.get('easy_apply_only', True):
    #             self._apply_easy_apply_filter()
                
    #     except Exception as e:
    #         self.log_error(f"Search error: {str(e)}")
    
    # Replace the search_jobs method:
    def search_jobs(self, keywords: str, location: str = ""):
        """Perform job search with updated selectors"""
        try:
            if self.driver is None:
                self.log_error("Browser not initialized. Call start_browser() first.")
                return False
                
            self.log_info(f"Searching for: {keywords} in {location}")
            
            # Method 1: Direct URL navigation (more reliable)
            base_url = "https://www.linkedin.com/jobs/search/"
            params = []
            
            if keywords:
                params.append(f"keywords={keywords.replace(' ', '%20')}")
            if location:
                params.append(f"location={location.replace(' ', '%20')}")
            if self.get('easy_apply_only', True):
                params.append("f_AL=true")
                
            search_url = f"{base_url}?{'&'.join(params)}" if params else base_url
            self.driver.get(search_url)
            time.sleep(self.app_config.page_load_delay)
            return True
            
        except Exception as e:
            self.log_error(f"Search error: {str(e)}")
            
            # Fallback method
            try:
                if self.driver is None:
                    self.log_error("Driver is None in fallback search")
                    return False
                    
                self.navigate_to_jobs()
                
                # Try multiple selectors for keyword field
                keyword_field = None
                for selector in self.UPDATED_SELECTORS["search_keywords"]:
                    try:
                        keyword_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                        break
                    except:
                        continue
                        
                if keyword_field:
                    self.interactor.safe_send_keys(keyword_field, keywords)
                    keyword_field.send_keys(Keys.RETURN)
                    time.sleep(self.app_config.page_load_delay)
                else:
                    self.log_error("Could not find search field")
                    
            except Exception as e2:
                self.log_error(f"Fallback search failed: {str(e2)}")


    def _apply_easy_apply_filter(self):
        """Apply Easy Apply filter"""
        try:
            if self.wait is None:
                self.log_warning("WebDriverWait not initialized, skipping Easy Apply filter")
                return
                
            easy_apply_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[aria-label='Easy Apply']"))
            )
            self.interactor.safe_click(easy_apply_button)
            time.sleep(2)
        except:
            self.log_warning("Could not apply Easy Apply filter")
    
    def process_job_listings(self):
        """Main method to process job listings"""
        self.stats['start_time'] = datetime.now()
        self.log_info("Starting job application process...")
        
        try:
            while self._should_continue():
                job_cards = self._get_job_cards()
                
                if not job_cards:
                    self.log_info("No more job cards found")
                    break
                
                for idx in range(len(job_cards)):
                    if not self._should_continue():
                        break
                    
                    # Re-fetch job cards to avoid stale element references
                    try:
                        current_cards = self._get_job_cards()
                        if idx >= len(current_cards):
                            self.log_warning(f"Job card {idx + 1} no longer available")
                            continue
                        card = current_cards[idx]
                        self._process_single_job(card, idx + 1)
                    except Exception as e:
                        self.log_error(f"Error processing job {idx + 1}: {str(e)}")
                        continue
                
                # Check for next page
                if not self._go_to_next_page():
                    break
                    
        except KeyboardInterrupt:
            self.log_info("Process interrupted by user")
        except Exception as e:
            self.log_error(f"Process error: {str(e)}")
        finally:
            self._finalize_run()
    
    def _should_continue(self) -> bool:
        """Check if should continue processing"""
        if self.warning_detected:
            return False
            
        max_apps = self.get('max_applications_per_run', 100)
        if self.stats['applied'] >= max_apps:
            self.log_info(f"Reached maximum applications limit: {max_apps}")
            return False
            
        if self.consecutive_errors >= 5:
            self.log_error("Too many consecutive errors, stopping")
            return False
            
        return True
    
    def log_debug(self, msg: str):
        """Log debug message"""
        if self.logger and hasattr(self.logger, 'debug'):
            self.logger.debug(msg)
        else:
            print(f"[DEBUG] {msg}")
            
    # def _get_job_cards(self) -> List:
    #     """Get job cards from current page"""
    #     try:
    #         # Wait for cards to load
    #         self.wait.until(
    #             EC.presence_of_element_located((By.CSS_SELECTOR, "[data-job-id]"))
    #         )
            
    #         cards = self.driver.find_elements(By.CSS_SELECTOR, "[data-job-id]")
    #         self.log_info(f"Found {len(cards)} job cards")
    #         return cards
    # Replace the _get_job_cards method:
    def _get_job_cards(self) -> List:
        """Get job cards with updated selectors"""
        if self.driver is None:
            self.log_error("Browser not initialized. Cannot get job cards.")
            return []
            
        cards = []
        
        # Wait a bit for dynamic content
        time.sleep(2)
        
        # Try each selector
        for selector in self.UPDATED_SELECTORS["job_cards"]:
            try:
                cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if cards:
                    self.log_info(f"Found {len(cards)} job cards using: {selector}")
                    return cards
            except Exception as e:
                self.log_debug(f"Selector {selector} failed: {str(e)}")
                continue
        
        # If no cards found, log page source snippet for debugging
        if not cards:
            try:
                page_source = self.driver.page_source[:1000]
                self.log_debug(f"No cards found. Page source snippet: {page_source}")
            except:
                pass
                
        return cards

    def _process_single_job(self, card, idx: int):
        """Process a single job card"""
        try:
            self.log_info(f"\n{'='*60}")
            self.log_info(f"Processing job {idx}")
            
            # Extract job info
            job_info = self._extract_job_info(card)
            if not job_info:
                self.log_warning("Could not extract job info")
                return
                
            self.log_info(f"Job: {job_info['title']} at {job_info['company']}")
            
            # Click on job card
            if not self.interactor.safe_click(card):
                self.log_error("Could not click job card")
                return
                
            time.sleep(2)
            
            # Check if already applied
            if self._check_already_applied():
                self.log_info("Already applied to this job")
                self.stats['skipped'] += 1
                return
            
            # Find Easy Apply button
            apply_button = self._find_easy_apply_button()
            if not apply_button:
                self.log_info("No Easy Apply button found")
                self.stats['skipped'] += 1
                return
            
            # Check for warnings
            has_warning, warning_text = self.validator.check_for_warnings()
            if has_warning:
                self.log_warning(f"Warning detected: {warning_text}")
                self.warning_detected = True
                return
            
            # Apply to job
            if self._apply_to_job(apply_button, job_info):
                self.stats['applied'] += 1
                self.consecutive_errors = 0
                self.last_application_time = time.time()
            else:
                self.stats['errors'] += 1
                self.consecutive_errors += 1
                
        except Exception as e:
            self.log_error(f"Error processing job {idx}: {str(e)}")
            self.stats['errors'] += 1
            self.consecutive_errors += 1
    
    # def _extract_job_info(self, card) -> Optional[Dict[str, str]]:
    #     """Extract job information from card"""
    #     try:
    #         job_info = {
    #             'id': card.get_attribute('data-job-id'),
    #             'title': '',
    #             'company': '',
    #             'location': '',
    #             'url': ''
    #         }
            
    #         # Try to extract title
    #         try:
    #             title_elem = card.find_element(By.CSS_SELECTOR, "[data-control-name='job_card_title']")
    #             job_info['title'] = title_elem.text
    #         except:
    #             pass
                
    #         # Try to extract company
    #         try:
    #             company_elem = card.find_element(By.CSS_SELECTOR, "[data-control-name='company_name']")
    #             job_info['company'] = company_elem.text
    #         except:
    #             pass
                
    #         return job_info if job_info['title'] else None
            
    #     except Exception as e:
    #         self.log_error(f"Failed to extract job info: {str(e)}")
    #         return None
    
    # Replace the _extract_job_info method:
    def _extract_job_info(self, card) -> Optional[Dict[str, str]]:
        """Extract job info with updated selectors"""
        try:
            job_info = {
                'id': '',
                'title': '',
                'company': '',
                'location': '',
                'url': ''
            }
            
            # Get job ID from attributes
            job_id = (card.get_attribute('data-occludable-job-id') or 
                    card.get_attribute('data-job-id') or 
                    card.get_attribute('id') or 
                    'unknown')
            job_info['id'] = job_id
            
            # Extract title
            for selector in self.UPDATED_SELECTORS["job_title"]:
                try:
                    title_elem = card.find_element(By.CSS_SELECTOR, selector)
                    title_text = title_elem.text.strip()
                    if not title_text:
                        title_text = title_elem.get_attribute('textContent').strip()
                    if title_text:
                        job_info['title'] = title_text
                        break
                except:
                    continue
            
            # Extract company
            for selector in self.UPDATED_SELECTORS["job_company"]:
                try:
                    company_elem = card.find_element(By.CSS_SELECTOR, selector)
                    company_text = company_elem.text.strip()
                    if not company_text:
                        company_text = company_elem.get_attribute('textContent').strip()
                    if company_text:
                        job_info['company'] = company_text
                        break
                except:
                    continue
            
            # Log what we found for debugging
            self.log_debug(f"Extracted job info: {job_info}")
            
            # Return if we have at least a title or ID
            return job_info if (job_info['title'] or job_info['id'] != 'unknown') else None
            
        except Exception as e:
            self.log_error(f"Failed to extract job info: {str(e)}")
            return None


    def _check_already_applied(self) -> bool:
        """Check if already applied to current job"""
        try:
            if self.driver is None:
                self.log_error("Browser not initialized. Cannot check if already applied.")
                return False
                
            # Look for "Applied" indicator
            applied_indicators = [
                "li-icon[type='check-circle']",
                ".jobs-details-top-card__applied-label",
                "span:contains('Applied')"
            ]
            
            for selector in applied_indicators:
                if self.driver.find_elements(By.CSS_SELECTOR, selector):
                    return True
                    
            return False
            
        except:
            return False
    
    def _find_easy_apply_button(self) -> Optional[Any]:
        """Find Easy Apply button on job detail page"""
        self.log_info("Looking for Easy Apply button...")
        
        # Check if driver is available
        if not self.driver:
            self.log_error("Driver is None - cannot find buttons")
            return None
        
        # Debug: Check what buttons are available
        try:
            all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
            self.log_debug(f"Found {len(all_buttons)} total buttons on page")
            
            easy_apply_candidates = []
            for btn in all_buttons:
                try:
                    btn_text = btn.text.strip()
                    aria_label = btn.get_attribute('aria-label') or ''
                    if 'easy apply' in btn_text.lower() or 'easy apply' in aria_label.lower():
                        easy_apply_candidates.append(btn)
                        self.log_debug(f"Easy Apply candidate: text='{btn_text}', aria-label='{aria_label}', visible={btn.is_displayed()}, enabled={btn.is_enabled()}")
                except:
                    pass
            
            # Return first visible and enabled Easy Apply button
            for btn in easy_apply_candidates:
                if btn.is_displayed() and btn.is_enabled():
                    self.log_info(f"Found Easy Apply button: {btn.text or btn.get_attribute('aria-label')}")
                    return btn
                    
        except Exception as e:
            self.log_debug(f"Error in Easy Apply button search: {e}")
        
        # Fallback to original method
        return self.interactor.find_clickable_button(
            button_texts=['Easy Apply'],
            selectors=self.selectors.EASY_APPLY_BUTTONS
        )
    
    def _apply_to_job(self, apply_button, job_info: Dict[str, str]) -> bool:
        """Apply to a specific job"""
        try:
            # Demo mode check
            if self.demo_mode:
                self.log_info("[DEMO MODE] Would apply to this job")
                # In demo mode, still check if modal appears for debugging
                return True
            
            # Click Easy Apply
            self.log_info("Attempting to click Easy Apply button")
            self.log_debug(f"Easy Apply button found: {apply_button.get_attribute('outerHTML')[:200] if apply_button else 'None'}")
            
            if not self.interactor.safe_click(apply_button):
                self.log_error("Failed to click Easy Apply button")
                return False
            
            self.log_info("Successfully clicked Easy Apply button")
            
            # Wait for modal to appear and check multiple times
            modal_appeared = False
            max_wait_attempts = 6
            
            for attempt in range(max_wait_attempts):
                time.sleep(1)  # Wait 1 second between attempts
                
                # Check if driver is still available before proceeding
                if self.driver is None:
                    self.log_error("Browser driver is None, cannot check for modal")
                    break
                
                # Check for modal using multiple selectors
                for selector in ["div[data-test-modal-id='easy-apply-modal']", ".artdeco-modal", "div.jobs-easy-apply-modal", "[role='dialog']"]:
                    try:
                        modals = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for modal in modals:
                            class_attr = modal.get_attribute('class')
                            if modal.is_displayed() and ('easy' in (class_attr.lower() if class_attr else '') or 'easy' in selector.lower()):
                                self.log_info(f"Modal appeared using selector: {selector} (attempt {attempt + 1})")
                                modal_appeared = True
                                break
                    except:
                        continue
                
                if modal_appeared:
                    break
                    
                # Also check if we're on a dedicated application page (not modal)
                try:
                    current_url = self.driver.current_url
                    if '/jobs/application' in current_url or 'easy-apply' in current_url:
                        self.log_info("Redirected to application page instead of modal")
                        modal_appeared = True
                        break
                except:
                    self.log_debug("Could not check current URL")
                    
                self.log_debug(f"Modal not found yet, attempt {attempt + 1}/{max_wait_attempts}")
            
            if not modal_appeared:
                self.log_warning("Easy Apply modal did not appear after clicking button, but continuing anyway")
                # Don't return False immediately, let's try to proceed
                # The application might still work if we're on an application page
            
            # Handle application form
            return self._handle_application_form(job_info)
            
        except Exception as e:
            self.log_error(f"Application error: {str(e)}")
            return False
    
    def _handle_application_form(self, job_info: Dict[str, str]) -> bool:
        """Handle the multi-step application form"""
        max_steps = 10
        
        for step in range(max_steps):
            self.log_info(f"Processing application step {step + 1}")
            
            # Check if application complete
            if self.validator.is_application_complete():
                self.log_info("✅ Application submitted successfully!")
                return True
            
            # Check for errors
            has_errors, error_msgs = self.validator.has_errors()
            if has_errors:
                self.log_error(f"Form errors detected: {error_msgs}")
                if not self._handle_form_errors():
                    return False
            
            # Fill current form
            filled_any = self._fill_current_form()
            self.log_info(f"Form filling result: {filled_any}")
            
            # Try to proceed to next step
            proceeded = self._proceed_to_next_step()
            self.log_info(f"Proceed to next step result: {proceeded}")
            
            if not proceeded:
                self.log_error("Could not proceed to next step")
                # Check if we're on the final submit page
                submit_button = self.interactor.find_clickable_button(
                    button_texts=['Submit application', 'Submit'],
                    selectors=self.selectors.SUBMIT_BUTTONS
                )
                if not submit_button:
                    return False
                else:
                    self.log_info("Found submit button, ready for final step")
                
            time.sleep(self.app_config.form_submit_delay)
        
        self.log_error("Max steps reached without completion")
        return False
    
    def _fill_current_form(self) -> bool:
        """Fill the current form page"""
        try:
            # Analyze form structure
            form_data = self.analyzer.analyze_form_structure()
            if not form_data:
                self.log_warning("Form analysis returned no data")
                return False
            
            self.log_info(f"Found {len(form_data['text_fields'])} text fields, {len(form_data['dropdowns'])} dropdowns")
            filled_any = False
            
            # Fill text fields
            for field_info in form_data['text_fields']:
                self.log_info(f"Processing field: '{field_info['label']}' (required: {field_info['required']})")
                if field_info['value']:  # Skip if already filled
                    self.log_info(f"Field already filled with: {field_info['value']}")
                    continue
                    
                answer = self.matcher.match_field(field_info['label'], field_info['type'])
                if answer:
                    self.log_info(f"Found answer from matcher: {answer}")
                    if self.interactor.safe_send_keys(field_info['element'], answer):
                        filled_any = True
                        self._save_question_answer(field_info['label'], answer)
                elif field_info['required']:
                    # Ask user for required fields
                    self.log_info(f"Asking user for required field: {field_info['label']}")
                    answer = self._ask_user_for_answer(field_info['label'])
                    if answer and self.interactor.safe_send_keys(field_info['element'], answer):
                        filled_any = True
                        self._save_question_answer(field_info['label'], answer)
                else:
                    self.log_info(f"No answer found for optional field: {field_info['label']}")
            
            # Handle dropdowns
            for dropdown_info in form_data['dropdowns']:
                if dropdown_info['selected']:  # Skip if already selected
                    continue
                    
                answer = self.matcher.match_field(dropdown_info['label'], 'dropdown')
                if answer:
                    if self._select_dropdown_option(dropdown_info['element'], answer, dropdown_info['options']):
                        filled_any = True
            
            # Handle file uploads
            resume_path = self.get('resume_path')
            if resume_path and os.path.exists(resume_path) and self.driver is not None:
                file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
                for file_input in file_inputs:
                    if file_input.is_displayed() and not file_input.get_attribute('value'):
                        file_input.send_keys(resume_path)
                        filled_any = True
                        time.sleep(2)
            
            return filled_any
            
        except Exception as e:
            self.log_error(f"Form filling error: {str(e)}")
            return False
    
    def _select_dropdown_option(self, dropdown_element, desired_value: str, options: List[str]) -> bool:
        """Select dropdown option with fuzzy matching"""
        try:
            select = Select(dropdown_element)
            
            # Try exact match first
            for option in options:
                if option.lower() == desired_value.lower():
                    select.select_by_visible_text(option)
                    return True
            
            # Try contains match
            for option in options:
                if desired_value.lower() in option.lower():
                    select.select_by_visible_text(option)
                    return True
                    
            return False
            
        except Exception as e:
            self.log_error(f"Dropdown selection error: {str(e)}")
            return False
    
    def _ask_user_for_answer(self, question: str) -> Optional[str]:
        """Ask user for answer to a question"""
        if self.get('auto_use_defaults', False):
            return None
            
        print(f"\n❓ Question: {question}")
        answer = input("Your answer (or press Enter to skip): ").strip()
        return answer if answer else None
    
    def _handle_form_errors(self) -> bool:
        """Handle form validation errors"""
        # Try to fix common errors
        # This is where you could add more sophisticated error handling
        return False
    
    def _proceed_to_next_step(self) -> bool:
        """Proceed to next step in application"""
        self.log_info("Looking for navigation buttons...")
        
        # Check if driver is initialized
        if self.driver is None:
            self.log_error("Driver is None, cannot proceed to next step")
            return False
        
        # Debug: Log all buttons on page
        try:
            if self.driver is not None:
                all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                self.log_debug(f"Found {len(all_buttons)} buttons on page")
                for i, btn in enumerate(all_buttons[:10]):  # Log first 10 buttons
                    try:
                        btn_text = btn.text.strip() or btn.get_attribute('aria-label') or btn.get_attribute('type')
                        self.log_debug(f"Button {i+1}: '{btn_text}' - visible: {btn.is_displayed()} - enabled: {btn.is_enabled()}")
                    except:
                        pass
            else:
                self.log_debug("Driver is None, cannot log buttons")
                return False
        except Exception as e:
            self.log_debug(f"Error logging buttons: {e}")
            return False
        
        # Additional check to ensure driver is still valid
        if self.driver is None:
            self.log_error("Driver is None, cannot proceed to next step")
            return False
        
        # Additional check to ensure driver is still valid
        if self.driver is None:
            self.log_error("Driver is None, cannot proceed to next step")
            return False
        
        # Try data attribute buttons first (most reliable)
        for attr in ['data-live-test-easy-apply-next-button', 'data-easy-apply-next-button']:
            try:
                if self.driver is not None:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, f"button[{attr}]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found Next button by {attr}, clicking...")
                            return self.interactor.safe_click(button)
            except Exception as e:
                self.log_debug(f"Error finding button by {attr}: {e}")
        # Try Next/Continue/Review buttons by text
        for button_text in ['Next', 'Continue', 'Review']:
            try:
                if self.driver is not None:
                    # Look for buttons with specific text in artdeco-button structure
                    buttons = self.driver.find_elements(By.XPATH, f"//button[contains(@class, 'artdeco-button') and .//span[contains(text(), '{button_text}')]]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found {button_text} button (artdeco), clicking...")
                            return self.interactor.safe_click(button)
                            
                    # Fallback to simple text search
                    buttons = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{button_text}')]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found {button_text} button (simple), clicking...")
                            return self.interactor.safe_click(button)
            except Exception as e:
                self.log_debug(f"Error finding {button_text} button: {e}")
        # Try Submit buttons by data attributes first
        for attr in ['data-live-test-easy-apply-submit-button', 'data-easy-apply-submit-button']:
            try:
                if self.driver is not None:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, f"button[{attr}]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found Submit button by {attr}, clicking...")
                            return self.interactor.safe_click(button)
            except Exception as e:
                self.log_debug(f"Error finding button by {attr}: {e}")
        # Try Submit buttons by text
        for button_text in ['Submit application', 'Submit']:
            try:
                if self.driver is not None:
                    # Look for buttons with specific text in artdeco-button structure  
                    buttons = self.driver.find_elements(By.XPATH, f"//button[contains(@class, 'artdeco-button') and .//span[contains(text(), '{button_text}')]]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found {button_text} button (artdeco), clicking...")
                            return self.interactor.safe_click(button)
                            
                    # Fallback to simple text search
                    buttons = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{button_text}')]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.log_info(f"Found {button_text} button (simple), clicking...")
                            return self.interactor.safe_click(button)
            except Exception as e:
                self.log_debug(f"Error finding {button_text} button: {e}")
        
        self.log_warning("No Next/Continue/Submit buttons found")
        return False
    
    def _go_to_next_page(self) -> bool:
        """Navigate to next page of results"""
        try:
            if self.driver is None:
                self.log_error("Browser not initialized. Cannot go to next page.")
                return False
                
            next_button = self.driver.find_element(By.CSS_SELECTOR, "button[aria-label='Next']")
            if next_button.is_enabled():
                self.interactor.safe_click(next_button)
                time.sleep(self.app_config.page_load_delay)
                return True
        except Exception as e:
            self.log_debug(f"Next page navigation failed: {str(e)}")
            
        return False
    
    def _finalize_run(self):
        """Finalize the run and save stats"""
        self.stats['end_time'] = datetime.now()
        
        duration = self.stats['end_time'] - self.stats['start_time']
        
        self.log_info("\n" + "="*60)
        self.log_info("RUN COMPLETE")
        self.log_info(f"Duration: {duration}")
        self.log_info(f"Applied: {self.stats['applied']}")
        self.log_info(f"Skipped: {self.stats['skipped']}")
        self.log_info(f"Errors: {self.stats['errors']}")
        self.log_info("="*60)
    
    def run(self):
        """Main run method"""
        try:
            # Start browser
            self.start_browser()
            
            # Login
            username = self.get('username')
            password = self.get('password')
            
            if not username or not password:
                self.log_error("Username or password not provided, exiting")
                return
            
            if not self.login(username, password):
                self.log_error("Login failed, exiting")
                return
            
            # Navigate to jobs
            self.navigate_to_jobs()
            
            # Search for jobs
            search_config = self.get('search', {}) or {}
            keywords = search_config.get('keywords', ['Software Engineer'])[0] if isinstance(search_config.get('keywords'), list) else search_config.get('keywords', 'Software Engineer')
            location = search_config.get('location', 'Remote')
            
            self.search_jobs(keywords, location)
            
            # Process listings
            self.process_job_listings()
            
        except Exception as e:
            self.log_error(f"Run error: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()


def create_job_application_handler(config, logger=None):
    """Factory function to create job applicator instance"""
    return ModularJobApplicator(config, logger)