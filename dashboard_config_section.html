<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Configuration - Dashboard</title>
    <style>
        :root {
            --primary: #0077B5;
            --secondary: #00A0DC;
            --success: #10B981;
            --warning: #F59E0B;
            --danger: #EF4444;
            --dark: #1F2937;
            --light: #F3F4F6;
            --white: #FFFFFF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light);
            color: var(--dark);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: var(--white);
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .header h1 {
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .config-section {
            background: var(--white);
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .config-section h2 {
            color: var(--dark);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--light);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            font-size: 1rem;
            transition: border-color 0.15s ease-in-out;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(0, 119, 181, 0.1);
        }

        .range-input {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .range-input input[type="number"] {
            width: 80px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            display: inline-block;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #CBD5E1;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .button-group {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: #005885;
        }

        .btn-secondary {
            background: var(--light);
            color: var(--dark);
            border: 1px solid #D1D5DB;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .selector-list {
            background: var(--light);
            padding: 1rem;
            border-radius: 0.375rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .selector-item {
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: white;
            border-radius: 0.25rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .selector-item code {
            font-size: 0.875rem;
            background: var(--light);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }

        .remove-btn {
            background: var(--danger);
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.75rem;
        }

        .add-selector-form {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .add-selector-form input {
            flex: 1;
        }

        .info-box {
            background: #EFF6FF;
            border: 1px solid #BFDBFE;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .info-box p {
            color: #1E40AF;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Job Application Configuration</h1>
            <p>Fine-tune your LinkedIn automation settings for optimal performance</p>
        </div>

        <!-- Delays Configuration -->
        <div class="config-section">
            <h2>⏱️ Timing & Delays</h2>
            <div class="info-box">
                <p>Adjust these values to make the automation more human-like and avoid detection</p>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label>Click Delay (seconds)</label>
                    <div class="range-input">
                        <input type="number" class="form-control" id="clickDelayMin" value="0.5" step="0.1" min="0.1" aria-label="Click Delay Minimum">
                        <span>to</span>
                        <input type="number" class="form-control" id="clickDelayMax" value="1.0" step="0.1" min="0.1" aria-label="Click Delay Maximum">
                    </div>
                </div>
                <div class="form-group">
                    <label>Typing Delay (seconds)</label>
                    <div class="range-input">
                        <input type="number" class="form-control" id="typingDelayMin" value="0.05" step="0.01" min="0.01" aria-label="Typing Delay Minimum">
                        <span>to</span>
                        <input type="number" class="form-control" id="typingDelayMax" value="0.15" step="0.01" min="0.01" aria-label="Typing Delay Maximum">
                    </div>
                </div>
                <div class="form-group">
                    <label>Page Load Delay</label>
                    <input type="number" class="form-control" id="pageLoadDelay" value="3.0" step="0.5" min="1" aria-label="Page Load Delay">
                </div>
                <div class="form-group">
                    <label>Form Submit Delay</label>
                    <input type="number" class="form-control" id="formSubmitDelay" value="2.0" step="0.5" min="1" aria-label="Form Submit Delay">
                </div>
            </div>
        </div>

        <!-- Retry Configuration -->
        <div class="config-section">
            <h2>🔄 Retry Settings</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label>Max Click Attempts</label>
                    <input type="number" class="form-control" id="maxClickAttempts" value="3" min="1" max="10" aria-label="Max Click Attempts">
                </div>
                <div class="form-group">
                    <label>Max Form Attempts</label>
                    <input type="number" class="form-control" id="maxFormAttempts" value="2" min="1" max="5" aria-label="Max Form Attempts">
                </div>
            </div>
        </div>

        <!-- Features Configuration -->
        <div class="config-section">
            <h2>🚀 Features</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label>
                        <span>Auto Dismiss Modals</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoDismissModals" checked aria-label="Auto Dismiss Modals">
                            <span class="toggle-slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <span>Auto Scroll</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoScroll" checked aria-label="Auto Scroll">
                            <span class="toggle-slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <span>JavaScript Fallback</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="jsFallback" checked aria-label="JavaScript Fallback">
                            <span class="toggle-slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <span>Screenshot on Error</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="screenshotOnError" checked aria-label="Screenshot on Error">
                            <span class="toggle-slider"></span>
                        </label>
                    </label>
                </div>
            </div>
        </div>

        <!-- Custom Selectors -->
        <div class="config-section">
            <h2>🎯 Custom Selectors</h2>
            <div class="info-box">
                <p>Add custom CSS selectors for elements that may have changed on LinkedIn</p>
            </div>
            
            <div class="form-group">
                <label>Easy Apply Button Selectors</label>
                <div class="selector-list" id="easyApplySelectors">
                    <!-- Selectors will be added here dynamically -->
                </div>
                <div class="add-selector-form">
                    <input type="text" class="form-control" id="newEasyApplySelector" placeholder="e.g., button[aria-label*='Easy Apply']">
                    <button class="btn btn-primary" onclick="addSelector('easyApply')">Add</button>
                </div>
            </div>

            <div class="form-group">
                <label>Submit Button Selectors</label>
                <div class="selector-list" id="submitSelectors">
                    <!-- Selectors will be added here dynamically -->
                </div>
                <div class="add-selector-form">
                    <input type="text" class="form-control" id="newSubmitSelector" placeholder="e.g., button[type='submit']">
                    <button class="btn btn-primary" onclick="addSelector('submit')">Add</button>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="button-group">
            <button class="btn btn-primary" onclick="saveConfiguration()">Save Configuration</button>
            <button class="btn btn-secondary" onclick="resetToDefaults()">Reset to Defaults</button>
            <button class="btn btn-secondary" onclick="exportConfiguration()">Export Config</button>
        </div>
    </div>

    <script>
        // Configuration management
        let currentConfig = {};

        // Load configuration on page load
        async function loadConfiguration() {
            try {
                const response = await fetch('/api/config/job-application');
                currentConfig = await response.json();
                updateUI();
            } catch (error) {
                console.error('Failed to load configuration:', error);
            }
        }

        // Update UI with current configuration
        function updateUI() {
            // Delays
            document.getElementById('clickDelayMin').value = currentConfig.delays?.click_delay?.[0] || 0.5;
            document.getElementById('clickDelayMax').value = currentConfig.delays?.click_delay?.[1] || 1.0;
            document.getElementById('typingDelayMin').value = currentConfig.delays?.typing_delay?.[0] || 0.05;
            document.getElementById('typingDelayMax').value = currentConfig.delays?.typing_delay?.[1] || 0.15;
            document.getElementById('pageLoadDelay').value = currentConfig.delays?.page_load || 3.0;
            document.getElementById('formSubmitDelay').value = currentConfig.delays?.form_submit || 2.0;

            // Retries
            document.getElementById('maxClickAttempts').value = currentConfig.retries?.max_click_attempts || 3;
            document.getElementById('maxFormAttempts').value = currentConfig.retries?.max_form_attempts || 2;

            // Features
            document.getElementById('autoDismissModals').checked = currentConfig.features?.auto_dismiss_modals ?? true;
            document.getElementById('autoScroll').checked = currentConfig.features?.auto_scroll ?? true;
            document.getElementById('jsFallback').checked = currentConfig.features?.javascript_fallback ?? true;
            document.getElementById('screenshotOnError').checked = currentConfig.features?.take_screenshots_on_error ?? true;

            // Custom selectors
            updateSelectorLists();
        }

        // Update selector lists
        function updateSelectorLists() {
            const easyApplyList = document.getElementById('easyApplySelectors');
            const submitList = document.getElementById('submitSelectors');

            easyApplyList.innerHTML = '';
            submitList.innerHTML = '';

            // Easy Apply selectors
            (currentConfig.selectors?.custom_easy_apply_buttons || []).forEach((selector, index) => {
                addSelectorToList('easyApply', selector, index);
            });

            // Submit selectors
            (currentConfig.selectors?.custom_submit_buttons || []).forEach((selector, index) => {
                addSelectorToList('submit', selector, index);
            });
        }

        // Add selector to list
        function addSelectorToList(type, selector, index) {
            const listId = type === 'easyApply' ? 'easyApplySelectors' : 'submitSelectors';
            const list = document.getElementById(listId);
            
            const item = document.createElement('div');
            item.className = 'selector-item';
            item.innerHTML = `
                <code>${selector}</code>
                <button class="remove-btn" onclick="removeSelector('${type}', ${index})">Remove</button>
            `;
            
            list.appendChild(item);
        }

        // Add new selector
        function addSelector(type) {
            const inputId = type === 'easyApply' ? 'newEasyApplySelector' : 'newSubmitSelector';
            const input = document.getElementById(inputId);
            const selector = input.value.trim();

            if (!selector) return;

            if (!currentConfig.selectors) {
                currentConfig.selectors = {};
            }

            const arrayKey = type === 'easyApply' ? 'custom_easy_apply_buttons' : 'custom_submit_buttons';
            
            if (!currentConfig.selectors[arrayKey]) {
                currentConfig.selectors[arrayKey] = [];
            }

            currentConfig.selectors[arrayKey].push(selector);
            input.value = '';
            updateSelectorLists();
        }

        // Remove selector
        function removeSelector(type, index) {
            const arrayKey = type === 'easyApply' ? 'custom_easy_apply_buttons' : 'custom_submit_buttons';
            currentConfig.selectors[arrayKey].splice(index, 1);
            updateSelectorLists();
        }

        // Save configuration
        async function saveConfiguration() {
            // Gather values from UI
            currentConfig.delays = {
                click_delay: [
                    parseFloat(document.getElementById('clickDelayMin').value),
                    parseFloat(document.getElementById('clickDelayMax').value)
                ],
                typing_delay: [
                    parseFloat(document.getElementById('typingDelayMin').value),
                    parseFloat(document.getElementById('typingDelayMax').value)
                ],
                page_load: parseFloat(document.getElementById('pageLoadDelay').value),
                form_submit: parseFloat(document.getElementById('formSubmitDelay').value)
            };

            currentConfig.retries = {
                max_click_attempts: parseInt(document.getElementById('maxClickAttempts').value),
                max_form_attempts: parseInt(document.getElementById('maxFormAttempts').value)
            };

            currentConfig.features = {
                auto_dismiss_modals: document.getElementById('autoDismissModals').checked,
                auto_scroll: document.getElementById('autoScroll').checked,
                javascript_fallback: document.getElementById('jsFallback').checked,
                take_screenshots_on_error: document.getElementById('screenshotOnError').checked
            };

            try {
                const response = await fetch('/api/config/job-application', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(currentConfig)
                });

                if (response.ok) {
                    alert('Configuration saved successfully!');
                } else {
                    alert('Failed to save configuration');
                }
            } catch (error) {
                console.error('Save error:', error);
                alert('Error saving configuration');
            }
        }

        // Reset to defaults
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset to default values?')) {
                currentConfig = {
                    delays: {
                        click_delay: [0.5, 1.0],
                        typing_delay: [0.05, 0.15],
                        page_load: 3.0,
                        form_submit: 2.0
                    },
                    retries: {
                        max_click_attempts: 3,
                        max_form_attempts: 2
                    },
                    features: {
                        auto_dismiss_modals: true,
                        auto_scroll: true,
                        javascript_fallback: true,
                        take_screenshots_on_error: true
                    },
                    selectors: {
                        custom_easy_apply_buttons: [],
                        custom_submit_buttons: []
                    }
                };
                updateUI();
            }
        }

        // Export configuration
        function exportConfiguration() {
            const dataStr = JSON.stringify(currentConfig, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'job_application_config.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // Initialize on load
        window.addEventListener('load', loadConfiguration);
    </script>
</body>
</html>