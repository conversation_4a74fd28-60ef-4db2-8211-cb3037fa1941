#!/usr/bin/env python3
"""
Check what methods EnhancedLogger actually has
"""

try:
    from enhanced_logging import EnhancedLogger
    
    print("EnhancedLogger methods:")
    logger = EnhancedLogger()
    
    # Get all methods
    methods = [method for method in dir(logger) if not method.startswith('_')]
    
    print("\nAvailable methods:")
    for method in sorted(methods):
        print(f"  - {method}")
        
    # Check specific logging methods
    print("\nLogging method check:")
    print(f"  - Has 'info': {hasattr(logger, 'info')}")
    print(f"  - Has 'error': {hasattr(logger, 'error')}")
    print(f"  - Has 'log_info': {hasattr(logger, 'log_info')}")
    print(f"  - Has 'log_error': {hasattr(logger, 'log_error')}")
    
    # Try to find the actual error logging method
    print("\nMethods containing 'error':")
    error_methods = [m for m in methods if 'error' in m.lower()]
    for method in error_methods:
        print(f"  - {method}")
        
except Exception as e:
    print(f"Error: {e}")
