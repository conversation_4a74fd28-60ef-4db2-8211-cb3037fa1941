#!/usr/bin/env python3
"""
Quick fix script to add missing imports to message_autoreply_module.py
"""

import os

def fix_message_module():
    """Add missing typing imports to the message module"""
    
    file_path = 'message_autoreply_module.py'
    
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if typing imports are missing
    if 'from typing import' not in content:
        # Add typing imports at the top after the docstring
        lines = content.split('\n')
        
        # Find where to insert (after initial comments/docstring)
        insert_pos = 0
        in_docstring = False
        for i, line in enumerate(lines):
            if line.strip().startswith('"""') or line.strip().startswith("'''"):
                if not in_docstring:
                    in_docstring = True
                else:
                    insert_pos = i + 1
                    break
            elif not in_docstring and line.strip() and not line.strip().startswith('#'):
                insert_pos = i
                break
        
        # Insert the typing import
        lines.insert(insert_pos, 'from typing import Dict, List, Any, Optional, Tuple')
        lines.insert(insert_pos + 1, '')
        
        # Write back
        with open(file_path, 'w') as f:
            f.write('\n'.join(lines))
        
        print(f"✓ Fixed {file_path} - added typing imports")
    else:
        # Check if 'Any' is in the imports
        import_line = None
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('from typing import'):
                import_line = i
                if 'Any' not in line:
                    # Add Any to existing imports
                    lines[i] = line.rstrip() + ', Any'
                    with open(file_path, 'w') as f:
                        f.write('\n'.join(lines))
                    print(f"✓ Fixed {file_path} - added 'Any' to typing imports")
                    return
                else:
                    print(f"✓ {file_path} already has 'Any' imported")
                    return
        
        print(f"! Could not fix {file_path} - manual intervention needed")

if __name__ == "__main__":
    fix_message_module()
    print("\nNow you can run: python3 main.py")
