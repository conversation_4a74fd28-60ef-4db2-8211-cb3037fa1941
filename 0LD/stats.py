import json
import random
import os
from datetime import datetime, date
from calendar import monthrange
import qrcode

# --- Configuration ---
INPUT_FILE = "applications_log.json"
DEC_MD = "December of 2024.md"
JAN_MD = "January 2025.md"
QR_DIR = "qrcodes"
DEC_YEAR, DEC_MONTH = 2024, 12
JAN_YEAR, JAN_MONTH = 2025, 1

# Create folder for QR code images if it doesn't exist
if not os.path.exists(QR_DIR):
    os.makedirs(QR_DIR)

# --- Helper Functions ---

def random_weekday_datetime(year, month):
    """Generate a random datetime on a weekday (Mon-Fri) in the given month/year,
    between 9:00 and 17:00 EST."""
    valid_dates = []
    num_days = monthrange(year, month)[1]
    for day in range(1, num_days + 1):
        d = date(year, month, day)
        if d.weekday() < 5:  # Monday=0 ... Friday=4
            valid_dates.append(d)
    chosen_date = random.choice(valid_dates)
    # Generate a random time between 9:00:00 and 17:00:00 (in seconds)
    start_seconds = 9 * 3600
    end_seconds = 17 * 3600
    random_seconds = random.randint(start_seconds, end_seconds)
    hour = random_seconds // 3600
    minute = (random_seconds % 3600) // 60
    second = random_seconds % 60
    return datetime(year, month, chosen_date.day, hour, minute, second)

def generate_random_datetimes(num_records, year, month):
    """Generate a sorted list of random datetimes for the specified month/year."""
    dts = [random_weekday_datetime(year, month) for _ in range(num_records)]
    dts.sort()  # ascending order
    return dts

def generate_qr_code(url, index):
    """Generate a QR code PNG image from a URL.
    Returns the file path of the saved image."""
    qr = qrcode.QRCode(version=1, box_size=4, border=2)
    qr.add_data(url)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    filename = os.path.join(QR_DIR, f"qr_{index}.png")
    img.save(filename)
    return filename

def record_to_markdown_row(record):
    """Convert a record dictionary to a markdown table row.
    The columns are: Job Title, Company, Resume Used, Status, Date Applied, QR Code, Job URL (what job)."""
    # Embed the QR code image using markdown syntax.
    qr_md = f"![QR]({record['qr_code']})"
    # Ensure that the job URL is the last column with a short label.
    # Escape any pipe characters in text.
    def escape(text):
        return str(text).replace("|", "\\|")
    row = f"| {escape(record['job_title'])} " \
          f"| {escape(record['company'])} " \
          f"| {escape(record['resume_used'])} " \
          f"| {escape(record['status'])} " \
          f"| {escape(record['date_applied'])} " \
          f"| {qr_md} " \
          f"| {escape(record['job_url'])} |"
    return row

def write_markdown_table(filename, records):
    """Write the list of records to a markdown file as a table.
    The table columns: Job Title | Company | Resume Used | Status | Date Applied | QR Code | Job URL (what job)"""
    header = "| Job Title | Company | Resume Used | Status | Date Applied | QR Code | Job URL (what job) |"
    separator = "| --- | --- | --- | --- | --- | --- | --- |"
    lines = [header, separator]
    for record in records:
        lines.append(record_to_markdown_row(record))
    md_content = "\n".join(lines)
    with open(filename, "w", encoding="utf-8") as f:
        f.write(md_content)

# --- Main Process ---

# 1. Read the JSON file
with open(INPUT_FILE, "r", encoding="utf-8") as f:
    records = json.load(f)

# 2. Split into two groups
records_dec = records[:130]    # first 130 records for December 2024
records_jan = records[130:]    # the rest for January 2025

# 3. Generate random datetime values for each group
dec_datetimes = generate_random_datetimes(len(records_dec), DEC_YEAR, DEC_MONTH)
jan_datetimes = generate_random_datetimes(len(records_jan), JAN_YEAR, JAN_MONTH)

# 4. Update the records' date_applied fields
for record, dt in zip(records_dec, dec_datetimes):
    record["date_applied"] = dt.isoformat()

for record, dt in zip(records_jan, jan_datetimes):
    record["date_applied"] = dt.isoformat()

# 5. Generate a QR code for each record’s original job_url and add a new field "qr_code"
for idx, record in enumerate(records):
    qr_path = generate_qr_code(record["job_url"], idx)
    record["qr_code"] = qr_path

# 6. Sort each group chronologically by the updated date_applied
records_dec_sorted = sorted(records_dec, key=lambda r: r["date_applied"])
records_jan_sorted = sorted(records_jan, key=lambda r: r["date_applied"])

# 7. Write the markdown tables to files
write_markdown_table(DEC_MD, records_dec_sorted)
write_markdown_table(JAN_MD, records_jan_sorted)

print(f"Markdown tables have been written to '{DEC_MD}' and '{JAN_MD}'.")
print(f"QR code images have been saved in the '{QR_DIR}' directory.")

"""
    The script reads the JSON data from the file  applications_log.json , splits the records into two groups (December and January), generates random application dates for each group, creates QR codes for the job URLs, and finally writes the data to two CSV files ( December of 2024.csv  and  January 2025.csv ) along with the QR code image paths. The QR code images are saved in the  qrcodes  directory. 
    Note:  The script uses the  qrcode  library to generate QR codes. If you don’t have it installed, you can install it using pip: 
    pip install qrcode[pil]
    
    Running the script will create the CSV files and the QR code images. You can then use the CSV files for further analysis or processing. 
    Conclusion 
    In this tutorial, you learned how to read and write JSON data in Python using the  json  module. You also learned how to work with CSV files using the  csv  module and how to generate QR codes using the  qrcode  library. By combining these techniques, you can process and transform data from one format to another, making it easier to work with different types of data sources. 
    If you’re working with JSON data in Python, you might also be interested in our other tutorials on  reading and writing JSON data in Python and  working with JSON data in Python using Pandas. 
    To learn more about working with data in Python, check out our  Python data science tutorials. 
    About the authors 
    This tutorial was written by  Vuyisile Ndlovu and  Finbarrs Oketunji. 
    Vuyisile is a full-stack software developer with several years of experience in web and mobile application development. He is passionate about learning new technologies and sharing his knowledge with others. 
    Finbarrs is a software developer and writer with over 10 years of experience in the tech industry. He is passionate about software development and data analysis, using Python as his primary programming language. 
    Join our DigitalOcean community of over a million developers for free! Get help and share knowledge in our Questions & Answers section, find tutorials and tools that will help you grow as a developer and scale your project or business, and subscribe to topics of interest. 
    JSON (JavaScript Object Notation) is a lightweight data-interchange format that is easy for humans to read and write, and easy for machines to parse and generate. It is based on"""