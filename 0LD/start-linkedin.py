#!/usr/bin/env python3
"""
LinkedIn Automation Platform - Smart Startup Script
This script handles all the import issues and starts the platform correctly
"""

import sys
import os
import subprocess

def fix_imports():
    """Fix common import issues in the project"""
    
    # Fix message_autoreply_module.py
    if os.path.exists('message_autoreply_module.py'):
        try:
            with open('message_autoreply_module.py', 'r') as f:
                content = f.read()
            
            if 'from typing import' not in content or ('from typing import' in content and 'Any' not in content):
                print("Fixing message_autoreply_module.py imports...")
                
                # Add import at the beginning
                lines = content.split('\n')
                
                # Find the right place to insert
                insert_pos = 0
                for i, line in enumerate(lines):
                    if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('"""'):
                        insert_pos = i
                        break
                
                if 'from typing import' in content:
                    # Update existing import
                    for i, line in enumerate(lines):
                        if line.startswith('from typing import') and 'Any' not in line:
                            lines[i] = line.rstrip() + ', Any'
                            break
                else:
                    # Add new import
                    lines.insert(insert_pos, 'from typing import Dict, List, Any, Optional, Tuple\n')
                
                with open('message_autoreply_module.py', 'w') as f:
                    f.write('\n'.join(lines))
                
                print("✓ Fixed message_autoreply_module.py")
        except Exception as e:
            print(f"Warning: Could not fix message_autoreply_module.py: {e}")

def run_platform():
    """Run the LinkedIn automation platform"""
    
    # First, try the fixed main.py
    if os.path.exists('main.py'):
        print("Starting LinkedIn Automation Platform via main.py...")
        try:
            subprocess.run([sys.executable, 'main.py'] + sys.argv[1:])
            return
        except Exception as e:
            print(f"Error running main.py: {e}")
    
    # If main.py doesn't work, try lin.py directly
    if os.path.exists('lin.py'):
        print("Starting LinkedIn Automation Platform via lin.py...")
        try:
            # Import and run directly
            import lin
            if hasattr(lin, 'LinkedInAutomationCLI'):
                cli = lin.LinkedInAutomationCLI()
                cli.run()
            elif hasattr(lin, 'main'):
                lin.main()
            else:
                subprocess.run([sys.executable, 'lin.py'])
        except Exception as e:
            print(f"Error running lin.py: {e}")
            
            # Try as subprocess
            try:
                subprocess.run([sys.executable, 'lin.py'])
            except:
                pass
    
    # Last resort - run complete_enhanced_linkedin.py
    if os.path.exists('complete_enhanced_linkedin.py'):
        print("Starting LinkedIn Automation Platform via complete_enhanced_linkedin.py...")
        subprocess.run([sys.executable, 'complete_enhanced_linkedin.py'])

def main():
    """Main entry point"""
    print("LinkedIn Automation Platform - Smart Startup")
    print("=" * 50)
    
    # Fix known issues
    fix_imports()
    
    # Run the platform
    run_platform()

if __name__ == "__main__":
    main()
