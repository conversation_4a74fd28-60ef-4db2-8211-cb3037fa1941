#!/usr/bin/env python3
"""
LinkedIn Automation Platform - Main Entry Point
Integrates all modular components into a unified system
"""

import sys
import os
import time
import argparse
import threading
from datetime import datetime

# Core modules
try:
    from enhanced_config import EnhancedConfig
    from enhanced_logging import Enhanced<PERSON>ogger
    from integration_adapter import <PERSON>rowser<PERSON>anager, DataManager, AIProvider
except ImportError as e:
    print(f"Missing core module: {e}")
    print("Please ensure enhanced_config.py, enhanced_logging.py, and integration_adapter.py are present")
    sys.exit(1)

# Feature modules
try:
    from network_maintenance_module import NetworkMaintainer
    from article_publishing_module import ContentCreator
    from message_autoreply_module import MessageAutoResponder
    from web_dashboard_backend import DashboardServer
except ImportError as e:
    print(f"Warning: Some feature modules not available: {e}")
    print("Some menu options may not work until all modules are implemented")

class LinkedInAutomationPlatform:
    def __init__(self, config_path="linkedin_config.json"):
        """Initialize the automation platform with enhanced modules"""
        print("🚀 Initializing LinkedIn Automation Platform...")
        
        # Load configuration
        self.config = EnhancedConfig(config_path)
        
        # Setup enhanced logging
        self.logger = EnhancedLogger(self.config)
        self.logger.log_run_start()
        
        # Initialize core components
        self.browser_manager = BrowserManager(self.config, self.logger)
        self.data_manager = DataManager(self.config, self.logger)
        self.ai_provider = AIProvider(self.config, self.logger)
        
        # Initialize feature modules
        self.network_maintainer = None
        self.content_creator = None
        self.message_responder = None
        self.dashboard_server = None
        
        print("✅ Platform initialized successfully!")

    def show_menu(self):
        """Display the main menu options"""
        print("\n" + "="*50)
        print("   LinkedIn Automation Platform")
        print("="*50)
        print("1. 🎯 Apply to Jobs (Core Automation)")
        print("2. 🤝 Network Maintenance")
        print("3. ✍️  Article Publishing & Content Creation")
        print("4. 💬 Message Auto-Reply")
        print("5. 📊 Open Web Dashboard")
        print("6. 📈 View Statistics & Reports")
        print("7. ⚙️  Configuration Management")
        print("8. 📋 View Logs")
        print("9. ❌ Exit")
        print("="*50)

    def apply_to_jobs(self):
        """Core job application automation"""
        print("\n🎯 Starting Job Application Process...")
        
        try:
            # Import the core job application logic
            # This would be from your complete_enhanced_linkedin.py or similar
            from complete_enhanced_linkedin import LinkedInAutomation
            
            automation = LinkedInAutomation(
                browser_manager=self.browser_manager,
                data_manager=self.data_manager,
                ai_provider=self.ai_provider,
                config=self.config,
                logger=self.logger
            )
            
            automation.run_job_search()
            
        except ImportError:
            print("⚠️  Core automation module not found. Using basic job search...")
            self._basic_job_search()
        except Exception as e:
            self.logger.log_error("Job application failed", str(e))
            print(f"❌ Error during job application: {e}")

    def _basic_job_search(self):
        """Basic job search functionality if main module not available"""
        print("🔍 Running basic job search...")
        
        # Basic LinkedIn job search logic
        browser = self.browser_manager.get_browser()
        
        try:
            # Login
            browser.get("https://linkedin.com/login")
            time.sleep(2)
            
            # Add your basic job search logic here
            print("📋 Basic job search completed")
            
        except Exception as e:
            self.logger.log_error("Basic job search failed", str(e))
            print(f"❌ Basic job search error: {e}")
        finally:
            self.browser_manager.close_browser()

    def network_maintenance(self):
        """Handle network maintenance tasks"""
        print("\n🤝 Starting Network Maintenance...")
        
        try:
            if not self.network_maintainer:
                self.network_maintainer = NetworkMaintainer(
                    self.browser_manager, 
                    self.data_manager, 
                    self.config, 
                    self.logger
                )
            
            self.network_maintainer.maintain_network()
            print("✅ Network maintenance completed successfully!")
            
        except Exception as e:
            self.logger.log_error("Network maintenance failed", str(e))
            print(f"❌ Network maintenance error: {e}")

    def article_publishing(self):
        """Handle content creation and publishing"""
        print("\n✍️  Starting Content Creation & Publishing...")
        
        try:
            if not self.content_creator:
                self.content_creator = ContentCreator(
                    self.browser_manager, 
                    self.ai_provider, 
                    self.config, 
                    self.logger
                )
            
            self.content_creator.create_and_publish_content()
            print("✅ Content creation completed successfully!")
            
        except Exception as e:
            self.logger.log_error("Content creation failed", str(e))
            print(f"❌ Content creation error: {e}")

    def message_auto_reply(self):
        """Handle automatic message responses"""
        print("\n💬 Starting Message Auto-Reply Service...")
        
        try:
            if not self.message_responder:
                self.message_responder = MessageAutoResponder(
                    self.browser_manager, 
                    self.ai_provider, 
                    self.config, 
                    self.logger
                )
            
            self.message_responder.check_and_respond_to_messages()
            print("✅ Message processing completed successfully!")
            
        except Exception as e:
            self.logger.log_error("Message auto-reply failed", str(e))
            print(f"❌ Message auto-reply error: {e}")

    def open_web_dashboard(self):
        """Launch the web dashboard"""
        print("\n📊 Starting Web Dashboard...")
        
        try:
            if not self.dashboard_server:
                self.dashboard_server = DashboardServer(self.config, self.logger)
            
            # Start dashboard in a separate thread
            dashboard_thread = threading.Thread(
                target=self.dashboard_server.run,
                daemon=True
            )
            dashboard_thread.start()
            
            print("🌐 Dashboard server started!")
            print("📱 Access your dashboard at: http://localhost:8000/dashboard")
            print("⏸️  Press Enter to return to main menu...")
            input()
            
        except Exception as e:
            self.logger.log_error("Dashboard startup failed", str(e))
            print(f"❌ Dashboard error: {e}")

    def view_statistics(self):
        """Display statistics and reports"""
        print("\n📈 Generating Statistics Report...")
        
        try:
            stats = self.data_manager.get_run_statistics()
            
            print("\n📊 Current Session Statistics:")
            print(f"   • Applications submitted: {stats.get('applications', 0)}")
            print(f"   • Network connections: {stats.get('connections', 0)}")
            print(f"   • Messages processed: {stats.get('messages', 0)}")
            print(f"   • Content published: {stats.get('content', 0)}")
            
            # Show recent activity
            recent_logs = self.logger.get_recent_logs(limit=5)
            print("\n📝 Recent Activity:")
            for log in recent_logs:
                print(f"   • {log}")
                
        except Exception as e:
            self.logger.log_error("Statistics view failed", str(e))
            print(f"❌ Statistics error: {e}")

    def configuration_management(self):
        """Handle configuration settings"""
        print("\n⚙️  Configuration Management")
        print("1. View current configuration")
        print("2. Update job search keywords")
        print("3. Update network settings")
        print("4. Export configuration")
        print("5. Return to main menu")
        
        choice = input("Select option: ").strip()
        
        try:
            if choice == '1':
                print("\n📋 Current Configuration:")
                config_data = self.config.get_all_settings()
                for section, settings in config_data.items():
                    print(f"  [{section}]")
                    if isinstance(settings, dict):
                        for key, value in settings.items():
                            # Hide sensitive info
                            if 'password' in key.lower() or 'api_key' in key.lower():
                                value = "*" * len(str(value))
                            print(f"    {key}: {value}")
                    else:
                        print(f"    {settings}")
                    print()
                        
            elif choice == '2':
                current_keywords = self.config.get_setting('search.keywords', [])
                print(f"\nCurrent keywords: {', '.join(current_keywords)}")
                new_keywords = input("Enter new keywords (comma-separated): ").strip()
                if new_keywords:
                    keywords_list = [k.strip() for k in new_keywords.split(',')]
                    self.config.update_setting('search.keywords', keywords_list)
                    print("✅ Keywords updated!")
                    
            elif choice == '3':
                print("\n🤝 Network Settings:")
                current_limit = self.config.get_setting('network.connection_limit_per_day', 40)
                print(f"Current daily connection limit: {current_limit}")
                new_limit = input("Enter new daily limit (or press Enter to keep current): ").strip()
                if new_limit.isdigit():
                    self.config.update_setting('network.connection_limit_per_day', int(new_limit))
                    print("✅ Network settings updated!")
                    
            elif choice == '4':
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_file = f"config_backup_{timestamp}.json"
                self.config.export_config(export_file)
                print(f"✅ Configuration exported to {export_file}")
                
        except Exception as e:
            self.logger.log_error("Configuration management failed", str(e))
            print(f"❌ Configuration error: {e}")

    def view_logs(self):
        """Display recent logs"""
        print("\n📋 Recent Activity Logs")
        print("1. Application logs")
        print("2. Error logs")
        print("3. Network activity logs")
        print("4. All recent logs")
        print("5. Return to main menu")
        
        choice = input("Select log type: ").strip()
        
        try:
            if choice == '1':
                logs = self.logger.get_application_logs(limit=10)
            elif choice == '2':
                logs = self.logger.get_error_logs(limit=10)
            elif choice == '3':
                logs = self.logger.get_network_logs(limit=10)
            elif choice == '4':
                logs = self.logger.get_recent_logs(limit=15)
            else:
                return
                
            print(f"\n📝 Showing {len(logs)} recent entries:")
            for i, log in enumerate(logs, 1):
                print(f"{i:2d}. {log}")
                
        except Exception as e:
            self.logger.log_error("Log viewing failed", str(e))
            print(f"❌ Log viewing error: {e}")

    def run_interactive_mode(self):
        """Run the interactive menu system"""
        print("🎮 Starting Interactive Mode...")
        
        while True:
            try:
                self.show_menu()
                choice = input("\n👉 Select an option (1-9): ").strip()
                
                if choice == '1':
                    self.apply_to_jobs()
                elif choice == '2':
                    self.network_maintenance()
                elif choice == '3':
                    self.article_publishing()
                elif choice == '4':
                    self.message_auto_reply()
                elif choice == '5':
                    self.open_web_dashboard()
                elif choice == '6':
                    self.view_statistics()
                elif choice == '7':
                    self.configuration_management()
                elif choice == '8':
                    self.view_logs()
                elif choice == '9':
                    print("\n👋 Thank you for using LinkedIn Automation Platform!")
                    print("🌟 Have a productive day, Giorgiy!")
                    break
                else:
                    print("❌ Invalid option. Please select 1-9.")
                    
                # Pause between operations
                if choice in ['1', '2', '3', '4']:
                    input("\n⏸️  Press Enter to continue...")
                    
            except KeyboardInterrupt:
                print("\n\n🛑 Interrupted by user. Exiting gracefully...")
                break
            except Exception as e:
                self.logger.log_error("Menu system error", str(e))
                print(f"❌ Unexpected error: {e}")
                print("Continuing with menu...")

    def cleanup(self):
        """Clean up resources before exit"""
        try:
            self.logger.log_run_end()
            if self.browser_manager:
                self.browser_manager.cleanup()
            print("🧹 Cleanup completed successfully!")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

def main():
    """Main entry point with command line argument support"""
    parser = argparse.ArgumentParser(
        description="LinkedIn Automation Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Interactive mode
  python main.py --apply           # Direct job application
  python main.py --network         # Network maintenance
  python main.py --content         # Content creation
  python main.py --messages        # Message processing
  python main.py --dashboard       # Launch dashboard only
        """
    )
    
    parser.add_argument('--apply', action='store_true', 
                       help='Run job application automation')
    parser.add_argument('--network', action='store_true', 
                       help='Run network maintenance')
    parser.add_argument('--content', action='store_true', 
                       help='Run content creation')
    parser.add_argument('--messages', action='store_true', 
                       help='Run message auto-reply')
    parser.add_argument('--dashboard', action='store_true', 
                       help='Launch web dashboard')
    parser.add_argument('--config', default='linkedin_config.json',
                       help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize platform
    try:
        platform = LinkedInAutomationPlatform(args.config)
        
        # Handle command line modes
        if args.apply:
            platform.apply_to_jobs()
        elif args.network:
            platform.network_maintenance()
        elif args.content:
            platform.article_publishing()
        elif args.messages:
            platform.message_auto_reply()
        elif args.dashboard:
            platform.open_web_dashboard()
        else:
            # Interactive mode
            platform.run_interactive_mode()
            
    except Exception as e:
        print(f"💥 Platform startup failed: {e}")
        sys.exit(1)
    finally:
        if 'platform' in locals():
            platform.cleanup()

if __name__ == "__main__":
    main()
