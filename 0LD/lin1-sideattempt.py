#!/usr/bin/env python3
# coding: utf-8
"""
LinkedIn Automation Script — AI-assisted job applications with secure config and env-var credentials.
"""

import os, json, sys, time, random, getpass, threading, webbrowser, logging, requests, uvicorn
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Set, Any
from abc import ABC, abstractmethod

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException,
    ElementClickInterceptedException, ElementNotInteractableException,
    StaleElementReferenceException, InvalidSessionIdException
)
from fastapi import FastAPI

# ===================== CONFIGURATION =====================
@dataclass
class Config:
    CONFIG_FILE = 'linkedin_config.json'
    
    username_env_var: str = "LINKEDIN_USERNAME"
    password_env_var: str = "LINKEDIN_PASSWORD"
    browser: str = "chrome"
    resume_path: str = ""
    openai_api_key: str = ""
    auto_use_defaults: bool = False
    headless_mode: bool = False
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    page_load_wait_min: int = 5
    page_load_wait_max: int = 10
    step_wait_seconds: float = 1.0
    autocomplete_timeout: int = 3
    default_wait_time: int = 10
    resume_excerpt_lines: int = 20
    max_application_steps: int = 6
    search_keywords: List[str] = field(default_factory=lambda: [
        "web developer", ".NET Developer", "C# Developer"
    ])
    dashboard_port: int = 8000
    dashboard_autolaunch: bool = True

    @classmethod
    def load(cls) -> 'Config':
        cfg = cls()
        if os.path.exists(cls.CONFIG_FILE):
            data = json.load(open(cls.CONFIG_FILE))
            for k in cfg.__annotations__:
                if k in data: setattr(cfg, k, data[k])
        cfg.resume_path = os.path.abspath(cfg.resume_path) if cfg.resume_path else ""
        return cfg

    def save(self):
        data = {}
        for k in self.__annotations__:
            if k in ["username_env_var", "password_env_var", "browser", "resume_path", "openai_api_key",
                      "auto_use_defaults", "headless_mode", "retry_attempts", "retry_delay_seconds",
                      "page_load_wait_min", "page_load_wait_max", "step_wait_seconds",
                      "autocomplete_timeout", "default_wait_time", "resume_excerpt_lines",
                      "max_application_steps", "search_keywords",
                      "dashboard_port", "dashboard_autolaunch"]:
                data[k] = getattr(self, k)
        json.dump(data, open(self.CONFIG_FILE, 'w'), indent=4)

# ===================== LOGGER =====================
class Logger:
    def __init__(self, config: Config):
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(levelname)s - %(message)s')
        self.log = logging.getLogger('LinkedInAutomation')
        fh = logging.FileHandler('error.log')
        fh.setLevel(logging.ERROR)
        self.log.addHandler(fh)
    
    def info(self, m): self.log.info(m); print(m)
    def warning(self, m): self.log.warning(m); print(f"⚠️ {m}")
    def error(self, m): self.log.error(m); print(f"❌ ERROR: {m}")

# ===================== STATISTICS =====================
@dataclass
class Statistics:
    applied: int = 0
    skipped: int = 0
    errors: int = 0
    jobs_processed: int = 0
    start_time: float = field(default_factory=time.time)
    
    def to_dict(self):
        uptime = time.time() - self.start_time
        return {'applied':self.applied,'skipped':self.skipped,
                'errors':self.errors,'jobs_processed':self.jobs_processed,
                'uptime':uptime}
    
    def throughput(self):
        uptime = time.time() - self.start_time
        return {'per_hour': self.applied/(uptime/3600) if uptime else 0.0}

@dataclass
class NetworkStatistics:
    actions_accepted: int = 0
    actions_sent: int = 0
    attempts: int = 0

class StatsManager:
    def __init__(self, config: Config):
        self.config = config
        self.current = Statistics()
        self.network = NetworkStatistics()

    def load_overall(self):
        if os.path.exists('overall_stats.json'):
            return json.load(open('overall_stats.json'))
        return {
            'run_count':0,'applied':0,'skipped':0,'errors':0,'jobs_processed':0,
            'network':{'actions_accepted':0,'actions_sent':0,'attempts':0},
            'last_run_timestamp':None
        }

    def update_overall(self):
        o = self.load_overall()
        o.update({
            'run_count': o['run_count']+1,
            'applied': o['applied']+self.current.applied,
            'skipped': o['skipped']+self.current.skipped,
            'errors': o['errors']+self.current.errors,
            'jobs_processed': o['jobs_processed']+self.current.jobs_processed,
            'last_run_timestamp': datetime.now().isoformat()
        })
        o['network']['actions_accepted'] += self.network.actions_accepted
        o['network']['actions_sent'] += self.network.actions_sent
        o['network']['attempts'] += self.network.attempts
        json.dump(o, open('overall_stats.json','w'), indent=4)
        return o

    def dump_dashboard(self):
        json.dump({
            **self.current.to_dict(),
            'network': vars(self.network)
        }, open('dashboard_data.json','w'), indent=4)

# ===================== DATA MANAGER =====================
class DataManager:
    def __init__(self, config): self.cfg=config
    def load_questions(self):
        return json.load(open(self.cfg.CONFIG_FILE.replace('config','questions'))) \
            if os.path.exists(self.cfg.CONFIG_FILE.replace('config','questions')) else {}
    def save_questions(self, qs): json.dump(qs, open(self.cfg.CONFIG_FILE.replace('config','questions'),'w'), indent=4)
    def log_application(self, title, comp, url, resume, status):
        entry = {'job_title':title,'company':comp,'url':url,'resume':resume,
                 'status':status,'date':datetime.now().isoformat()}
        jfn = self.cfg.CONFIG_FILE.replace('config','applications')
        lst = json.load(open(jfn)) if os.path.exists(jfn) else []
        lst.append(entry)
        json.dump(lst, open(jfn,'w'), indent=4)
    def get_applied_urls(self):
        jfn = self.cfg.CONFIG_FILE.replace('config','applications')
        if os.path.exists(jfn):
            return {e['url'] for e in json.load(open(jfn)) if e.get('url')}
        return set()

# ===================== AI Provider =====================
class AIProvider(ABC):
    @abstractmethod
    def generate_answer(self, label, ftype, context=""): ...
        
class ChatGPTProvider(AIProvider):
    def __init__(self, key, model='gpt-3.5-turbo'):
        self.key = key
        self.model = model
        self.url = "https://api.openai.com/v1/chat/completions"
    
    def generate_answer(self, label, ftype, context=""):
        if not self.key:
            return f"Default answer for {label}"
        prompt = f"Provide concise answer for field '{label}' of type '{ftype}'."
        if context: prompt += f"\nContext:\n{context}"
        headers={'Authorization':f"Bearer {self.key}", 'Content-Type':'application/json'}
        payload={'model':self.model,'messages':[{'role':'user','content':prompt}],
                 'temperature':0.7,'max_tokens':200}
        try:
            r=requests.post(self.url, json=payload, headers=headers, timeout=15)
            if r.status_code==200:
                return r.json()['choices'][0]['message']['content'].strip()
        except:
            pass
        return f"Default answer for {label}"
    
    def generate_cover_letter(self, title, comp, jd, resume_excerpt):
        if not self.key:
            return f"Dear {comp}, I’m very interested in {title}."
        prompt = (
            f"Write a professional cover letter for '{title}' at '{comp}'.\n"
            f"Job Description:\n{jd}\n"
            f"Resume excerpt:\n{resume_excerpt}\n"
            "Tailored, concise and compelling."
        )
        headers={'Authorization':f"Bearer {self.key}", 'Content-Type':'application/json'}
        payload={'model':self.model,'messages':[{'role':'user','content':prompt}],
                 'temperature':0.7,'max_tokens':500}
        try:
            r=requests.post(self.url, json=payload, headers=headers, timeout=20)
            if r.status_code==200:
                return r.json()['choices'][0]['message']['content'].strip()
        except:
            pass
        return f"Dear {comp}, I’m very interested in {title}."

# ===================== BROWSER MANAGER =====================
class BrowserManager:
    def __init__(self, cfg:Config, log:Logger):
        self.cfg, self.log = cfg, log
        self.driver = None
        self.timeout = cfg.default_wait_time

    def init_browser(self):
        opts = webdriver.ChromeOptions()
        if self.cfg.headless_mode: opts.add_argument('--headless')
        if self.cfg.browser=='chrome':
            opts.binary_location = os.getenv('CHROMIUM_BINARY','/usr/bin/chromium-browser')
            try:
                self.driver = webdriver.Chrome(service=Service(os.getenv('CHROMEDRIVER_PATH','/usr/bin/chromedriver')), options=opts)
            except Exception as e:
                self.log.error(f"Chrome init failed: {e}"); sys.exit(1)
        else:
            self.driver = webdriver.Edge()
        self.driver.implicitly_wait(self.timeout)
        return self.driver

    def login(self, username, password):
        try:
            self.driver.get("https://www.linkedin.com/login")
            WebDriverWait(self.driver,self.timeout).until(EC.presence_of_element_located((By.ID,'username')))
            self.driver.find_element(By.ID,'username').send_keys(username)
            self.driver.find_element(By.ID,'password').send_keys(password+Keys.RETURN)
            WebDriverWait(self.driver,20).until(EC.url_contains('feed'))
            self.log.info("Logged in")
            return True
        except TimeoutException:
            self.log.error("Login timed out")
            return False
        except Exception as e:
            self.log.error(f"Login error: {e}")
            return False

    def safe_click(self, el):
        for _ in range(self.cfg.retry_attempts):
            try:
                el.click()
                return True
            except (ElementClickInterceptedException, ElementNotInteractableException):
                time.sleep(self.cfg.retry_delay_seconds)
                self.driver.execute_script("arguments[0].scrollIntoView(true);", el)
            except StaleElementReferenceException:
                return False
            except Exception as e:
                self.log.warning(f"Click issue: {e}")
                time.sleep(self.cfg.retry_delay_seconds)
        return False

    def quit(self):
        if self.driver: self.driver.quit()

# ===================== FORM FILLER =====================
class FormFiller:
    def __init__(self, browser:BrowserManager, ai:AIProvider, data:DataManager, cfg:Config, log:Logger):
        self.browser, self.ai, self.data, self.cfg, self.log = browser, ai, data, cfg, log

    def fill_text_field(self, fld, label, questions):
        if "cover" in label.lower():
            title = self._safe_get(lambda: self.browser.driver.find_element(By.CSS_SELECTOR,'.job-details-jobs-unified-top-card__job-title').text, "Unknown Job")
            comp  = self._safe_get(lambda: self.browser.driver.find_element(By.CSS_SELECTOR,'.job-details-jobs-unified-top-card__company-name').text, "Company")
            jd    = self._safe_get(lambda: WebDriverWait(self.browser.driver,self.cfg.default_wait_time).until(EC.presence_of_element_located((By.CSS_SELECTOR,'.jobs-description__content'))).text, "")
            resume_excerpt = self._get_resume_excerpt()
            ans = self.ai.generate_cover_letter(title, comp, jd, resume_excerpt)
            self.log.info(f"Generated cover letter for '{title}' at '{comp}'")
        else:
            if label in questions and questions[label].strip():
                ans = questions[label]
            else:
                pre = fld.get_attribute('value').strip()
                if pre and self.cfg.auto_use_defaults:
                    ans = pre
                else:
                    ans = self.ai.generate_answer(label, "text")
                questions[label] = ans
        fld.clear()
        fld.send_keys(ans)
        try:
            WebDriverWait(self.browser.driver, self.cfg.autocomplete_timeout).until(EC.visibility_of_element_located((By.CSS_SELECTOR,"li.artdeco-typeahead__hit"))).click()
        except: pass
        return ans

    def fill_form(self):
        q = self.data.load_questions()
        try:
            modal = self.browser.driver.find_element(By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
        except NoSuchElementException:
            self.log.warning("Easy Apply modal missing")
            return
        for fld in modal.find_elements(By.CSS_SELECTOR,"input[type='text'],textarea"):
            label = fld.find_element(By.XPATH,"ancestor::div[label]//label").text.strip().splitlines()[0]
            self.fill_text_field(fld, label, q)
        self.data.save_questions(q)

    def _get_resume_excerpt(self):
        if not os.path.exists(self.cfg.resume_path): return ""
        txt = []
        with open(self.cfg.resume_path, encoding='utf-8') as f:
            for _ in range(self.cfg.resume_excerpt_lines):
                try:
                    txt.append(next(f).strip())
                except StopIteration:
                    break
        return "\n".join(txt)

    def _safe_get(self, getter, fallback=""):
        try: return getter()
        except: return fallback

# ===================== JOB APPLICATION MANAGER =====================
class JobApplicationManager:
    def __init__(self, browser:BrowserManager, filler:FormFiller, data:DataManager, stats:StatsManager, cfg:Config, log:Logger):
        self.browser, self.filler, self.data, self.stats, self.cfg, self.log = browser,filler,data,stats,cfg,log
        self.kw = cfg.search_keywords

    def get_applied_jobs(self) -> Set[str]:
        seen = set()
        try:
            self.browser.driver.get("https://www.linkedin.com/my-items/saved-jobs/?cardType=APPLIED")
            WebDriverWait(self.browser.driver, self.cfg.default_wait_time).until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR,"ul.jobs-saved-jobs-list li a.job-card-list__title"))
            )
            for elm in self.browser.driver.find_elements(By.CSS_SELECTOR,"ul.jobs-saved-jobs-list li a.job-card-list__title"):
                href = elm.get_attribute("href")
                if href: seen.add(href)
        except Exception as e:
            self.log.warning(f"Could not retrieve applied jobs: {e}")
        return seen

    def handle_multi_step(self, resume):
        for i in range(self.cfg.max_application_steps):
            time.sleep(self.cfg.step_wait_seconds)
            if i == 0:
                try:
                    u = WebDriverWait(self.browser.driver,self.cfg.default_wait_time).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR,"input[type='file']"))
                    )
                    u.send_keys(resume)
                except: pass
            try:
                btn = WebDriverWait(self.browser.driver, self.cfg.step_wait_seconds).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR,"button[data-live-test-easy-apply-review-button],button[aria-label='Review your application'],//button[contains(.,'Review')]"))
                )
                self.browser.safe_click(btn)
                continue
            except: pass
            if self._click("submit"): return True
            if self._click("next"): continue
            break
        return True

    def _click(self, mode):
        sel = {
            'next': "button[data-live-test-easy-apply-next-button], button[data-easy-apply-next-button], //button[normalize-space(text())='Next']",
            'submit': "button[data-live-test-easy-apply-submit-button], button[aria-label='Submit application']"
        }
        try:
            btn = WebDriverWait(self.browser.driver,self.cfg.default_wait_time).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR+"," + sel[mode]))
            )
            self.browser.safe_click(btn)
            return True
        except:
            return False

    def apply(self, resume):
        seen = self.data.get_applied_urls() | self.get_applied_jobs()
        for term in self.kw:
            self.log.info(f"Searching: {term}")
            if not self._nav_page(term, 0): continue
            page = 0
            while True:
                cards = self._get_cards()
                if not cards: break
                for inc, card in enumerate(cards):
                    self._process_card(card, resume, seen)
                page += 1
                if not self._nav_page(term, page): break

    def _nav_page(self, term, page):
        if page == 0:
            url = f"https://www.linkedin.com/jobs/search/?f_AL=true&keywords={term}&location=Remote"
            self.browser.driver.get(url)
        else:
            try:
                nb = WebDriverWait(self.browser.driver, self.cfg.step_wait_seconds).until(
                    EC.element_to_be_clickable((By.XPATH,"//button[.//span[contains(text(),'Next')]]"))
                )
                nb.click()
            except:
                self.browser.driver.get(f"https://www.linkedin.com/jobs/search/?f_AL=true&keywords={term}&location=Remote&start={page*25}")
        time.sleep(random.uniform(self.cfg.page_load_wait_min, self.cfg.page_load_wait_max))
        return bool(WebDriverWait(self.browser.driver,self.cfg.step_wait_seconds).until(
            EC.presence_of_all_elements_located((By.CLASS_NAME,'job-card-container'))
        ))

    def _get_cards(self):
        return self.browser.driver.find_elements(By.CLASS_NAME,'job-card-container')

    def _process_card(self, card, resume, seen):
        self.stats.current.jobs_processed += 1
        if "applied" in card.text.lower(): self.stats.current.skipped += 1; return
        try:
            job_link = card.find_element(By.TAG_NAME,'a').get_attribute('href')
        except:
            self.stats.current.skipped += 1
            return
        if not job_link or job_link in seen:
            self.stats.current.skipped += 1
            return
        self.browser.driver.execute_script("arguments[0].scrollIntoView(true);", card)
        if not self.browser.safe_click(card):
            self.stats.current.skipped += 1
            return
        time.sleep(random.uniform(self.cfg.page_load_wait_min,self.cfg.page_load_wait_max))
        try:
            apply_btn = WebDriverWait(self.browser.driver,self.cfg.step_wait_seconds).until(
                EC.element_to_be_clickable((By.XPATH,"//button[contains(@class,'jobs-apply-button')]"))
            )
        except:
            self.stats.current.skipped += 1
            return
        self.browser.safe_click(apply_btn)
        time.sleep(self.cfg.step_wait_seconds)
        if not self.handle_multi_step(resume):
            self.stats.current.skipped += 1
            return
        title = self._safe_select_text('.job-details-jobs-unified-top-card__job-title')
        company = self._safe_select_text('.job-details-jobs-unified-top-card__company-name')
        self.data.log_application(title,company,job_link,resume,'submitted')
        self.log.info(f"Applied to {title} @ {company}")
        self.stats.current.applied += 1
        seen.add(job_link)
        self.stats.dump_dashboard()

    def _safe_select_text(self, sel):
        try:
            return self.browser.driver.find_element(By.CSS_SELECTOR,sel).text.strip()
        except:
            return "Unknown"

# ===================== DASHBOARD SERVER =====================
class DashboardServer:
    def __init__(self, cfg):
        self.cfg=cfg; self.app=FastAPI()
        @self.app.get("/")  
        def read(): return json.load(open('dashboard_data.json')) if os.path.exists('dashboard_data.json') else {"error":"not-ready"}
        @self.app.get("/stats")
        def stats(): return StatsManager(self.cfg).load_overall()

    def run(self):
        uvicorn.run(self.app, host="0.0.0.0", port=self.cfg.dashboard_port)

# ===================== CLI =====================
class CLI:
    def __init__(self):
        self.cfg = Config.load()
        self.log = Logger(self.cfg)
        self.stats = StatsManager(self.cfg)
        self.data = DataManager(self.cfg)
        self.browser = None
        self.ai = None

    def run(self):
        if len(sys.argv)>1 and sys.argv[1]=='--apply':
            return self.apply_loop()
        return self.menu()

    def menu(self):
        while True:
            print("\n1) Apply Jobs  2) Dashboard  3) Update Config  4) Quit")
            c=input("> ").strip()
            if c=='1': self.apply_loop()
            elif c=='2': self.launch_dashboard()
            elif c=='3': self.update_settings()
            elif c=='4': break

    def get_creds(self):
        usr=os.getenv(self.cfg.username_env_var)
        pwd=os.getenv(self.cfg.password_env_var)
        if not usr:
            usr=input("Username: ").strip()
        if not pwd:
            pwd=getpass.getpass("Password: ")
        return usr,pwd

    def apply_loop(self):
        usr,pwd=self.get_creds()
        if not os.path.exists(self.cfg.resume_path):
            self.cfg.resume_path=input("Resume path: ").strip()
        self.browser = BrowserManager(self.cfg, self.log)
        self.ai = ChatGPTProvider(self.cfg.openai_api_key)
        filler = FormFiller(self.browser, self.ai, self.data, self.cfg, self.log)
        manager = JobApplicationManager(self.browser, filler, self.data, self.stats, self.cfg, self.log)
        self.browser.init_browser()
        if not self.browser.login(usr, pwd):
            self.log.error("Login Failure"); return
        try:
            manager.apply(self.cfg.resume_path)
            self.stats.update_overall()
        except InvalidSessionIdException:
            self.log.warning("Session lost, retrying once...")
            self.browser.quit()
            self.browser.init_browser()
            if self.browser.login(usr, pwd):
                manager.apply(self.cfg.resume_path)
                self.stats.update_overall()
        finally:
            self.browser.quit()

    def launch_dashboard(self):
        self.log.info(f"Dashboard on port {self.cfg.dashboard_port}")
        if self.cfg.dashboard_autolaunch:
            webbrowser.open(f"http://127.0.0.1:{self.cfg.dashboard_port}")
        threading.Thread(target=DashboardServer(self.cfg).run, daemon=True).start()

    def update_settings(self):
        print("Update settings in config file then run config.save() manually.")
        input("Press Enter when done...")

# ===================== ENTRYPOINT =====================
if __name__ == "__main__":
    CLI().run()
