#!/usr/bin/env python3
import os
import re
import sys

def reconstruct(snapshot_path: str, output_root: str = "."):
    # Patterns for the headers/footers
    header_re = re.compile(r"^=== FILE: (.+) ===$")
    footer_re_tmpl = r"^=== END FILE: {path} ===$"
    
    with open(snapshot_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        header_match = header_re.match(lines[i].rstrip("\n"))
        if header_match:
            relpath = header_match.group(1)
            footer_re = re.compile(footer_re_tmpl.format(path=re.escape(relpath)))
            i += 1
            content = []
            # Collect until footer
            while i < len(lines) and not footer_re.match(lines[i].rstrip("\n")):
                content.append(lines[i])
                i += 1
            # Skip the footer line
            i += 1
            
            # Write out the file
            outpath = os.path.join(output_root, relpath)
            os.makedirs(os.path.dirname(outpath), exist_ok=True)
            mode = "wb" if any(l.startswith("[BINARY") for l in content) else "w"
            
            if mode == "w":
                with open(outpath, mode, encoding="utf-8") as out:
                    out.writelines(content)
            else:
                # strip the “[BINARY – base64 below]” line and decode
                b64data = "".join(content[1:])  # skip first line
                import base64
                with open(outpath, "wb") as out:
                    out.write(base64.b64decode(b64data))
            
            print(f"Recreated: {relpath}")
        else:
            i += 1

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: reconstruct_from_snapshot.py snapshot.txt [output_directory]")
        sys.exit(1)
    snapshot = sys.argv[1]
    outdir   = sys.argv[2] if len(sys.argv) > 2 else "."
    reconstruct(snapshot, outdir)
