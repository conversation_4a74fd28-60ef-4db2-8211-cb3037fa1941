#!/usr/bin/env python3
import os
import sys
import argparse
import base64

def dump_folder_contents(root, include_exts, ignore_exts):
    for dirpath, dirnames, filenames in os.walk(root):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            relpath = os.path.relpath(filepath, root)
            _, ext = os.path.splitext(filename.lower())

            # decide whether to skip
            if include_exts and ext not in include_exts:
                continue
            if ignore_exts and ext in ignore_exts:
                continue
            # skip the script itself
            if os.path.abspath(filepath) == os.path.abspath(__file__):
                continue

            print(f"\n=== FILE: {relpath} ===")
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    print(f.read(), end='')
            except UnicodeDecodeError:
                print("[BINARY – base64 below]")
                with open(filepath, 'rb') as f:
                    print(base64.b64encode(f.read()).decode('ascii'))
            print(f"\n=== END FILE: {relpath} ===")

def parse_args():
    p = argparse.ArgumentParser(
        description="Dump folder contents with optional include/ignore by extension."
    )
    p.add_argument(
        "root",
        nargs="?",
        default=os.getcwd(),
        help="Root folder to walk (default: current working dir)"
    )
    p.add_argument(
        "--include",
        "-i",
        nargs="+",
        metavar="EXT",
        help="Only include these extensions (e.g. -i .py .js .html)"
    )
    p.add_argument(
        "--ignore",
        "-x",
        nargs="+",
        metavar="EXT",
        help="Skip these extensions (e.g. -x .log .md)"
    )
    return p.parse_args()

if __name__ == "__main__":
    args = parse_args()
    # normalize to lowercase and ensure leading dot
    include_exts = {e.lower() if e.startswith('.') else f".{e.lower()}" for e in args.include} if args.include else set()
    ignore_exts  = {e.lower() if e.startswith('.') else f".{e.lower()}" for e in args.ignore}  if args.ignore  else set()
    dump_folder_contents(args.root, include_exts, ignore_exts)
    print(f"Include extensions: {include_exts}")
    print(f"Ignore extensions: {ignore_exts}")
    dump_folder_contents(args.root, include_exts, ignore_exts)
    print("\nDone.")
    print("Run this script with --help for usage information.")
    print("You can specify --include or --ignore to filter file types.")
    print("Example: python dump_folder.py --include .py .txt --ignore .log")