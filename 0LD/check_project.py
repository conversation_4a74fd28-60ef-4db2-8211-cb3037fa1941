#!/usr/bin/env python3
"""
Diagnostic script to check LinkedIn automation project structure
This will help identify what modules and classes are actually available
"""

import os
import ast
import json
import importlib.util

def check_python_file(filepath):
    """Check a Python file for classes and main functions"""
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        classes = []
        functions = []
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
            elif isinstance(node, ast.FunctionDef) and node.col_offset == 0:  # Top-level functions
                functions.append(node.name)
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        
        return {
            'classes': classes,
            'functions': functions,
            'imports': list(set(imports)),
            'size': len(content),
            'lines': content.count('\n')
        }
    except Exception as e:
        return {'error': str(e)}

def check_json_file(filepath):
    """Check if JSON file is valid and get structure"""
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        if isinstance(data, dict):
            return {
                'valid': True,
                'keys': list(data.keys()),
                'type': 'dict'
            }
        elif isinstance(data, list):
            return {
                'valid': True,
                'length': len(data),
                'type': 'list'
            }
        else:
            return {'valid': True, 'type': type(data).__name__}
    except Exception as e:
        return {'valid': False, 'error': str(e)}

def main():
    print("LinkedIn Automation Project Diagnostic")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"\nCurrent directory: {current_dir}")
    
    # Check for key files
    print("\n[Core Configuration Files]")
    config_files = ['linkedin_config.json', 'config.json']
    for cf in config_files:
        if os.path.exists(cf):
            result = check_json_file(cf)
            print(f"✓ {cf}: {result}")
    
    # Check Python modules
    print("\n[Python Modules Analysis]")
    
    # Core modules to check
    modules_to_check = [
        ('enhanced_config.py', 'Configuration module'),
        ('enhanced_logging.py', 'Logging module'),
        ('integration_adapter.py', 'Integration module'),
        ('network_maintenance_module.py', 'Network maintenance'),
        ('article_publishing_module.py', 'Article publishing'),
        ('message_autoreply_module.py', 'Message auto-reply'),
        ('web_dashboard_backend.py', 'Web dashboard backend'),
        ('lin.py', 'Main automation script'),
        ('complete_enhanced_linkedin.py', 'Enhanced automation'),
        ('main.py', 'Main entry point')
    ]
    
    for module_file, description in modules_to_check:
        if os.path.exists(module_file):
            result = check_python_file(module_file)
            if 'error' not in result:
                print(f"\n✓ {module_file} ({description}):")
                print(f"  - Lines: {result['lines']}")
                print(f"  - Classes: {', '.join(result['classes']) if result['classes'] else 'None'}")
                print(f"  - Main functions: {', '.join([f for f in result['functions'] if not f.startswith('_')][:5])}")
            else:
                print(f"✗ {module_file}: Error - {result['error']}")
        else:
            print(f"✗ {module_file}: Not found")
    
    # Check for other LinkedIn scripts
    print("\n[Other LinkedIn Scripts Found]")
    py_files = [f for f in os.listdir('.') if f.endswith('.py') and 'lin' in f.lower()]
    for pf in sorted(py_files):
        if pf not in [m[0] for m in modules_to_check]:
            size = os.path.getsize(pf)
            print(f"  - {pf} ({size} bytes)")
    
    # Check directories
    print("\n[Directories]")
    dirs = [d for d in os.listdir('.') if os.path.isdir(d) and not d.startswith('.')]
    for d in sorted(dirs):
        file_count = len([f for f in os.listdir(d) if os.path.isfile(os.path.join(d, f))])
        print(f"  - {d}/ ({file_count} files)")
    
    # Check log files
    print("\n[Log and Data Files]")
    log_files = [f for f in os.listdir('.') if f.endswith(('.log', '.json')) and 'config' not in f]
    for lf in sorted(log_files)[:10]:  # Show first 10
        size = os.path.getsize(lf)
        print(f"  - {lf} ({size} bytes)")
    
    # Try to import and check main automation class
    print("\n[Import Test Results]")
    
    # Test config import
    try:
        import enhanced_config
        if hasattr(enhanced_config, 'EnhancedConfig'):
            print("✓ enhanced_config.EnhancedConfig found")
        elif hasattr(enhanced_config, 'load_config'):
            print("✓ enhanced_config.load_config() function found")
        else:
            print("! enhanced_config imported but no expected exports found")
            print(f"  Available: {[x for x in dir(enhanced_config) if not x.startswith('_')]}")
    except ImportError as e:
        print(f"✗ Could not import enhanced_config: {e}")
    
    # Test main automation import
    for module_name in ['lin', 'complete_enhanced_linkedin']:
        try:
            module = importlib.import_module(module_name)
            classes = [x for x in dir(module) if not x.startswith('_') and x[0].isupper()]
            if classes:
                print(f"✓ {module_name} classes found: {', '.join(classes[:3])}")
            else:
                print(f"! {module_name} imported but no classes found")
        except ImportError as e:
            print(f"✗ Could not import {module_name}: {e}")
    
    print("\n[Recommendations]")
    print("Based on the analysis above, here are the next steps:")
    
    # Give specific recommendations based on what was found
    if not os.path.exists('linkedin_config.json'):
        print("1. Create linkedin_config.json with your credentials")
    
    if not any(os.path.exists(f) for f in ['lin.py', 'complete_enhanced_linkedin.py']):
        print("2. You need a main automation script (lin.py or complete_enhanced_linkedin.py)")
    
    print("\nRun 'python3 main.py' to start the platform")

if __name__ == "__main__":
    main()