#!/usr/bin/env python3
"""
Message Auto-Reply Module for LinkedIn Automation
Checks messages and sends tailored responses with customized resumes
"""
import time
import random
import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

@dataclass
class Message:
    """Represents a LinkedIn message"""
    sender_name: str
    sender_title: str
    sender_company: str
    sender_profile_url: str
    message_text: str
    timestamp: str
    conversation_id: str
    is_recruiter: bool = False
    has_job_opportunity: bool = False
    job_details: Optional[Dict] = None
    
@dataclass
class JobInquiry:
    """Represents a job inquiry from a message"""
    company: str
    position: str
    requirements: List[str]
    recruiter_name: str
    original_message: str
    extracted_salary: Optional[str] = None
    location: Optional[str] = None
    job_type: Optional[str] = None

class MessageAutoResponder:
    """Handles automatic message responses and resume tailoring"""
    
    def __init__(self, browser_manager, ai_provider, config, logger):
        self.browser = browser_manager
        self.ai = ai_provider
        self.config = config
        self.logger = logger
        self.processed_conversations = self.load_processed_conversations()
        
    def check_and_respond_to_messages(self):
        """Main method to check messages and send responses"""
        self.logger.info("Checking LinkedIn messages for job opportunities")
        
        try:
            # Navigate to messages
            self.browser.driver.get("https://www.linkedin.com/messaging/")
            time.sleep(3)
            
            # Get unread messages
            unread_messages = self.get_unread_messages()
            
            self.logger.info(f"Found {len(unread_messages)} unread messages")
            
            # Process each message
            for message in unread_messages:
                if message.conversation_id not in self.processed_conversations:
                    self.process_message(message)
                    
                    # Add delay between processing messages
                    time.sleep(random.uniform(5, 10))
                    
            # Save processed conversations
            self.save_processed_conversations()
            
        except Exception as e:
            self.logger.error(f"Error checking messages: {e}")
            
    def get_unread_messages(self) -> List[Message]:
        """Get all unread messages"""
        messages = []
        
        try:
            # Find all conversation items
            conversations = self.browser.driver.find_elements(
                By.XPATH, 
                "//li[contains(@class,'msg-conversation-listitem')]"
            )
            
            for conv in conversations[:20]:  # Limit to 20 most recent
                try:
                    # Check if unread
                    unread_indicator = conv.find_elements(
                        By.XPATH, 
                        ".//span[contains(@class,'msg-conversation-card__unread-count')]"
                    )
                    
                    if unread_indicator:
                        # Click to open conversation
                        self.browser.safe_click(conv)
                        time.sleep(2)
                        
                        # Extract message details
                        message = self.extract_message_details()
                        if message:
                            messages.append(message)
                            
                except Exception as e:
                    self.logger.debug(f"Error processing conversation: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error getting unread messages: {e}")
            
        return messages
        
    def extract_message_details(self) -> Optional[Message]:
        """Extract details from current message"""
        try:
            # Get sender info
            sender_elem = self.browser.driver.find_element(
                By.XPATH, 
                "//h2[@class='msg-entity-lockup__entity-title']"
            )
            sender_name = sender_elem.text.strip()
            
            # Get sender title
            title_elem = self.browser.driver.find_elements(
                By.XPATH,
                "//p[@class='msg-entity-lockup__entity-headline']"
            )
            sender_title = title_elem[0].text.strip() if title_elem else ""
            
            # Get latest message
            messages = self.browser.driver.find_elements(
                By.XPATH,
                "//div[@class='msg-s-event-listitem__body']"
            )
            
            if messages:
                latest_message = messages[-1].text.strip()
                
                # Get conversation ID
                conv_id = self.browser.driver.current_url.split('/')[-1]
                
                # Check if recruiter
                is_recruiter = self.is_recruiter_message(sender_title, latest_message)
                
                # Check for job opportunity
                has_job = self.contains_job_opportunity(latest_message)
                
                # Extract job details if present
                job_details = None
                if has_job:
                    job_details = self.extract_job_details(latest_message)
                    
                return Message(
                    sender_name=sender_name,
                    sender_title=sender_title,
                    sender_company=self.extract_company_from_title(sender_title),
                    sender_profile_url="",
                    message_text=latest_message,
                    timestamp=datetime.now().isoformat(),
                    conversation_id=conv_id,
                    is_recruiter=is_recruiter,
                    has_job_opportunity=has_job,
                    job_details=job_details
                )
                
        except Exception as e:
            self.logger.error(f"Error extracting message details: {e}")
            
        return None
        
    def is_recruiter_message(self, sender_title: str, message_text: str) -> bool:
        """Check if message is from a recruiter"""
        recruiter_keywords = [
            'recruiter', 'talent', 'hiring', 'staffing', 'hr', 
            'human resources', 'acquisition', 'recruitment'
        ]
        
        title_lower = sender_title.lower()
        message_lower = message_text.lower()
        
        for keyword in recruiter_keywords:
            if keyword in title_lower:
                return True
                
        # Check message content
        recruiter_phrases = [
            'looking for', 'opportunity', 'position', 'role',
            'job opening', 'interested in your profile'
        ]
        
        for phrase in recruiter_phrases:
            if phrase in message_lower:
                return True
                
        return False
        
    def contains_job_opportunity(self, message_text: str) -> bool:
        """Check if message contains job opportunity"""
        job_keywords = self.config.auto_reply.keywords_job_inquiry
        
        message_lower = message_text.lower()
        
        for keyword in job_keywords:
            if keyword.lower() in message_lower:
                return True
                
        return False
        
    def extract_job_details(self, message_text: str) -> Dict:
        """Extract job details from message"""
        details = {
            'position': None,
            'company': None,
            'location': None,
            'salary': None,
            'job_type': None,
            'requirements': []
        }
        
        # Extract position
        position_patterns = [
            r'position:?\s*([^\n]+)',
            r'role:?\s*([^\n]+)',
            r'looking for (?:a |an )?([^\n]+)',
            r'hiring (?:a |an )?([^\n]+)'
        ]
        
        for pattern in position_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                details['position'] = match.group(1).strip()
                break
                
        # Extract location
        location_patterns = [
            r'location:?\s*([^\n]+)',
            r'based in\s*([^\n]+)',
            r'located in\s*([^\n]+)'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                details['location'] = match.group(1).strip()
                break
                
        # Extract salary
        salary_patterns = [
            r'\$[\d,]+(?:\s*-\s*\$[\d,]+)?',
            r'[\d,]+k(?:\s*-\s*[\d,]+k)?',
            r'salary:?\s*([^\n]+)'
        ]
        
        for pattern in salary_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                details['salary'] = match.group(0).strip()
                break
                
        # Extract requirements
        req_section = re.search(
            r'requirements?:?\s*([^\.]+)', 
            message_text, 
            re.IGNORECASE | re.DOTALL
        )
        
        if req_section:
            req_text = req_section.group(1)
            # Split by common separators
            requirements = re.split(r'[•\-\n]+', req_text)
            details['requirements'] = [
                req.strip() for req in requirements 
                if req.strip() and len(req.strip()) > 5
            ]
            
        return details
        
    def extract_company_from_title(self, title: str) -> str:
        """Extract company name from title"""
        # Common patterns: "Title at Company"
        match = re.search(r'at\s+(.+?)(?:\s*[\|\-]|$)', title)
        if match:
            return match.group(1).strip()
        return ""
        
    def process_message(self, message: Message):
        """Process a single message and respond if appropriate"""
        self.logger.info(f"Processing message from {message.sender_name}")
        
        if message.has_job_opportunity:
            # Check if job description is provided
            if message.job_details and message.job_details.get('position'):
                # Create tailored resume and respond
                self.respond_with_tailored_resume(message)
            else:
                # Ask for job description
                self.ask_for_job_description(message)
        else:
            # Send general response if configured
            if self.config.auto_reply.enabled:
                self.send_general_response(message)
                
        # Mark conversation as processed
        self.processed_conversations.add(message.conversation_id)
        
    def respond_with_tailored_resume(self, message: Message):
        """Create tailored resume and send response"""
        try:
            # Generate tailored resume
            resume_path = self.create_tailored_resume(message)
            
            # Generate tailored response
            response = self.generate_tailored_response(message)
            
            # Send response
            self.send_message_response(response)
            
            # Attach resume if possible (LinkedIn limitations)
            self.attach_resume_instructions(resume_path)
            
            self.logger.info(f"Sent tailored response to {message.sender_name}")
            
        except Exception as e:
            self.logger.error(f"Error sending tailored response: {e}")
            
    def create_tailored_resume(self, message: Message) -> str:
        """Create a tailored version of the resume"""
        try:
            # Load base resume
            base_resume_path = self.config.paths.resume_path
            
            if not os.path.exists(base_resume_path):
                self.logger.error("Base resume not found")
                return base_resume_path
                
            # Create new document
            doc = Document(base_resume_path)
            
            # Add tailored objective/summary if position is known
            if message.job_details and message.job_details.get('position'):
                position = message.job_details['position']
                company = message.sender_company or "your company"
                
                # Generate tailored objective
                objective = self.generate_tailored_objective(position, company, message)
                
                # Add to document (this is simplified - real implementation would be more sophisticated)
                # You would need to find and replace the existing objective section
                
            # Save tailored resume
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"Resume_Tailored_{message.sender_company}_{timestamp}.docx"
            tailored_path = os.path.join(
                os.path.dirname(base_resume_path),
                filename
            )
            
            doc.save(tailored_path)
            
            self.logger.info(f"Created tailored resume: {filename}")
            return tailored_path
            
        except Exception as e:
            self.logger.error(f"Error creating tailored resume: {e}")
            return self.config.paths.resume_path
            
    def generate_tailored_objective(self, position: str, company: str, message: Message) -> str:
        """Generate tailored objective statement"""
        requirements = message.job_details.get('requirements', []) if message.job_details else []
        
        prompt = f"""Write a professional resume objective for:
        Position: {position}
        Company: {company}
        
        Key requirements: {', '.join(requirements[:3])}
        
        Background: {', '.join(self.config.ai.personality_traits[:3])}
        
        The objective should be 2-3 sentences, highlighting relevant experience and enthusiasm for this specific role."""
        
        return self.ai.generate_answer("Resume Objective", "objective", prompt)
        
    def generate_tailored_response(self, message: Message) -> str:
        """Generate a tailored response to job inquiry"""
        position = message.job_details.get('position', 'the position')
        company = message.sender_company or message.sender_name
        
        prompt = f"""Write a professional response to a recruiter who contacted me about:
        Position: {position}
        Company: {company}
        
        The response should:
        - Thank them for reaching out
        - Express genuine interest in the position
        - Briefly highlight 2-3 relevant qualifications
        - Mention that I'm attaching a tailored resume
        - Suggest next steps
        - Be warm but professional
        - Be 150-200 words
        
        My background: {', '.join(self.config.ai.personality_traits[:4])}"""
        
        return self.ai.generate_answer("Recruiter Response", "message", prompt)
        
    def ask_for_job_description(self, message: Message):
        """Ask for job description when not provided"""
        response = self.config.auto_reply.reply_templates.get(
            'job_inquiry',
            "Thank you for reaching out! I'd be happy to learn more about this opportunity. Could you please share the job description?"
        )
        
        # Personalize response
        response = response.replace('{name}', message.sender_name.split()[0])
        
        self.send_message_response(response)
        
        self.logger.info(f"Asked {message.sender_name} for job description")
        
    def send_general_response(self, message: Message):
        """Send general auto-response"""
        # Determine response type
        if any(keyword in message.message_text.lower() for keyword in ['connect', 'network']):
            response = self.config.auto_reply.reply_templates.get('networking', '')
        else:
            response = self.config.auto_reply.reply_templates.get('default', '')
            
        if response:
            self.send_message_response(response)
            self.logger.info(f"Sent general response to {message.sender_name}")
            
    def send_message_response(self, response_text: str):
        """Send a message response"""
        try:
            # Find message input
            message_input = WebDriverWait(self.browser.driver, 10).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[@contenteditable='true'][@role='textbox']")
                )
            )
            
            # Clear and type message
            message_input.clear()
            message_input.click()
            
            # Type with natural delay
            for char in response_text:
                message_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.1))
                
            # Send message
            send_btn = self.browser.driver.find_element(
                By.XPATH, "//button[@type='submit'][contains(@class,'msg-form__send-button')]"
            )
            self.browser.safe_click(send_btn)
            
            time.sleep(2)
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            
    def attach_resume_instructions(self, resume_path: str):
        """Send instructions for resume since LinkedIn doesn't allow direct attachments