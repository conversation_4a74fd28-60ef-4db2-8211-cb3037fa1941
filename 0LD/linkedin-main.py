#!/usr/bin/env python3
"""
LinkedIn Automation Platform - Main Entry Point
Flexible version that adapts to different module structures
"""

import sys
import os
import time
import json
import argparse
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Flexible imports with multiple fallbacks
config = None
logger = None

# Try to import configuration
try:
    from enhanced_config import EnhancedConfig
    config = EnhancedConfig()
except:
    try:
        from enhanced_config import load_config, save_config
        config = load_config()
    except:
        try:
            import enhanced_config
            if hasattr(enhanced_config, 'config'):
                config = enhanced_config.config
        except:
            pass

# If no config module works, use the JSON file directly
if config is None:
    config_file = 'linkedin_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config = json.load(f)
    else:
        # Create default config
        config = {
            "username": "<EMAIL>",
            "password": "Exit532493(*)",
            "browser": "chrome",
            "resume_path": "/home/<USER>/Documents/Source/lin/linkedin-jobs/GSBR.docx",
            "openai_api_key": "********************************************************************************************************************************************************************",
            "auto_use_defaults": False,
            "delays": {
                "typing": 0.08,
                "between_applications": 30,
                "page_load": 3
            }
        }
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

# Try to import logging
try:
    from enhanced_logging import EnhancedLogger
    logger = EnhancedLogger()
except:
    try:
        from enhanced_logging import get_logger
        logger = get_logger(config)
    except:
        try:
            from logging_enhanced import EnhancedLogger
            logger = EnhancedLogger()
        except:
            # Fallback to basic logging
            import logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            logger = logging.getLogger(__name__)

# Try to import the main automation class
LinkedInAutomation = None
try:
    from complete_enhanced_linkedin import LinkedInJobAutomation as LinkedInAutomation
except:
    try:
        from complete_enhanced_linkedin import LinkedInAutomation
    except:
        try:
            from lin import LinkedInAutomation
        except:
            try:
                from lin import LinkedInJobAutomation as LinkedInAutomation
            except:
                print("Warning: Main automation module not found")

# Feature modules - import with error handling
NetworkMaintainer = None
ContentCreator = None
MessageAutoResponder = None
DashboardServer = None

try:
    from network_maintenance_module import NetworkMaintainer
except Exception as e:
    print(f"Network module not loaded: {e}")

try:
    from article_publishing_module import ContentCreator
except Exception as e:
    print(f"Article module not loaded: {e}")

try:
    from message_autoreply_module import MessageAutoResponder
except Exception as e:
    print(f"Message module not loaded: {e}")

try:
    from web_dashboard_backend import DashboardServer
except Exception as e:
    print(f"Dashboard module not loaded: {e}")


class LinkedInAutomationPlatform:
    """Main platform orchestrator for all LinkedIn automation features"""
    
    def __init__(self):
        """Initialize the platform with available config and logging"""
        self.config = config
        self.logger = logger
        
        # Log startup
        if hasattr(self.logger, 'info'):
            self.logger.info("LinkedIn Automation Platform initialized")
        else:
            print("[INFO] LinkedIn Automation Platform initialized")
    
    def get_config_value(self, key, default=None):
        """Safely get config value regardless of config type"""
        if isinstance(self.config, dict):
            return self.config.get(key, default)
        elif hasattr(self.config, 'get'):
            return self.config.get(key, default)
        elif hasattr(self.config, key):
            return getattr(self.config, key, default)
        return default
    
    def display_menu(self):
        """Display the main menu"""
        print("\n" + "="*50)
        print("LinkedIn Automation Platform - Main Menu")
        print("="*50)
        print("1. Apply to Jobs (Auto-Apply Mode)")
        print("2. Network Maintenance (Birthdays, Congrats, etc.)")
        print("3. Article Publishing (Create & Post Content)")
        print("4. Message Auto-Reply (Check & Respond)")
        print("5. Web Dashboard (Launch Browser Interface)")
        print("6. View Statistics")
        print("7. Configuration Settings")
        print("8. Exit")
        print("="*50)
    
    def apply_to_jobs_handler(self):
        """Handler for job application automation"""
        print("\n[Job Application Mode]")
        
        if LinkedInAutomation:
            try:
                # Try different initialization patterns
                try:
                    # Try with config and logger
                    automation = LinkedInAutomation(self.config, self.logger)
                except:
                    try:
                        # Try with just config
                        automation = LinkedInAutomation(self.config)
                    except:
                        # Try with no arguments
                        automation = LinkedInAutomation()
                
                # Try different run methods
                if hasattr(automation, 'run'):
                    automation.run()
                elif hasattr(automation, 'start'):
                    automation.start()
                elif hasattr(automation, 'apply_to_jobs'):
                    automation.apply_to_jobs()
                else:
                    print("Automation class found but no run method available")
                    
            except Exception as e:
                print(f"Error during job applications: {e}")
                print("\nTrying alternative approach...")
                
                # Try to run the script directly
                import subprocess
                scripts = ['lin.py', 'complete_enhanced_linkedin.py', 'lin_original_v1.py']
                for script in scripts:
                    if os.path.exists(script):
                        print(f"Running {script}...")
                        subprocess.run([sys.executable, script])
                        break
        else:
            print("Job application module not found.")
            print("Available Python files:")
            py_files = [f for f in os.listdir('.') if f.endswith('.py')]
            for f in py_files:
                print(f"  - {f}")
    
    def network_maintenance_handler(self):
        """Handler for network maintenance features"""
        print("\n[Network Maintenance Mode]")
        
        if NetworkMaintainer:
            try:
                # Create maintainer instance with available arguments
                maintainer = NetworkMaintainer(self.config, self.logger)
                
                print("Starting network maintenance...")
                print("- Checking for birthdays")
                print("- Looking for job changes to congratulate")
                print("- Finding work anniversaries")
                
                if hasattr(maintainer, 'maintain_network'):
                    maintainer.maintain_network()
                elif hasattr(maintainer, 'run'):
                    maintainer.run()
                else:
                    print("Network maintainer found but no run method available")
                    
            except Exception as e:
                print(f"Error during network maintenance: {e}")
        else:
            print("Network maintenance module not available.")
            if os.path.exists('network_maintenance_module.py'):
                print("File exists but couldn't import. Check for syntax errors.")
    
    def article_publishing_handler(self):
        """Handler for content creation and publishing"""
        print("\n[Article Publishing Mode]")
        
        if ContentCreator:
            try:
                creator = ContentCreator(self.config, self.logger)
                
                print("Content creation options:")
                print("1. Create article from trending topics")
                print("2. Create industry insight post")
                print("3. Create poll")
                print("4. Auto-create best option")
                
                choice = input("Select option (1-4): ").strip()
                
                if hasattr(creator, 'create_and_publish_content'):
                    creator.create_and_publish_content()
                elif hasattr(creator, 'run'):
                    creator.run()
                else:
                    print("Content creator found but no run method available")
                    
            except Exception as e:
                print(f"Error during content creation: {e}")
        else:
            print("Article publishing module not available.")
            if os.path.exists('article_publishing_module.py'):
                print("File exists but couldn't import. Check for syntax errors.")
    
    def message_auto_reply_handler(self):
        """Handler for message checking and auto-reply"""
        print("\n[Message Auto-Reply Mode]")
        
        if MessageAutoResponder:
            try:
                responder = MessageAutoResponder(self.config, self.logger)
                
                print("Checking for unread messages...")
                
                if hasattr(responder, 'check_and_respond_to_messages'):
                    responder.check_and_respond_to_messages()
                elif hasattr(responder, 'run'):
                    responder.run()
                else:
                    print("Message responder found but no run method available")
                    
            except Exception as e:
                print(f"Error during message handling: {e}")
        else:
            print("Message auto-reply module not available.")
            if os.path.exists('message_autoreply_module.py'):
                print("File exists but couldn't import. Check for syntax errors.")
    
    def web_dashboard_handler(self):
        """Handler for launching web dashboard"""
        print("\n[Web Dashboard Mode]")
        
        if DashboardServer:
            try:
                server = DashboardServer(self.config, self.logger)
                
                print("Starting web dashboard...")
                print("Dashboard will be available at: http://localhost:8000/dashboard")
                print("Press Ctrl+C to stop the server")
                
                if hasattr(server, 'run'):
                    server.run()
                elif hasattr(server, 'start'):
                    server.start()
                else:
                    print("Dashboard server found but no run method available")
                    
            except Exception as e:
                print(f"Error launching dashboard: {e}")
        else:
            print("Web dashboard module not available.")
            
            # Try to run the backend script directly
            if os.path.exists('web_dashboard_backend.py'):
                print("Trying to run web_dashboard_backend.py directly...")
                import subprocess
                subprocess.run([sys.executable, 'web_dashboard_backend.py'])
            elif os.path.exists('web_dashboard_module.html'):
                print("Found HTML dashboard. Opening in browser...")
                import webbrowser
                webbrowser.open(f'file://{os.path.abspath("web_dashboard_module.html")}')
    
    def view_statistics_handler(self):
        """Handler for viewing automation statistics"""
        print("\n[Statistics]")
        
        # Check various statistics files
        stats_files = [
            'overall_stats.json',
            'dashboard_data.json',
            'applications_log.json',
            'linkedin_applications.json'
        ]
        
        for stats_file in stats_files:
            if os.path.exists(stats_file):
                try:
                    with open(stats_file, 'r') as f:
                        stats = json.load(f)
                    
                    print(f"\nStats from {stats_file}:")
                    if isinstance(stats, dict):
                        for key, value in stats.items():
                            print(f"  - {key}: {value}")
                    elif isinstance(stats, list):
                        print(f"  - Total entries: {len(stats)}")
                        if stats:
                            print(f"  - Latest entry: {stats[-1]}")
                except Exception as e:
                    print(f"Error reading {stats_file}: {e}")
        
        # Check log directories
        if os.path.exists('linkedin_logs'):
            log_dirs = [d for d in os.listdir('linkedin_logs') if os.path.isdir(f'linkedin_logs/{d}')]
            print(f"\nLog runs found: {len(log_dirs)}")
            if log_dirs:
                print(f"Latest run: {sorted(log_dirs)[-1]}")
    
    def configuration_settings_handler(self):
        """Handler for viewing/editing configuration"""
        print("\n[Configuration Settings]")
        
        print("1. View current configuration")
        print("2. Edit configuration file")
        print("3. Test configuration")
        
        choice = input("Select option (1-3): ").strip()
        
        if choice == '1':
            # Display current config
            print("\nCurrent Configuration:")
            if isinstance(self.config, dict):
                print(json.dumps(self.config, indent=2))
            else:
                # Try to convert object to dict
                try:
                    config_dict = vars(self.config)
                    print(json.dumps(config_dict, indent=2))
                except:
                    print(f"Configuration loaded: {type(self.config)}")
                    print(f"Username: {self.get_config_value('username', 'Not set')}")
                    print(f"Browser: {self.get_config_value('browser', 'Not set')}")
                    print(f"Resume path: {self.get_config_value('resume_path', 'Not set')}")
                
        elif choice == '2':
            # Try to open config file
            config_file = 'linkedin_config.json'
            if os.path.exists(config_file):
                print(f"\nOpening {config_file}...")
                try:
                    # Try different editors
                    import subprocess
                    for editor in ['nano', 'vim', 'notepad', 'gedit']:
                        try:
                            subprocess.call([editor, config_file])
                            break
                        except:
                            continue
                except:
                    print(f"Please edit {config_file} manually")
            else:
                print("Configuration file not found")
                
        elif choice == '3':
            # Test configuration
            print("\nTesting configuration...")
            print(f"✓ Username: {self.get_config_value('username', 'Not set')}")
            print(f"✓ Password: {'*' * len(str(self.get_config_value('password', '')))}")
            
            resume_path = self.get_config_value('resume_path', '')
            if resume_path and os.path.exists(resume_path):
                print(f"✓ Resume found: {resume_path}")
            else:
                print(f"✗ Resume not found: {resume_path}")
            
            api_key = self.get_config_value('openai_api_key', '')
            if api_key:
                print(f"✓ OpenAI API key: {api_key[:20]}...")
            else:
                print("✗ OpenAI API key not set")
    
    def run(self):
        """Main execution loop"""
        username = self.get_config_value('username', 'User')
        print(f"\nWelcome to LinkedIn Automation Platform!")
        print(f"Logged in as: {username}")
        
        while True:
            try:
                self.display_menu()
                choice = input("\nSelect option (1-8): ").strip()
                
                if choice == '1':
                    self.apply_to_jobs_handler()
                elif choice == '2':
                    self.network_maintenance_handler()
                elif choice == '3':
                    self.article_publishing_handler()
                elif choice == '4':
                    self.message_auto_reply_handler()
                elif choice == '5':
                    self.web_dashboard_handler()
                elif choice == '6':
                    self.view_statistics_handler()
                elif choice == '7':
                    self.configuration_settings_handler()
                elif choice == '8':
                    print("\nExiting LinkedIn Automation Platform...")
                    print("Have a productive day!")
                    break
                else:
                    print("Invalid option. Please try again.")
                    
                # Pause before returning to menu
                if choice in ['1', '2', '3', '4', '6', '7']:
                    input("\nPress Enter to return to menu...")
                    
            except KeyboardInterrupt:
                print("\n\nInterrupted by user")
                continue
            except Exception as e:
                print(f"\nAn error occurred: {e}")
                input("Press Enter to continue...")


def main():
    """Main entry point with argument parsing"""
    parser = argparse.ArgumentParser(description='LinkedIn Automation Platform')
    parser.add_argument('--apply', action='store_true', 
                       help='Run job application mode directly')
    parser.add_argument('--network', action='store_true',
                       help='Run network maintenance directly')
    parser.add_argument('--publish', action='store_true',
                       help='Run article publishing directly')
    parser.add_argument('--messages', action='store_true',
                       help='Run message auto-reply directly')
    parser.add_argument('--dashboard', action='store_true',
                       help='Launch web dashboard directly')
    
    args = parser.parse_args()
    
    # Create platform instance
    platform = LinkedInAutomationPlatform()
    
    # Handle direct mode arguments
    if args.apply:
        platform.apply_to_jobs_handler()
    elif args.network:
        platform.network_maintenance_handler()
    elif args.publish:
        platform.article_publishing_handler()
    elif args.messages:
        platform.message_auto_reply_handler()
    elif args.dashboard:
        platform.web_dashboard_handler()
    else:
        # Run interactive menu mode
        platform.run()


if __name__ == "__main__":
    main()