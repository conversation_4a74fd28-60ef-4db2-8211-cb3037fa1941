{"username": "<EMAIL>", "password": "Exit532493(*)", "browser": {"headless": false, "window_size": [1920, 1080], "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "timeout": 30, "implicit_wait": 10, "page_load_timeout": 30}, "resume_path": "/home/<USER>/Documents/Source/lin/linkedin-jobs/GSBR.docx", "openai_api_key": "********************************************************************************************************************************************************************", "auto_use_defaults": false, "delays": {"page_load": 3, "click_delay": 2, "typing_delay": 1, "between_applications": 5, "login_wait": 5}, "search": {"keywords": ["Software Engineer", "Full Stack Developer", ".NET Developer", "Azure", "React", "<PERSON><PERSON>", "Backend Engineer", "Technical Product Owner", "Python Developer", "Remote", "C# Developer", "Angular Developer", "DevOps Engineer", "Cloud Engineer", "Microservices", "API Developer"], "locations": ["Remote", "Cleveland, OH", "Columbus, OH", "Cincinnati, OH", "United States", "Ohio", "Akron, OH", "Toledo, OH"], "experience_levels": ["Entry level", "Associate", "Mid-Senior", "Director"], "job_types": ["Full-time", "Contract", "Remote", "Hybrid"], "date_posted": "Past week", "salary_min": 80000, "exclude_companies": []}, "network": {"connection_limit_per_day": 40, "message_templates": {"birthday": "Hi {name}, happy birthday! Hope you have a wonderful year ahead filled with success and happiness.", "work_anniversary": "Congratulations on your work anniversary, {name}! Here's to continued success in your career.", "new_position": "Congratulations on your new position, {name}! Wishing you all the best in this exciting new chapter.", "general_connection": "Hi {name}, I'd love to connect and expand my professional network. Looking forward to staying in touch!"}, "engagement_limits": {"likes_per_day": 60, "comments_per_day": 10, "connection_requests_per_day": 40, "messages_per_day": 20}, "auto_congratulate": true, "auto_birthday_wishes": true}, "ai": {"model": "gpt-4", "cover_letter_tone": "Professional, enthusiastic, detail-oriented, results-driven", "resume_style": "Full Stack / .NE<PERSON> Developer, team player, innovative problem solver", "personality_traits": ["Detail-oriented", "Team collaborative", "Results-driven", "Innovative", "Reliable", "Continuous learner"], "max_tokens": 1000, "temperature": 0.7}, "job_preferences": {"salary_range": [80000, 200000], "preferred_industries": ["Software Development", "Cloud Computing", "Consulting", "Financial Services", "Healthcare Technology", "E-commerce"], "preferred_companies": ["Microsoft", "Google", "Amazon", "<PERSON><PERSON>", "KAG", "Progressive", "KeyBank", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "avoid_companies": [], "required_benefits": ["Health Insurance", "401k", "Remote Work"], "preferred_technologies": [".NET", "C#", "Azure", "React", "Vue.js", "Python", "SQL Server", "<PERSON>er", "Kubernetes"]}, "auto_reply": {"enabled": true, "job_inquiry_template": "Hi {recruiter}, thank you for reaching out about the {position} opportunity. I'm very interested! Could you please provide the job description and key requirements so I can tailor my resume and cover letter for your review? I'm particularly interested in learning about the technical stack and team structure. Looking forward to hearing from you!", "application_followup_template": "Dear {contact}, I wanted to follow up regarding my recent application for the {role} position at {company}. I'm very excited about this opportunity and believe my experience with {technologies} would be a great fit. Please let me know if you need any additional information from my side. Thank you for your consideration!", "general_response_template": "Thank you for your message! I'm currently open to new opportunities in software development, particularly in .NET/C# and full-stack roles. Please feel free to share any relevant positions you think might be a good fit.", "check_frequency_minutes": 30, "max_responses_per_day": 15}, "content_creation": {"enabled": true, "post_frequency_days": 3, "topics": ["Software Development", ".NET Development", "Cloud Computing", "Career Growth", "Technology Trends", "Remote Work"], "hashtags": ["#SoftwareDeveloper", "#DotNet", "#Azure", "#TechCareer", "#RemoteWork", "#CloudComputing", "#FullStackDeveloper"], "content_types": ["Article", "Post", "Poll"], "max_posts_per_week": 3}, "logging": {"enabled": true, "log_level": "INFO", "log_directory": "linkedin_logs", "max_log_files": 30, "log_applications": true, "log_network_activity": true, "log_messages": true, "log_errors": true}, "files": {"screenshots_directory": "screenshots", "exports_directory": "exports", "backup_directory": "backups", "applications_log": "applications_log.json", "network_log": "network_activity.json", "message_log": "messages_log.json"}, "dashboard": {"enabled": true, "host": "localhost", "port": 8000, "auto_refresh_seconds": 30, "show_sensitive_data": false}, "security": {"encrypt_sensitive_data": false, "session_timeout_minutes": 120, "max_login_attempts": 3, "logout_on_close": true}, "advanced": {"rate_limit_seconds": 60, "analytics_enabled": true, "scheduling_enabled": false, "backup_frequency_days": 7, "cleanup_old_logs_days": 30, "max_concurrent_operations": 1, "error_retry_attempts": 3, "debug_mode": false}}