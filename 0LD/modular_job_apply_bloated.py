#!/usr/bin/env python3
"""
Enhanced Modular Job Application Module
Combines best practices from both versions with improved selectors and error handling
"""
import os
import time
import random
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    ElementClickInterceptedException,
    StaleElementReferenceException,
    TimeoutException,
    NoSuchElementException,
)
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options


def click_element_safely(driver, element, timeout=10):
    """
    Enhanced click handler that tries multiple strategies to click an element.
    Handles overlapping elements and other common click interception issues.
    """
    # Scroll element into view
    driver.execute_script(
        "arguments[0].scrollIntoView({block: 'center', inline: 'center'});", element
    )
    time.sleep(0.5)

    # Wait for element to be clickable
    try:
        WebDriverWait(driver, timeout).until(EC.element_to_be_clickable(element))
    except:
        pass

    # Try different click methods
    click_methods = [
        lambda: element.click(),
        lambda: driver.execute_script("arguments[0].click();", element),
        lambda: ActionChains(driver).move_to_element(element).click().perform(),
    ]

    for method in click_methods:
        try:
            method()
            return True
        except ElementClickInterceptedException:
            continue
        except Exception:
            continue

    # If still blocked, try hiding common overlapping elements
    try:
        overlapping_selectors = [
            ".artdeco-entity-lockup",
            ".msg-overlay-list-bubble",
            "[data-test-modal]",
            ".artdeco-modal",
        ]
        for selector in overlapping_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for el in elements:
                    driver.execute_script("arguments[0].style.display='none';", el)
            except:
                pass
        time.sleep(0.2)
    except:
        pass

    # Final attempt with JavaScript
    try:
        driver.execute_script("arguments[0].click();", element)
        return True
    except:
        return False


class ModularJobApplicator:
    """Enhanced job application handler with improved selectors and error handling."""

    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
        self.driver = None
        self.wait = None
        self.applied_count = 0
        self.error_count = 0
        self.skipped_count = 0
        self.demo_mode = True
        self.warning_detected = False
        self.consecutive_errors = 0
        self.last_application_time = None

    def log_info(self, msg):
        """Log info message."""
        if self.logger and hasattr(self.logger, "info"):
            self.logger.info(msg)
        else:
            print(f"[INFO] {msg}")

    def log_error(self, msg):
        """Log error message."""
        if self.logger and hasattr(self.logger, "error"):
            self.logger.error(msg)
        else:
            print(f"[ERROR] {msg}")

    def get(self, key, default=None):
        """Get config value safely."""
        return self.config.get(key, default)

    def start_browser(self):
        """Initialize browser with proper configuration."""
        opts = Options()

        # Find Chrome binary
        candidates = [
            self.get("chrome_binary_location"),
            os.environ.get("CHROME_BINARY"),
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/usr/bin/chromium",
        ]

        for path in candidates:
            if path and os.path.exists(path):
                opts.binary_location = path
                self.log_info(f"Using Chrome binary: {path}")
                break
        else:
            raise RuntimeError("Chrome/Chromium binary not found.")

        # Set options
        if self.get("headless", False):
            opts.add_argument("--headless=new")
        opts.add_argument("--disable-gpu")
        opts.add_argument("--no-sandbox")
        opts.add_argument("--disable-dev-shm-usage")
        opts.add_argument("--window-size=1920,1080")

        # Remove stale chromedriver
        try:
            os.remove("/usr/bin/chromedriver")
        except:
            pass

        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=opts)
        self.driver.maximize_window()
        self.wait = WebDriverWait(self.driver, 15)

        self.log_info("Browser started successfully")
        return self.driver

    def set_mode(self, demo=True):
        """Set application mode (demo or live)."""
        self.demo_mode = demo
        mode = "DEMO" if demo else "LIVE"
        self.log_info(f"Running in {mode} mode")

        if not demo:
            print("\n" + "=" * 60)
            print("⚠️  WARNING: LIVE MODE - REAL APPLICATIONS WILL BE SUBMITTED")
            print("=" * 60)
            ans = input("Are you SURE you want to submit real applications? (yes/no): ")
            if ans.lower() != "yes":
                self.demo_mode = True
                self.log_info("Switched back to DEMO mode for safety")

    def login(self):
        """Login to LinkedIn."""
        username = self.get("username")
        password = self.get("password")

        if not username or not password:
            self.log_error("Missing LinkedIn credentials.")
            return False

        self.log_info("Logging into LinkedIn...")
        self.driver.get("https://www.linkedin.com/login")

        try:
            # Wait for login form
            self.wait.until(EC.presence_of_element_located((By.ID, "username")))

            # Enter credentials
            username_field = self.driver.find_element(By.ID, "username")
            username_field.send_keys(username)

            password_field = self.driver.find_element(By.ID, "password")
            password_field.send_keys(password)
            password_field.send_keys(Keys.RETURN)

            # Wait for successful login
            self.wait.until(
                lambda driver: any(
                    x in driver.current_url for x in ["/feed", "/mynetwork", "/jobs"]
                )
            )

            self.log_info("Successfully logged in")
            time.sleep(3)
            self.dismiss_popups()
            return True

        except TimeoutException:
            self.log_error("Login timeout - check credentials")
            return False
        except Exception as e:
            self.log_error(f"Login failed: {str(e)}")
            return False

    def dismiss_popups(self):
        """Dismiss any popups or overlays."""
        popup_selectors = [
            "[data-test-modal-close-btn]",
            ".msg-overlay-bubble-header__control--close",
            "button[aria-label*='Dismiss']",
            ".artdeco-modal__dismiss",
            "button[data-control-name='overlay.close_overlay']",
        ]

        for selector in popup_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        click_element_safely(self.driver, element)
                        time.sleep(0.5)
                        self.log_info("Dismissed popup")
            except:
                continue

    def search_jobs(self):
        """Search for jobs based on configuration."""
        search_config = self.get("search", {})
        keywords = search_config.get("keywords", ["Software Engineer"])

        self.log_info(f"Searching for jobs with keywords: {keywords}")

        for keyword in keywords[:3]:  # Limit for demo
            self._search_keyword(keyword)

    def _search_keyword(self, keyword):
        """Search for jobs with specific keyword."""
        try:
            # Navigate to job search
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keyword}&location=United%20States&f_AL=true"
            self.driver.get(search_url)
            time.sleep(5)

            self.dismiss_popups()

            # Wait for job results
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-job-id]"))
            )

            # Get all job cards
            job_cards = self.driver.find_elements(By.CSS_SELECTOR, "div[data-job-id]")
            self.log_info(f"Found {len(job_cards)} job cards for '{keyword}'")

            # Process each job
            for idx, card in enumerate(job_cards[:10], 1):  # Process up to 10 jobs
                self._process_job(card, idx)

        except Exception as e:
            self.log_error(f"Search failed for '{keyword}': {str(e)}")

    def _process_job(self, card, idx):
        """Process a single job card with safety checks."""
        try:
            # Check for warnings before proceeding
            if self._check_for_warnings():
                self.log_error("Warning detected! Stopping automation for safety.")
                return

            # Add human-like delay before clicking
            self._safe_delay("between_jobs")

            # Simulate human behavior occasionally
            if random.random() < 0.3:  # 30% chance
                self._simulate_human_behavior()

            # Check consecutive errors
            if self.consecutive_errors >= 3:
                self.log_error("Too many consecutive errors. Taking a break...")
                time.sleep(random.uniform(60, 120))  # 1-2 minute break
                self.consecutive_errors = 0

            # Click on job card
            if not click_element_safely(self.driver, card):
                self.log_error(f"Could not click job card {idx}")
                self.consecutive_errors += 1
                return

            # Wait for job details with random delay
            self._safe_delay("page_scan")

            # Extract job information
            job_info = self._extract_job_info()
            if not job_info:
                self.log_error(f"Could not extract job info for card {idx}")
                self.consecutive_errors += 1
                return

            self.consecutive_errors = 0  # Reset on success
            self.log_info(f"Job {idx}: {job_info['title']} at {job_info['company']}")

            # Find Easy Apply button
            apply_button = self._find_easy_apply_button()

            if not apply_button:
                self.log_info(f"No Easy Apply button found for {job_info['title']}")
                self.skipped_count += 1
                return

            # Rate limiting check
            if self._should_rate_limit():
                self.log_info("Rate limit reached. Taking a break...")
                break_time = random.uniform(300, 600)  # 5-10 minute break
                time.sleep(break_time)

            # Handle application
            if self.demo_mode:
                self.log_info(
                    f"[DEMO] Would apply to {job_info['title']} at {job_info['company']}"
                )
                self.applied_count += 1
            else:
                if self._handle_easy_apply(apply_button, job_info):
                    self.applied_count += 1
                    self.last_application_time = time.time()
                    # Longer delay after successful application
                    self._safe_delay("after_submit")
                else:
                    self.error_count += 1

        except Exception as e:
            self.log_error(f"Error processing job {idx}: {str(e)}")
            self.error_count += 1
            self.consecutive_errors += 1

    def _should_rate_limit(self):
        """Check if we should slow down based on application rate."""
        # Limit applications per hour
        max_per_hour = 10  # Conservative limit

        if self.last_application_time:
            time_since_last = time.time() - self.last_application_time
            if time_since_last < (3600 / max_per_hour):  # Less than 6 minutes
                return True

        # Check total applications in session
        if self.applied_count >= 25:
            self.log_info("Reached 25 applications. Consider taking a break.")
            return True

        return False

    def _extract_job_info(self):
        """Extract job title and company from the details pane."""
        try:
            # Multiple selectors for job title
            title_selectors = [
                "h1.job-details-jobs-unified-top-card__job-title",
                "h2.job-details-jobs-unified-top-card__job-title",
                "h1.jobs-unified-top-card__job-title",
                ".job-details-jobs-unified-top-card__job-title",
            ]

            title = None
            for selector in title_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    title = element.text.strip()
                    if title:
                        break
                except:
                    continue

            # Multiple selectors for company
            company_selectors = [
                "a.job-details-jobs-unified-top-card__company-name",
                "span.job-details-jobs-unified-top-card__company-name",
                "div.job-details-jobs-unified-top-card__company-name",
                ".job-details-jobs-unified-top-card__primary-description a",
            ]

            company = None
            for selector in company_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    company = element.text.strip()
                    if company:
                        break
                except:
                    continue

            if title and company:
                return {"title": title, "company": company}

            return None

        except Exception as e:
            self.log_error(f"Error extracting job info: {str(e)}")
            return None

        """Fill all form fields in current step."""
        try:
            # Fill text inputs
            self._fill_text_inputs()

            # Handle dropdowns
            self._handle_dropdowns()

            # Handle radio buttons
            self._handle_radio_buttons()

            # Handle checkboxes
            self._handle_checkboxes()

            # Handle file uploads
            self._handle_file_upload()

        except Exception as e:
            self.log_error(f"Error filling form: {str(e)}")

    def _fill_text_inputs(self):
        """Fill text input fields with human-like typing."""
        inputs = self.driver.find_elements(
            By.CSS_SELECTOR,
            "input[type='text'], input[type='email'], input[type='tel'], input[type='number']",
        )

        user_profile = self.get("user_profile", {})

        for input_field in inputs:
            try:
                # Skip if already filled
                if input_field.get_attribute("value"):
                    continue

                # Get field context
                field_id = (input_field.get_attribute("id") or "").lower()
                field_name = (input_field.get_attribute("name") or "").lower()
                placeholder = (input_field.get_attribute("placeholder") or "").lower()
                aria_label = (input_field.get_attribute("aria-label") or "").lower()

                field_context = f"{field_id} {field_name} {placeholder} {aria_label}"

                # Determine what to fill
                value_to_type = None

                if any(x in field_context for x in ["phone", "mobile", "cell"]):
                    value_to_type = user_profile.get("phone", "************")
                elif "email" in field_context:
                    value_to_type = self.get("username", "<EMAIL>")
                elif any(x in field_context for x in ["years", "experience"]):
                    value_to_type = "5"
                elif "salary" in field_context:
                    value_to_type = "100000"

                if value_to_type:
                    input_field.clear()
                    # Type with human-like delays
                    for char in value_to_type:
                        input_field.send_keys(char)
                        time.sleep(random.uniform(0.08, 0.15))

                    # Small pause after field
                    time.sleep(random.uniform(0.5, 1.0))

            except Exception as e:
                continue

    def _handle_dropdowns(self):
        """Handle dropdown selections."""
        selects = self.driver.find_elements(By.TAG_NAME, "select")

        for select_element in selects:
            try:
                select = Select(select_element)

                # Skip if already selected
                if (
                    select.first_selected_option.text
                    and select.first_selected_option.text != "Select an option"
                ):
                    continue

                # Get context
                select_id = (select_element.get_attribute("id") or "").lower()

                # Try to find label
                label_text = ""
                try:
                    label = self.driver.find_element(
                        By.CSS_SELECTOR,
                        f"label[for='{select_element.get_attribute('id')}']",
                    )
                    label_text = label.text.lower()
                except:
                    pass

                context = f"{select_id} {label_text}"

                # Make selection based on context
                if "country" in context:
                    self._select_by_text(select, ["United States", "USA", "US"])
                elif "years" in context or "experience" in context:
                    self._select_by_text(select, ["5-10", "5+", "5"])
                elif "notice" in context:
                    self._select_by_text(select, ["2 weeks", "Immediately"])
                else:
                    # Select first non-empty option
                    if len(select.options) > 1:
                        select.select_by_index(1)

                time.sleep(0.2)

            except Exception as e:
                continue

    def _select_by_text(self, select, text_options):
        """Select option containing any of the given texts."""
        for option in select.options:
            for text in text_options:
                if text.lower() in option.text.lower():
                    select.select_by_visible_text(option.text)
                    return

    def _handle_radio_buttons(self):
        """Handle radio button selections."""
        # Find all radio buttons
        radios = self.driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")

        # Group by name
        radio_groups = {}
        for radio in radios:
            name = radio.get_attribute("name")
            if name:
                if name not in radio_groups:
                    radio_groups[name] = []
                radio_groups[name].append(radio)

        # Process each group
        for name, group_radios in radio_groups.items():
            try:
                # Get question context
                question_text = ""
                try:
                    fieldset = group_radios[0].find_element(
                        By.XPATH, "./ancestor::fieldset"
                    )
                    question_text = fieldset.text.lower()
                except:
                    try:
                        parent = group_radios[0].find_element(By.XPATH, "./../..")
                        question_text = parent.text.lower()
                    except:
                        pass

                # Answer based on question context
                if any(
                    x in question_text
                    for x in [
                        "authorized",
                        "legally",
                        "eligible",
                        "work in",
                        "drug test",
                    ]
                ):
                    # Select "Yes"
                    for radio in group_radios:
                        label = self._get_radio_label(radio).lower()
                        if "yes" in label and not radio.is_selected():
                            click_element_safely(self.driver, radio)
                            break
                elif any(x in question_text for x in ["sponsor", "visa"]):
                    # Select "No"
                    for radio in group_radios:
                        label = self._get_radio_label(radio).lower()
                        if "no" in label and not radio.is_selected():
                            click_element_safely(self.driver, radio)
                            break

                time.sleep(0.2)

            except Exception as e:
                continue

    def _get_radio_label(self, radio):
        """Get label text for a radio button."""
        # Try various methods to get label
        methods = [
            lambda: self.driver.find_element(
                By.CSS_SELECTOR, f"label[for='{radio.get_attribute('id')}']"
            ).text,
            lambda: radio.find_element(By.XPATH, "./parent::label").text,
            lambda: radio.find_element(By.XPATH, "./following-sibling::*").text,
            lambda: radio.find_element(
                By.XPATH, "./../text()[normalize-space()]"
            ).strip(),
        ]

        for method in methods:
            try:
                return method()
            except:
                continue

        return ""

    def _handle_checkboxes(self):
        """Handle checkbox selections."""
        checkboxes = self.driver.find_elements(
            By.CSS_SELECTOR, "input[type='checkbox']"
        )

        for checkbox in checkboxes:
            try:
                # Get checkbox context
                checkbox_id = checkbox.get_attribute("id") or ""
                label_text = ""

                # Try to get label
                try:
                    label = self.driver.find_element(
                        By.CSS_SELECTOR, f"label[for='{checkbox_id}']"
                    )
                    label_text = label.text.lower()
                except:
                    try:
                        parent = checkbox.find_element(By.XPATH, "./..")
                        label_text = parent.text.lower()
                    except:
                        pass

                # Check if it's an agreement/acknowledgment
                if any(
                    x in label_text
                    for x in ["agree", "acknowledge", "confirm", "accept", "understand"]
                ):
                    if not checkbox.is_selected():
                        click_element_safely(self.driver, checkbox)
                        time.sleep(0.2)

            except Exception as e:
                continue

    def _handle_file_upload(self):
        """Handle resume upload."""
        resume_path = self.get("resume_path")
        if not resume_path or not os.path.exists(resume_path):
            self.log_error(f"Resume not found at: {resume_path}")
            return

        file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")

        for file_input in file_inputs:
            try:
                # Check if already has file
                if file_input.get_attribute("value"):
                    continue

                # Check if it's for resume
                input_id = (file_input.get_attribute("id") or "").lower()

                if any(x in input_id for x in ["resume", "cv", "document"]):
                    file_input.send_keys(resume_path)
                    self.log_info("Resume uploaded")
                    time.sleep(1)
                    break

            except Exception as e:
                continue

    def _click_next_or_submit(self):
        """Click next or submit button."""
        # Button configurations in order of preference
        button_configs = [
            ("Submit application", True),
            ("Submit", True),
            ("Review", False),
            ("Next", False),
            ("Continue", False),
        ]

        for text, is_final in button_configs:
            # Try multiple selectors
            selectors = [
                f"button[aria-label*='{text}']",
                f"button:contains('{text}')",
                "button[data-easy-apply-next-button]",
                "button[data-easy-apply-submit-button]",
            ]

            for selector in selectors:
                try:
                    if ":contains" in selector:
                        # Use XPath for text content
                        xpath = f"//button[contains(., '{text}')]"
                        buttons = self.driver.find_elements(By.XPATH, xpath)
                    else:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.lower()
                            aria_label = (
                                button.get_attribute("aria-label") or ""
                            ).lower()

                            if (
                                text.lower() in button_text
                                or text.lower() in aria_label
                            ):
                                if click_element_safely(self.driver, button):
                                    self.log_info(f"Clicked '{text}' button")
                                    return True

                except Exception as e:
                    continue

        return False

    def _is_application_complete(self):
        """Check if application was submitted successfully."""
        success_indicators = [
            "application sent",
            "application submitted",
            "thank you for applying",
            "your application was sent",
            "you've successfully applied",
        ]

        try:
            # Check page text
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()

            for indicator in success_indicators:
                if indicator in page_text:
                    # Look for and click the Done button
                    self._click_done_button()
                    return True

            # Check for success modals
            success_selectors = [
                "[aria-label*='success']",
                "[aria-label*='applied']",
                ".artdeco-modal__header h2",
            ]

            for selector in success_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.lower()
                            if any(ind in text for ind in success_indicators):
                                # Look for and click the Done button
                                self._click_done_button()
                                return True
                except:
                    continue

        except Exception as e:
            self.log_error(f"Error checking completion: {str(e)}")

        return False

    def _click_done_button(self):
        """Click the Done button in the success modal."""
        done_selectors = [
            "button[aria-label='Done']",
            "button[aria-label='Dismiss']",
            "button:contains('Done')",
            ".artdeco-modal__confirm-dialog-btn",
            "button[data-control-name='dismiss_application_confirmation']",
        ]

        for selector in done_selectors:
            try:
                if ":contains" in selector:
                    # Use XPath for text content
                    buttons = self.driver.find_elements(
                        By.XPATH, "//button[contains(., 'Done')]"
                    )
                else:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.strip()
                        if (
                            button_text == "Done"
                            or "done"
                            in (button.get_attribute("aria-label") or "").lower()
                        ):
                            click_element_safely(self.driver, button)
                            self.log_info(
                                "Clicked Done button to dismiss success modal"
                            )
                            time.sleep(1)
                            return
            except:
                continue

        # If no Done button found, try ESC key
        try:
            self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
            self.log_info("Pressed ESC to dismiss modal")
        except:
            pass

    def _check_for_warnings(self):
        """Check for LinkedIn warnings or restrictions"""
        warning_texts = [
            "unusual activity",
            "account restricted",
            "temporarily limited",
            "please verify",
            "suspicious activity",
            "we've noticed",
            "security check",
            "verify your account",
        ]

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()

            for warning in warning_texts:
                if warning in page_text:
                    self.warning_detected = True
                    self.log_error(f"Warning detected: {warning}")
                    return True

        except Exception as e:
            self.log_error(f"Error checking for warnings: {e}")

        return False

    def _safe_delay(self, delay_type):
        """Add human-like delays"""
        delays = {
            "between_jobs": (8, 15),
            "page_scan": (3, 6),
            "form_fill": (1, 3),
            "after_submit": (10, 20),
        }

        min_delay, max_delay = delays.get(delay_type, (2, 4))
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def _simulate_human_behavior(self):
        """Simulate human-like behavior"""
        behaviors = [
            lambda: self.driver.execute_script(
                "window.scrollBy(0, Math.floor(Math.random() * 500));"
            ),
            lambda: time.sleep(random.uniform(2, 5)),
            lambda: self.driver.refresh() if random.random() < 0.1 else None,
        ]

        behavior = random.choice(behaviors)
        try:
            behavior()
        except:
            pass

    def run(self):
        """Main execution flow with safety measures."""
        try:
            # Mode selection
            print("\n" + "=" * 50)
            print("LinkedIn Job Application Bot")
            print("=" * 50)
            print("Select mode:")
            print("1. Demo Mode (safe - no real applications)")
            print("2. Live Mode (will submit real applications)")
            print("=" * 50)

            mode = input("Enter your choice (1 or 2): ").strip()
            self.set_mode(demo=(mode != "2"))

            # Safety settings
            print("\n⚠️  Safety Settings:")
            print("- Max applications per hour: 10")
            print("- Random delays: 8-15 seconds between jobs")
            print("- Human-like typing speed")
            print("- Automatic break after 25 applications")
            print("- Warning detection enabled")
            print("- Will stop if LinkedIn shows any warnings")

            input("\nPress Enter to continue with these safety settings...")

            # Start browser
            self.start_browser()

            # Login
            if not self.login():
                self.log_error("Login failed, exiting...")
                return

            # Initial delay after login
            print("\nWaiting for page to fully load...")
            time.sleep(random.uniform(5, 8))

            # Check for any immediate warnings
            if self._check_for_warnings():
                self.log_error("Warning detected immediately after login. Stopping.")
                return

            # Search and apply
            self.search_jobs()

            # Summary
            print("\n" + "=" * 50)
            print("Session Summary")
            print("=" * 50)
            print(f"✅ Applications submitted: {self.applied_count}")
            print(f"⏭️  Jobs skipped (no Easy Apply): {self.skipped_count}")
            print(f"❌ Errors encountered: {self.error_count}")
            if self.warning_detected:
                print("⚠️  WARNING: LinkedIn warning was detected during session!")
            print("=" * 50)

            if self.warning_detected:
                print("\n🚨 IMPORTANT: LinkedIn detected unusual activity!")
                print("   Wait at least 24 hours before running again.")
                print("   Consider reducing automation frequency.")

            input("\nPress Enter to close browser...")

        except Exception as e:
            self.log_error(f"Fatal error: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
                self.log_info("Browser closed")

    def _fill_application_form_step(self):
        """Fill forms in current step with required field validation and AI suggestions"""
        filled_count = 0

        try:
            # Find all form elements that need filling
            form_elements = self._find_all_form_elements()

            if not form_elements:
                self.log_info("ℹ️ No form fields found in this step")
                return 0

            self.log_info(f"📋 Found {len(form_elements)} form fields to process")

            # First pass: Fill fields with existing answers
            for i, (element, field_type, context) in enumerate(form_elements, 1):
                self.log_info(f"\n📝 FIELD {i}/{len(form_elements)}: {context}")

                # Check if we already have an answer
                saved_answer = self._get_answer_for_field(context)

                if saved_answer:
                    self.log_info(f"✅ Using saved answer: {saved_answer}")
                    if self._fill_element_with_value(element, field_type, saved_answer):
                        filled_count += 1
                        time.sleep(random.uniform(1, 2))
                else:
                    self.log_info(f"❓ No saved answer for: {context}")

            # Second pass: Handle any remaining required fields
            remaining_elements = []
            for element, field_type, context in form_elements:
                if self._is_field_empty(element, field_type):
                    remaining_elements.append((element, field_type, context))

            if remaining_elements:
                self.log_info(
                    f"\n📝 Processing {len(remaining_elements)} unfilled fields..."
                )

                # Check for required fields among the remaining
                empty_required_fields = []
                optional_fields = []

                for element, field_type, context in remaining_elements:
                    if self._is_field_required(element):
                        empty_required_fields.append((element, field_type, context))
                    else:
                        optional_fields.append((element, field_type, context))

                # Handle required fields first - MUST be filled
                if empty_required_fields:
                    self.log_info(
                        f"⚠️  Found {len(empty_required_fields)} REQUIRED fields"
                    )
                    if not self._handle_required_fields(empty_required_fields):
                        self.log_error("Cannot proceed - required fields not filled")
                        return 0
                    filled_count += len(empty_required_fields)

                # Handle optional fields - ask user but allow skip
                if optional_fields:
                    self.log_info(f"📝 Found {len(optional_fields)} optional fields")
                    for element, field_type, context in optional_fields:
                        answer = self._ask_user_for_optional_field(
                            context, field_type, element
                        )
                        if answer:
                            if self._fill_element_with_value(
                                element, field_type, answer
                            ):
                                self._save_answer_to_database(context, answer)
                                filled_count += 1
                                self.log_info(f"✅ Filled optional field: {answer}")
                        else:
                            self.log_info(f"⏭️ Skipped optional field: {context}")

            if filled_count > 0:
                self.log_info(f"\n✅ STEP SUMMARY: Filled {filled_count} fields")
                # Pause after filling all fields to let form process
                self.log_info("⏳ Waiting for form to process...")
                time.sleep(random.uniform(3, 5))

        except Exception as e:
            self.log_error(f"Error filling form step: {e}")

        return filled_count

    def _ask_user_for_optional_field(self, question, field_type, element):
        """Ask user for optional field with AI suggestion"""
        # Get AI suggestion
        ai_suggestion = self._get_ai_suggestion(question, field_type, element)

        print(f"\n📝 OPTIONAL FIELD: {question}")

        if ai_suggestion:
            print(f"🤖 AI suggests: {ai_suggestion}")
            choice = input(
                "1) Use AI answer  2) Your answer  3) Skip  (1/2/3): "
            ).strip()

            if choice == "1":
                return ai_suggestion
            elif choice == "2":
                answer = input("Your answer: ").strip()
                return answer if answer else None
            else:
                return None
        else:
            answer = input("Your answer (or 'skip'): ").strip()
            return answer if answer.lower() != "skip" else None

    def _find_easy_apply_button(self):
        """Enhanced Easy Apply button detection with better selectors"""
        # Wait for job details to load
        time.sleep(2)

        # More comprehensive selectors for Easy Apply buttons
        selectors = [
            # New LinkedIn selectors (2024/2025)
            "button.jobs-apply-button",
            "button[aria-label*='Easy Apply']",
            "button[data-control-name='jobdetails_topcard_inapply']",
            ".jobs-unified-top-card__content button[aria-label*='Easy Apply']",
            "div.jobs-apply-button button",
            ".jobs-unified-top-card__content .jobs-apply-button",
            # Alternative selectors
            "button.jobs-s-apply-button",
            "button[class*='apply-button']",
            ".jobs-apply-form button",
            "button[data-test-id*='apply']",
            # XPath fallbacks
            "//button[contains(text(), 'Easy Apply')]",
            "//button[contains(@aria-label, 'Easy Apply')]",
            "//button[.//span[contains(text(), 'Easy Apply')]]",
        ]

        for selector in selectors:
            try:
                if selector.startswith("//"):
                    # XPath selector
                    buttons = self.driver.find_elements(By.XPATH, selector)
                else:
                    # CSS selector
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for button in buttons:
                    try:
                        if not button.is_displayed() or not button.is_enabled():
                            continue

                        # Check button text and attributes
                        button_text = button.text.lower().strip()
                        aria_label = (button.get_attribute("aria-label") or "").lower()
                        data_control = button.get_attribute("data-control-name") or ""

                        # Validate it's actually an Easy Apply button
                        is_easy_apply = any(
                            [
                                "easy apply" in button_text,
                                "easy apply" in aria_label,
                                "inapply" in data_control,
                                button_text == "apply",
                            ]
                        )

                        if is_easy_apply:
                            self.log_info(
                                f"Found Easy Apply button: {button_text or aria_label}"
                            )
                            return button

                    except Exception as e:
                        continue

            except Exception as e:
                continue

        # Final attempt - look for any apply button
        try:
            apply_buttons = self.driver.find_elements(
                By.XPATH,
                "//button[contains(translate(text(), 'APPLY', 'apply'), 'apply')]",
            )
            for button in apply_buttons:
                if button.is_displayed() and button.is_enabled():
                    button_text = button.text.lower()
                    if "apply" in button_text and "save" not in button_text:
                        self.log_info(f"Found generic apply button: {button.text}")
                        return button
        except:
            pass

        return None

        """Fill forms in current step with user interaction and question saving"""
        filled_count = 0

        try:
            # Find all form elements that need filling
            form_elements = self._find_all_form_elements()

            if not form_elements:
                self.log_info("ℹ️ No form fields found in this step")
                return 0

            self.log_info(f"📋 Found {len(form_elements)} form fields to process")

            # Process each form element
            for i, (element, field_type, context) in enumerate(form_elements, 1):
                self.log_info(f"\n📝 FIELD {i}/{len(form_elements)}: {context}")

                # Check if we already have an answer
                saved_answer = self._get_answer_for_field(context)

                if saved_answer:
                    self.log_info(f"✅ Using saved answer: {saved_answer}")
                    if self._fill_element_with_value(element, field_type, saved_answer):
                        filled_count += 1
                        time.sleep(random.uniform(1, 2))  # Human-like delay
                else:
                    # Ask user for new answer
                    self.log_info(f"❓ New question found: {context}")

                    # Pause and ask user
                    user_answer = self._ask_user_for_answer(
                        context, field_type, element
                    )

                    if user_answer:
                        # Save to questions database
                        self._save_answer_to_database(context, user_answer)

                        # Fill the field
                        if self._fill_element_with_value(
                            element, field_type, user_answer
                        ):
                            filled_count += 1
                            self.log_info(f"✅ Filled field with: {user_answer}")
                        else:
                            self.log_error(f"❌ Failed to fill field")
                    else:
                        self.log_info("⏭️ Skipping field (no answer provided)")

                # Pause between fields
                if i < len(form_elements):
                    time.sleep(random.uniform(1, 3))

            if filled_count > 0:
                self.log_info(f"\n✅ STEP SUMMARY: Filled {filled_count} fields")
                # Pause after filling all fields to let form process
                self.log_info("⏳ Waiting for form to process...")
                time.sleep(random.uniform(3, 5))

        except Exception as e:
            self.log_error(f"Error filling form step: {e}")

        return filled_count

    def _find_all_form_elements(self):
        """Find all form elements that need filling"""
        elements = []

        try:
            # Text inputs
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR,
                "input[type='text']:not([readonly]):not([disabled]), "
                + "input[type='email']:not([readonly]):not([disabled]), "
                + "input[type='tel']:not([readonly]):not([disabled]), "
                + "textarea:not([readonly]):not([disabled])",
            )

            for input_elem in text_inputs:
                if self._should_fill_input(input_elem):
                    context = self._get_field_context(input_elem)
                    if context:  # Only include if we can determine what it's asking
                        elements.append((input_elem, "text", context))

            # Dropdowns
            dropdowns = self.driver.find_elements(
                By.CSS_SELECTOR, "select:not([disabled])"
            )
            for dropdown in dropdowns:
                if dropdown.is_displayed() and dropdown.is_enabled():
                    context = self._get_field_context(dropdown)
                    if context:
                        elements.append((dropdown, "dropdown", context))

            # File uploads
            file_inputs = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='file']"
            )
            for file_input in file_inputs:
                if file_input.is_displayed() and not file_input.get_attribute("value"):
                    context = self._get_field_context(file_input) or "Resume/CV Upload"
                    elements.append((file_input, "file", context))

            # Radio button groups
            radio_groups = self._get_radio_button_groups()
            for group_name, radios in radio_groups.items():
                context = self._get_field_context(radios[0])
                if context:
                    elements.append((radios, "radio", context))

            # Checkboxes (for agreements, etc.)
            checkboxes = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='checkbox']:not([disabled])"
            )
            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled():
                    context = self._get_field_context(checkbox)
                    if context and any(
                        word in context.lower()
                        for word in [
                            "agree",
                            "terms",
                            "privacy",
                            "consent",
                            "acknowledge",
                        ]
                    ):
                        elements.append((checkbox, "checkbox", context))

        except Exception as e:
            self.log_error(f"Error finding form elements: {e}")

        return elements

    def _ask_user_for_answer(self, question, field_type, element):
        """Ask user for answer to a form question"""
        print(f"\n{'='*60}")
        print(f"📋 NEW FORM QUESTION")
        print(f"{'='*60}")
        print(f"Question: {question}")
        print(f"Field Type: {field_type}")

        # Show field options if dropdown or radio
        if field_type == "dropdown":
            try:
                select = Select(element)
                options = [opt.text for opt in select.options if opt.text.strip()]
                if options:
                    print(
                        f"Available options: {', '.join(options[:10])}"
                    )  # Show first 10
            except:
                pass
        elif field_type == "radio":
            try:
                labels = [self._get_radio_label(radio) for radio in element]
                labels = [label for label in labels if label]
                if labels:
                    print(f"Available options: {', '.join(labels)}")
            except:
                pass
        elif field_type == "file":
            resume_path = self.get("resume_path")
            if resume_path and os.path.exists(resume_path):
                print(f"Will upload resume: {resume_path}")
                return "upload_resume"
            else:
                print("❌ No resume configured")
                return None
        elif field_type == "checkbox":
            print("This appears to be an agreement checkbox")

        # Get user input
        while True:
            if field_type == "checkbox":
                answer = input("Accept/Agree? (y/n/skip): ").strip().lower()
                if answer in ["y", "yes"]:
                    return "Yes"
                elif answer in ["n", "no"]:
                    return "No"
                elif answer in ["skip", "s"]:
                    return None
                else:
                    print("Please enter y/n/skip")
                    continue
            else:
                answer = input("Your answer (or 'skip' to skip): ").strip()
                if answer.lower() == "skip":
                    return None
                elif answer:
                    return answer
                else:
                    print("Please provide an answer or type 'skip'")
                    continue

    def _save_answer_to_database(self, question, answer):
        """Save question and answer to questions_log.json"""
        try:
            # Load existing questions
            questions_file = "questions_log.json"
            if os.path.exists(questions_file):
                with open(questions_file, "r") as f:
                    questions_db = json.load(f)
            else:
                questions_db = {}

            # Add new question
            questions_db[question] = answer

            # Save back to file
            with open(questions_file, "w") as f:
                json.dump(questions_db, f, indent=2)

            self.log_info(f"💾 Saved answer to database: {question} = {answer}")

        except Exception as e:
            self.log_error(f"Error saving answer to database: {e}")

    def _fill_element_with_value(self, element, field_type, value):
        """Fill form element with specific value"""
        try:
            if field_type == "text":
                element.clear()
                self._type_human_like(element, str(value))
                return True

            elif field_type == "dropdown":
                select = Select(element)
                # Try exact match first
                try:
                    select.select_by_visible_text(str(value))
                    return True
                except:
                    # Try partial match
                    for option in select.options:
                        if str(value).lower() in option.text.lower():
                            select.select_by_visible_text(option.text)
                            return True
                return False

            elif field_type == "file":
                if value == "upload_resume":
                    resume_path = self.get("resume_path")
                    if resume_path and os.path.exists(resume_path):
                        element.send_keys(resume_path)
                        return True
                return False

            elif field_type == "radio":
                # element is a list of radio buttons
                for radio in element:
                    radio_label = self._get_radio_label(radio)
                    if self._answer_matches_radio(str(value), radio_label):
                        if not radio.is_selected():
                            click_element_safely(self.driver, radio)
                            return True
                return False

            elif field_type == "checkbox":
                should_check = str(value).lower() in [
                    "yes",
                    "true",
                    "1",
                    "agree",
                    "accept",
                ]
                if should_check and not element.is_selected():
                    click_element_safely(self.driver, element)
                    return True
                elif not should_check and element.is_selected():
                    click_element_safely(self.driver, element)
                    return True
                return True  # Already in correct state

        except Exception as e:
            self.log_error(f"Error filling element: {e}")

        return False

    def _get_radio_button_groups(self):
        """Get radio buttons grouped by name"""
        radio_groups = {}
        radios = self.driver.find_elements(
            By.CSS_SELECTOR, "input[type='radio']:not([disabled])"
        )

        for radio in radios:
            if radio.is_displayed():
                name = radio.get_attribute("name")
                if name:
                    if name not in radio_groups:
                        radio_groups[name] = []
                    radio_groups[name].append(radio)

        return radio_groups

    def _type_human_like(self, element, text):
        """Type text with human-like delays"""
        for char in str(text):
            element.send_keys(char)
            time.sleep(random.uniform(0.08, 0.25))  # Slower, more human-like

    def _pause_and_show_step(self, step_number, job_info):
        """Pause and show user what's happening in this step"""
        print(f"\n{'='*60}")
        print(f"📋 APPLICATION STEP {step_number}")
        print(f"{'='*60}")
        print(f"Job: {job_info['title']}")
        print(f"Company: {job_info['company']}")
        print(f"Step: {step_number}")
        print(f"{'='*60}")

        # Show current page URL and title for context
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title
            print(f"Page: {page_title}")
            print(f"URL: {current_url}")
        except:
            pass

        # Brief pause to let user see what's happening
        print("Processing this step...")
        time.sleep(2)

        """Enhanced field context detection"""
        context = ""

        try:
            # Try multiple methods to get field context
            methods = [
                lambda: element.get_attribute("placeholder"),
                lambda: element.get_attribute("aria-label"),
                lambda: element.get_attribute("aria-labelledby")
                and self._get_element_by_id(element.get_attribute("aria-labelledby")),
                lambda: element.get_attribute("name"),
                lambda: element.get_attribute("id"),
                lambda: self._get_label_for_element(element),
                lambda: self._get_nearby_text(element),
                lambda: self._get_parent_text(element),
            ]

            for method in methods:
                try:
                    result = method()
                    if result and result.strip() and len(result.strip()) > 2:
                        # Clean up the context
                        context = result.strip()
                        # Remove asterisks and other formatting
                        context = re.sub(r"[*\n\r\t]+", " ", context)
                        context = re.sub(r"\s+", " ", context).strip()
                        if len(context) > 5:  # Only use meaningful context
                            break
                except:
                    continue

        except Exception as e:
            pass

        return context

    def _get_field_context(self, element):
        """Get comprehensive context for a form field including question text"""
        try:
            # Method 1: Look for label with for attribute pointing to this element
            element_id = element.get_attribute("id")
            if element_id:
                try:
                    label = self.driver.find_element(
                        By.CSS_SELECTOR, f"label[for='{element_id}']"
                    )
                    if label.is_displayed() and label.text.strip():
                        return self._clean_question_text(label.text.strip())
                except:
                    pass

            # Method 2: Look for parent label
            try:
                parent = element.find_element(By.XPATH, "./..")
                if parent.tag_name.lower() == "label" and parent.text.strip():
                    return self._clean_question_text(parent.text.strip())
            except:
                pass

            # Method 3: Look for fieldset legend (for radio groups)
            try:
                fieldset = element.find_element(By.XPATH, "./ancestor::fieldset[1]")
                legend = fieldset.find_element(By.TAG_NAME, "legend")
                if legend.is_displayed() and legend.text.strip():
                    return self._clean_question_text(legend.text.strip())
            except:
                pass

            # Method 4: Look in preceding siblings for question text
            try:
                # Check previous siblings of the element
                siblings = element.find_elements(By.XPATH, "./preceding-sibling::*")
                for sibling in reversed(siblings[-3:]):  # Check last 3 siblings
                    if sibling.is_displayed() and sibling.text.strip():
                        text = sibling.text.strip()
                        if self._is_likely_question_text(text):
                            return self._clean_question_text(text)
            except:
                pass

            # Method 5: Look in parent container for question text
            try:
                # Go up the DOM tree looking for containers with question text
                current = element
                for _ in range(4):  # Check up to 4 levels up
                    parent = current.find_element(By.XPATH, "./..")

                    # Look for text elements within this parent
                    text_elements = parent.find_elements(By.XPATH, ".//*[text()]")
                    for text_elem in text_elements:
                        if (
                            text_elem.is_displayed()
                            and text_elem != element
                            and text_elem.text.strip()
                        ):
                            text = text_elem.text.strip()
                            if self._is_likely_question_text(text):
                                return self._clean_question_text(text)

                    # Also check the parent's own text
                    if parent.text.strip():
                        # Extract just the question part, not the input content
                        parent_text = parent.text.strip()
                        lines = parent_text.split("\n")
                        for line in lines:
                            line = line.strip()
                            if line and self._is_likely_question_text(line):
                                return self._clean_question_text(line)

                    current = parent
            except:
                pass

            # Method 6: Check aria-label and similar attributes
            aria_label = element.get_attribute("aria-label")
            if aria_label and aria_label.strip():
                return self._clean_question_text(aria_label.strip())

            placeholder = element.get_attribute("placeholder")
            if (
                placeholder
                and placeholder.strip()
                and self._is_likely_question_text(placeholder)
            ):
                return self._clean_question_text(placeholder.strip())

            # Method 7: Last resort - try to make sense of name/id
            name = element.get_attribute("name")
            if name and not name.startswith("urn:li:") and len(name) > 5:
                # Convert technical names to readable text
                readable_name = name.replace("_", " ").replace("-", " ")
                readable_name = re.sub(
                    r"([a-z])([A-Z])", r"\1 \2", readable_name
                )  # camelCase
                readable_name = " ".join(
                    [word.capitalize() for word in readable_name.split()]
                )
                return readable_name

            return "Unknown question"

        except Exception as e:
            self.log_error(f"Error getting field context: {e}")
            return "Unknown question"


    def _is_likely_question_text(self, text):
        """Enhanced check if text looks like a question"""
        if not text or len(text) < 5 or len(text) > 300:
            return False

        # Skip common non-question text
        skip_patterns = [
            "select an option",
            "choose one",
            "click here",
            "enter your",
            "type here",
            "loading",
            "error",
            "success",
            "warning",
            "info",
            "submit",
            "cancel",
            "close",
        ]

        text_lower = text.lower().strip()
        for pattern in skip_patterns:
            if pattern in text_lower:
                return False

        # Strong question indicators
        strong_indicators = [
            "?",  # Ends with question mark
            "how many years",
            "do you have",
            "are you",
            "have you",
            "will you",
            "can you",
            "what is your",
            "what are your",
            "which of the following",
            "experience do you",
            "years of experience",
        ]

        for indicator in strong_indicators:
            if indicator in text_lower:
                return True

        # Moderate question indicators (need multiple)
        moderate_indicators = [
            "years",
            "experience",
            "work with",
            "familiar with",
            "knowledge of",
            "proficient",
            "skills",
            "location",
            "salary",
            "compensation",
            "available",
            "willing",
            "able to",
        ]

        matches = sum(1 for indicator in moderate_indicators if indicator in text_lower)
        return matches >= 2


    def _ask_user_for_answer(self, question, field_type, element):
        """Ask user for answer to a form question"""
        # Get the ACTUAL question text instead of using the CSS selector
        actual_question = self._get_field_context(element)

        print(f"\n{'='*60}")
        print(f"📋 REQUIRED FIELD")
        print(f"{'='*60}")
        print(f"Question: {actual_question}")  # Use actual question text here
        print(f"Field Type: {field_type}")

        # Show field options if dropdown or radio
        if field_type == "dropdown":
            try:
                select = Select(element)
                options = [opt.text for opt in select.options if opt.text.strip()]
                if options:
                    print(f"Available options: {', '.join(options[:10])}")  # Show first 10
            except:
                pass
        elif field_type == "radio":
            try:
                labels = [self._get_radio_label(radio) for radio in element]
                labels = [label for label in labels if label]
                if labels:
                    print(f"Available options: {', '.join(labels)}")
            except:
                pass
        elif field_type == "file":
            resume_path = self.get("resume_path")
            if resume_path and os.path.exists(resume_path):
                print(f"Will upload resume: {resume_path}")
                return "upload_resume"
            else:
                print("❌ No resume configured")
                return None
        elif field_type == "checkbox":
            print("This appears to be an agreement checkbox")

        # Get AI suggestion first
        ai_suggestion = self._get_ai_suggestion(actual_question, field_type, element)

        if ai_suggestion:
            print(f"🤖 AI Suggestion: {ai_suggestion}")

            # For automation, we want to add options
            print("Options:")
            print("1. Use AI suggestion: " + str(ai_suggestion))
            print("2. Enter your own answer")
            print("3. Skip (cannot proceed)")

            choice = input("Choose (1/2/3): ").strip()

            if choice == "1":
                # Save this Q&A for future use
                self._save_question_answer(actual_question, ai_suggestion)
                return ai_suggestion
            elif choice == "2":
                answer = input("Enter your answer: ").strip()
                if answer:
                    self._save_question_answer(actual_question, answer)
                    return answer
            else:
                return None
        else:
            # No AI suggestion available
            answer = input("Your answer (or 'skip'): ").strip()
            if answer.lower() != "skip" and answer:
                self._save_question_answer(actual_question, answer)
                return answer

        return None

    def _clean_question_text(self, text):
        """Clean up question text to remove formatting and extract the actual question"""
        if not text:
            return ""

        # Remove extra whitespace and normalize
        text = re.sub(r"\s+", " ", text).strip()

        # Remove asterisks and formatting
        text = re.sub(r"[*]+", "", text)

        # Remove "required" indicators
        text = re.sub(r"\s*\(required\)\s*$", "", text, flags=re.IGNORECASE)
        text = re.sub(r"\s*\*\s*$", "", text)
        text = re.sub(r"\s*required\s*$", "", text, flags=re.IGNORECASE)

        # If text contains multiple lines, try to extract just the question
        if "\n" in text:
            lines = [line.strip() for line in text.split("\n") if line.strip()]
            # Find the line that looks most like a question
            for line in lines:
                if self._is_likely_question_text(line):
                    return line.strip()
            # If no clear question found, return the first substantial line
            for line in lines:
                if len(line) > 10:
                    return line.strip()

        return text.strip()

    def _find_question_text_near_element(self, element):
        """Find visible question text near the form element"""
        try:
            # Look for text in preceding elements
            preceding_elements = element.find_elements(
                By.XPATH, "./preceding-sibling::*[position()<=3]"
            )
            for elem in reversed(preceding_elements):
                if elem.is_displayed():
                    text = elem.text.strip()
                    if text and self._is_likely_question_text(text):
                        return text

            # Look for text in parent's preceding siblings
            parent = element.find_element(By.XPATH, "./..")
            parent_siblings = parent.find_elements(
                By.XPATH, "./preceding-sibling::*[position()<=2]"
            )
            for elem in reversed(parent_siblings):
                if elem.is_displayed():
                    text = elem.text.strip()
                    if text and self._is_likely_question_text(text):
                        return text

            # Look for text in the same container
            container = element.find_element(
                By.XPATH,
                "./ancestor::*[contains(@class,'form') or contains(@class,'field') or contains(@class,'question')][1]",
            )
            visible_text = container.text.strip()
            if visible_text and self._is_likely_question_text(visible_text):
                # Extract just the question part (before the input)
                lines = visible_text.split("\n")
                for line in lines:
                    if line.strip() and self._is_likely_question_text(line.strip()):
                        return line.strip()
        except:
            pass

        return ""

    def _is_likely_question_text(self, text):
        """Check if text looks like a question"""
        if len(text) < 5 or len(text) > 200:
            return False

        # Skip common non-question text
        skip_patterns = [
            "select an option",
            "choose one",
            "click here",
            "enter your",
            "type here",
            "required",
            "optional",
        ]

        text_lower = text.lower()
        for pattern in skip_patterns:
            if pattern in text_lower:
                return False

        # Look for question indicators
        question_indicators = [
            "?",  # Ends with question mark
            "do you",
            "are you",
            "have you",
            "will you",
            "can you",
            "how many",
            "what is",
            "what are",
            "which",
            "where",
            "when",
            "why",
            "experience",
            "years",
            "salary",
            "location",
            "willing",
            "able to",
            "available",
        ]

        return any(indicator in text_lower for indicator in question_indicators)

    def _find_fieldset_legend(self, element):
        """Find fieldset legend that groups this element"""
        try:
            fieldset = element.find_element(By.XPATH, "./ancestor::fieldset[1]")
            legend = fieldset.find_element(By.TAG_NAME, "legend")
            if legend.is_displayed():
                return legend.text.strip()
        except:
            pass
        return ""

    def _get_label_text(self, element):
        """Get label text for form element"""
        try:
            # Try label by for attribute
            element_id = element.get_attribute("id")
            if element_id:
                label = self.driver.find_element(
                    By.CSS_SELECTOR, f"label[for='{element_id}']"
                )
                if label.is_displayed():
                    return label.text.strip()
        except:
            pass

        try:
            # Try parent label
            parent = element.find_element(By.XPATH, "./..")
            if parent.tag_name.lower() == "label":
                return parent.text.strip()
        except:
            pass

        return ""

    def _get_parent_question_text(self, element):
        """Get question text from parent containers"""
        try:
            # Look up the DOM tree for containers with question text
            ancestors = element.find_elements(By.XPATH, "./ancestor::*[position()<=4]")
            for ancestor in ancestors:
                if ancestor.is_displayed():
                    # Look for specific question elements within this ancestor
                    question_selectors = [
                        ".//*[contains(@class,'question')]",
                        ".//*[contains(@class,'label')]",
                        ".//label",
                        ".//legend",
                        ".//h3",
                        ".//h4",
                        ".//strong",
                    ]

                    for selector in question_selectors:
                        try:
                            question_elements = ancestor.find_elements(
                                By.XPATH, selector
                            )
                            for q_elem in question_elements:
                                if q_elem.is_displayed():
                                    text = q_elem.text.strip()
                                    if text and self._is_likely_question_text(text):
                                        return text
                        except:
                            continue
        except:
            pass

        return ""

    def _find_nearby_heading(self, element):
        """Find nearby heading elements"""
        try:
            # Look for headings in the vicinity
            heading_xpath = "./ancestor::*[position()<=3]//h1 | ./ancestor::*[position()<=3]//h2 | ./ancestor::*[position()<=3]//h3 | ./ancestor::*[position()<=3]//h4 | ./ancestor::*[position()<=3]//strong"
            headings = element.find_elements(By.XPATH, heading_xpath)

            for heading in headings:
                if heading.is_displayed():
                    text = heading.text.strip()
                    if text and self._is_likely_question_text(text):
                        return text
        except:
            pass

        return ""

    def _get_fallback_context(self, element):
        """Get fallback context when all else fails"""
        # Try aria-label
        aria_label = element.get_attribute("aria-label")
        if aria_label and len(aria_label) > 5:
            return self._clean_question_text(aria_label)

        # Try name attribute if it's somewhat readable
        name = element.get_attribute("name")
        if name and len(name) > 5 and not name.startswith("urn:"):
            # Convert camelCase or snake_case to readable text
            readable_name = name.replace("_", " ").replace("-", " ")
            readable_name = " ".join(
                [word.capitalize() for word in readable_name.split()]
            )
            return readable_name

        return "Unknown question"

    def _clean_question_text(self, text):
        """Clean up question text"""
        if not text:
            return ""

        # Remove asterisks and other formatting
        text = re.sub(r"[*\n\r\t]+", " ", text)
        text = re.sub(r"\s+", " ", text).strip()

        # Remove "required" and similar suffixes
        text = re.sub(r"\s*\(required\)\s*$", "", text, flags=re.IGNORECASE)
        text = re.sub(r"\s*\*\s*$", "", text)

        return text.strip()

    def _is_field_required(self, element):
        """Check if a form field is required"""
        try:
            # Check required attribute
            if element.get_attribute("required"):
                return True

            # Check aria-required
            if element.get_attribute("aria-required") == "true":
                return True

            # Look for asterisk in nearby text
            nearby_text = self._get_field_context(element)
            if "*" in nearby_text or "required" in nearby_text.lower():
                return True

            # Check for required class
            classes = element.get_attribute("class") or ""
            if "required" in classes.lower():
                return True

            # Check parent elements for required indicators
            try:
                parent = element.find_element(By.XPATH, "./..")
                parent_text = parent.text.lower()
                if "*" in parent_text or "required" in parent_text:
                    return True
            except:
                pass

        except Exception as e:
            pass

        return False

    def _validate_required_fields(self, form_elements):
        """Check if any required fields are empty"""
        empty_required_fields = []

        for element, field_type, context in form_elements:
            if self._is_field_required(element):
                if self._is_field_empty(element, field_type):
                    empty_required_fields.append((element, field_type, context))

        return empty_required_fields

    def _is_field_empty(self, element, field_type):
        """Check if a field is empty"""
        try:
            if field_type == "text":
                value = element.get_attribute("value") or ""
                return value.strip() == ""
            elif field_type == "dropdown":
                select = Select(element)
                selected = select.first_selected_option.text.strip()
                return selected == "" or selected.lower() in [
                    "select an option",
                    "choose one",
                    "select...",
                ]
            elif field_type == "radio":
                # element is a list of radio buttons
                return not any(radio.is_selected() for radio in element)
            elif field_type == "checkbox":
                # For required checkboxes (like agreements), they should be checked
                return not element.is_selected()
            elif field_type == "file":
                return not element.get_attribute("value")
        except:
            pass

        return True

    def _handle_required_fields(self, empty_required_fields):
        """Handle empty required fields - don't proceed until filled"""
        if not empty_required_fields:
            return True

        print(f"\n⚠️  REQUIRED FIELDS MISSING!")
        print(f"Found {len(empty_required_fields)} required fields that need answers:")

        for i, (element, field_type, context) in enumerate(empty_required_fields, 1):
            print(f"\n{i}. {context}")
            print(f"   Type: {field_type}")

            # Get AI suggestion
            ai_suggestion = self._get_ai_suggestion(context, field_type, element)

            # Present options to user
            answer = self._ask_user_with_ai_option(
                context, field_type, element, ai_suggestion
            )

            if answer:
                # Fill the field
                if self._fill_element_with_value(element, field_type, answer):
                    print(f"✅ Filled required field: {answer}")
                    # Save to database
                    self._save_answer_to_database(context, answer)
                else:
                    print(f"❌ Failed to fill field")
                    return False
            else:
                print(f"⚠️  Required field left empty - cannot proceed")
                return False

        return True

    def _get_ai_suggestion(self, question, field_type, element):
        """Get AI suggestion for form field"""
        try:
            # Try Ollama first (local LLM)
            suggestion = self._get_ollama_suggestion(question, field_type, element)
            if suggestion:
                return suggestion

            # Fallback to rule-based suggestions
            return self._get_rule_based_suggestion(question, field_type)

        except Exception as e:
            self.log_error(f"Error getting AI suggestion: {e}")
            return self._get_rule_based_suggestion(question, field_type)

    def _get_ollama_suggestion(self, question, field_type, element):
        """Get suggestion from local Ollama LLM"""
        try:
            import requests

            # Get context about the user
            user_profile = self.get("user_profile", {})
            years_exp = user_profile.get("years_experience", "10+")
            location = user_profile.get("location", "Cleveland, OH")
            skills = ", ".join(user_profile.get("skills", [])[:5])

            # Get options if dropdown/radio
            options_text = ""
            if field_type == "dropdown":
                try:
                    select = Select(element)
                    options = [opt.text for opt in select.options if opt.text.strip()]
                    if options:
                        options_text = f"\nAvailable options: {', '.join(options[:10])}"
                except:
                    pass
            elif field_type == "radio":
                try:
                    labels = [self._get_radio_label(radio) for radio in element]
                    labels = [label for label in labels if label]
                    if labels:
                        options_text = f"\nAvailable options: {', '.join(labels)}"
                except:
                    pass

            # Create prompt for Ollama
            prompt = f"""You are helping fill out a job application form. Based on this profile:
- Experience: {years_exp} years in software development
- Location: {location}  
- Skills: {skills}
- Work authorization: US Citizen
- Willing to relocate: No
- Remote preference: Remote or Hybrid

Question: {question}{options_text}

Provide a brief, appropriate answer for this job application question. If it's a yes/no question, answer yes or no. If it's asking for years of experience, give a number. Keep it professional and honest.

Answer:"""

            # Call Ollama API
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": "llama3.2:3b",  # or whatever model is available
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.3, "num_predict": 50},
                },
                timeout=10,
            )

            if response.status_code == 200:
                result = response.json()
                suggestion = result.get("response", "").strip()
                if suggestion:
                    # Clean up the suggestion
                    suggestion = suggestion.split("\n")[0]  # First line only
                    suggestion = suggestion.strip("\"'")  # Remove quotes
                    return suggestion

        except Exception as e:
            self.log_error(f"Ollama not available: {e}")

        return None

    def _get_rule_based_suggestion(self, question, field_type):
        """Get rule-based suggestion when AI is not available"""
        question_lower = question.lower()

        # Years of experience
        if "year" in question_lower and "experience" in question_lower:
            return "10"

        # Authorization/citizenship
        if any(
            word in question_lower
            for word in ["authorized", "citizen", "legal", "eligible"]
        ):
            return "Yes"

        # Relocation
        if "relocat" in question_lower:
            return "No"

        # Remote work
        if "remote" in question_lower:
            return "Yes"

        # Salary expectations
        if "salary" in question_lower or "compensation" in question_lower:
            return "80000"

        # Notice period
        if "notice" in question_lower:
            return "2 weeks"

        # Availability
        if "available" in question_lower or "start" in question_lower:
            return "Immediately"

        # General yes/no questions
        if field_type in ["dropdown", "radio"] and any(
            word in question_lower
            for word in ["do you", "are you", "have you", "can you"]
        ):
            return "Yes"

        return None

    def _ask_user_with_ai_option(self, question, field_type, element, ai_suggestion):
        """Ask user with AI suggestion option"""
        print(f"\n{'='*60}")
        print(f"📋 REQUIRED FIELD")
        print(f"{'='*60}")
        print(f"Question: {question}")
        print(f"Field Type: {field_type}")

        # Show options if available
        if field_type == "dropdown":
            try:
                select = Select(element)
                options = [opt.text for opt in select.options if opt.text.strip()]
                if options:
                    print(f"Available options: {', '.join(options[:10])}")
            except:
                pass
        elif field_type == "radio":
            try:
                labels = [self._get_radio_label(radio) for radio in element]
                labels = [label for label in labels if label]
                if labels:
                    print(f"Available options: {', '.join(labels)}")
            except:
                pass

        # Show AI suggestion
        if ai_suggestion:
            print(f"\n🤖 AI Suggestion: {ai_suggestion}")
            print(f"Options:")
            print(f"1. Use AI suggestion: {ai_suggestion}")
            print(f"2. Enter your own answer")
            print(f"3. Skip (cannot proceed)")

            choice = input("Choose (1/2/3): ").strip()

            if choice == "1":
                return ai_suggestion
            elif choice == "2":
                answer = input("Your answer: ").strip()
                return answer if answer else None
            else:
                return None
        else:
            # No AI suggestion available
            print(f"\nThis field is REQUIRED. Please provide an answer:")
            answer = input("Your answer: ").strip()
            return answer if answer else None

    def _get_element_by_id(self, element_id):
        """Get element text by ID"""
        try:
            element = self.driver.find_element(By.ID, element_id)
            return element.text.strip()
        except:
            return ""

    def _get_parent_text(self, element):
        """Get text from parent elements"""
        try:
            parent = element.find_element(By.XPATH, "./..")
            text = parent.text.strip()
            # Filter out very long text (probably not a label)
            if text and len(text) < 200:
                return text
        except:
            pass
        return ""

    def _show_application_summary(self, job_info, step_count, filled_count):
        """Show summary after each step"""
        print(f"\n📊 STEP {step_count} SUMMARY:")
        print(f"   Job: {job_info['title'][:50]}...")
        print(f"   Fields filled: {filled_count}")
        print(f"   Status: Moving to next step")

        # Give user a moment to see the summary
        time.sleep(1)

    def _handle_easy_apply(self, apply_button, job_info):
        """Handle Easy Apply process - FIXED VERSION to prevent infinite loops"""
        try:
            self.log_info(
                f"🚀 STARTING EASY APPLY FOR: {job_info['title']} at {job_info['company']}"
            )

            # Click the initial Easy Apply button
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", apply_button
            )
            time.sleep(2)

            if not click_element_safely(self.driver, apply_button):
                self.log_error("Could not click initial Easy Apply button")
                return False

            # Wait for application modal/form to load
            self.log_info("⏳ Waiting for application form to load...")
            time.sleep(random.uniform(4, 7))

            # Process application steps with careful progression
            step_count = 0
            max_steps = 8

            while step_count < max_steps:
                step_count += 1

                # Show user what's happening
                self._pause_and_show_step(step_count, job_info)

                self.log_info(f"\n📋 === STEP {step_count} ===")

                # Check if application is complete
                if self._is_application_complete():
                    self.log_info("🎉 APPLICATION COMPLETED SUCCESSFULLY!")
                    return True

                # CRITICAL: Fill form BEFORE trying to proceed
                self.log_info("📝 Analyzing form fields...")
                filled_fields = self._fill_application_form_step()

                # Show summary of what was filled
                self._show_application_summary(job_info, step_count, filled_fields)

                # Now try to proceed to next step
                self.log_info("🔍 Looking for Next/Submit button...")
                proceed_result = self._proceed_to_next_step()

                if proceed_result == "completed":
                    self.log_info("🎉 Application completed!")
                    return True
                elif proceed_result == "next":
                    self.log_info("➡️ Moved to next step")
                    # Wait for next step to load
                    self.log_info("⏳ Waiting for next step to load...")
                    time.sleep(random.uniform(4, 7))  # Longer wait
                elif proceed_result == "stuck":
                    self.log_error(
                        f"❌ Stuck on step {step_count} - no progress possible"
                    )

                    # If we filled fields but can't proceed, ask user to check
                    if filled_fields > 0:
                        print(f"\n⚠️ ATTENTION NEEDED!")
                        print(
                            f"Filled {filled_fields} fields but cannot find Next button."
                        )
                        print(f"Please check the browser window.")
                        response = input("Continue anyway? (y/n): ").strip().lower()

                        if response == "y":
                            # Try one more time
                            time.sleep(3)
                            if self._proceed_to_next_step() in ["next", "completed"]:
                                continue

                    # Really stuck - break out
                    self.log_error(f"Cannot proceed from step {step_count}")
                    return False
                else:
                    self.log_error(f"Unknown proceed result: {proceed_result}")
                    return False

        except Exception as e:
            self.log_error(f"Easy Apply error: {str(e)}")
            return False

        """Fill forms in current step and return count of fields filled"""
        filled_count = 0

        try:
            # Text inputs
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR,
                "input[type='text']:not([readonly]), input[type='email']:not([readonly]), input[type='tel']:not([readonly]), textarea:not([readonly])",
            )

            for input_elem in text_inputs:
                if self._should_fill_input(input_elem):
                    value = self._get_input_value(input_elem)
                    if value:
                        input_elem.clear()
                        self._type_human_like(input_elem, str(value))
                        filled_count += 1
                        self.log_info(f"📝 Filled input: {value}")
                        time.sleep(random.uniform(0.5, 1.5))

            # Dropdowns
            dropdowns = self.driver.find_elements(
                By.CSS_SELECTOR, "select:not([disabled])"
            )
            for dropdown in dropdowns:
                if dropdown.is_displayed() and dropdown.is_enabled():
                    context = self._get_field_context(dropdown)
                    value = self._get_answer_for_field(context)
                    if value:
                        try:
                            select = Select(dropdown)
                            select.select_by_visible_text(str(value))
                            filled_count += 1
                            self.log_info(f"📝 Selected dropdown: {value}")
                        except:
                            pass

            # File uploads
            file_inputs = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='file']"
            )
            for file_input in file_inputs:
                if file_input.is_displayed() and not file_input.get_attribute("value"):
                    resume_path = self.get("resume_path")
                    if resume_path and os.path.exists(resume_path):
                        file_input.send_keys(resume_path)
                        filled_count += 1
                        self.log_info("📄 Uploaded resume")
                        time.sleep(2)
                        break

            # Radio buttons and checkboxes
            filled_count += self._handle_radio_and_checkboxes()

        except Exception as e:
            self.log_error(f"Error filling form step: {e}")

        return filled_count

    def _should_fill_input(self, input_elem):
        """Check if input should be filled"""
        try:
            return (
                input_elem.is_displayed()
                and input_elem.is_enabled()
                and not input_elem.get_attribute("value")
                and not input_elem.get_attribute("readonly")
            )
        except:
            return False

    def _get_input_value(self, input_elem):
        """Get appropriate value for input field"""
        try:
            # Get field context
            context = self._get_field_context(input_elem)

            # Try questions database first
            value = self._get_answer_for_field(context)
            if value:
                return value

            # Fallback defaults based on field type/name
            context_lower = context.lower()
            if "phone" in context_lower:
                return self.get("user_profile", {}).get("phone", "4407734642")
            elif "email" in context_lower:
                return self.get("username", "")
            elif "first" in context_lower and "name" in context_lower:
                full_name = self.get("user_profile", {}).get("name", "Giorgiy")
                return full_name.split()[0] if full_name else "Giorgiy"
            elif "last" in context_lower and "name" in context_lower:
                full_name = self.get("user_profile", {}).get("name", "Shepov")
                return full_name.split()[-1] if full_name else "Shepov"
            elif "city" in context_lower:
                return "Cleveland"
            elif "year" in context_lower and "experience" in context_lower:
                return "10"

        except Exception as e:
            self.log_error(f"Error getting input value: {e}")

        return None

    def _proceed_to_next_step(self):
        """Try to proceed to next step, return status"""
        # Button priorities in order
        buttons_to_try = [
            ("Submit application", "completed"),
            ("Submit", "completed"),
            ("Send application", "completed"),
            ("Review", "next"),
            ("Next", "next"),
            ("Continue", "next"),
        ]

        for button_text, result_type in buttons_to_try:
            if self._try_click_button_with_text(button_text):
                self.log_info(f"✅ Clicked: {button_text}")
                return result_type

        # Try generic button selectors
        generic_selectors = [
            "button[data-easy-apply-next-button]",
            "button[data-easy-apply-submit-button]",
            "button[data-control-name*='continue']",
            "button[data-control-name*='submit']",
        ]

        for selector in generic_selectors:
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        if click_element_safely(self.driver, button):
                            self.log_info(f"✅ Clicked button via selector: {selector}")
                            return "next"
            except:
                continue

        return "stuck"

    def _try_click_button_with_text(self, text):
        """Try to click button with specific text"""
        selectors = [
            f"button[aria-label*='{text}']",
            f"//button[contains(text(), '{text}')]",
            f"//button[contains(@aria-label, '{text}')]",
            f"//button[.//span[contains(text(), '{text}')]]",
        ]

        for selector in selectors:
            try:
                if selector.startswith("//"):
                    buttons = self.driver.find_elements(By.XPATH, selector)
                else:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.lower()
                        aria_label = (button.get_attribute("aria-label") or "").lower()

                        # Make sure this button actually contains our target text
                        if text.lower() in button_text or text.lower() in aria_label:
                            if click_element_safely(self.driver, button):
                                return True
            except:
                continue

        return False

    def _handle_radio_and_checkboxes(self):
        """Handle radio buttons and checkboxes"""
        filled_count = 0

        try:
            # Radio buttons
            radio_groups = {}
            radios = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='radio']:not([disabled])"
            )

            for radio in radios:
                name = radio.get_attribute("name")
                if name and radio.is_displayed():
                    if name not in radio_groups:
                        radio_groups[name] = []
                    radio_groups[name].append(radio)

            for group_name, group_radios in radio_groups.items():
                context = self._get_field_context(group_radios[0])
                answer = self._get_answer_for_field(context)
                if answer:
                    for radio in group_radios:
                        radio_label = self._get_radio_label(radio)
                        if self._answer_matches_radio(str(answer), radio_label):
                            if not radio.is_selected():
                                click_element_safely(self.driver, radio)
                                filled_count += 1
                                self.log_info(f"📝 Selected radio: {radio_label}")
                            break

            # Checkboxes - typically for agreements
            checkboxes = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='checkbox']:not([disabled])"
            )
            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled():
                    context = self._get_field_context(checkbox)
                    # For agreements, usually check them
                    if any(
                        word in context.lower()
                        for word in ["agree", "terms", "privacy", "consent"]
                    ):
                        if not checkbox.is_selected():
                            click_element_safely(self.driver, checkbox)
                            filled_count += 1
                            self.log_info(f"✅ Checked agreement: {context}")

        except Exception as e:
            self.log_error(f"Error handling radio/checkboxes: {e}")

        return filled_count

    def _get_radio_label(self, radio):
        """Get label text for radio button"""
        try:
            radio_id = radio.get_attribute("id")
            if radio_id:
                label = self.driver.find_element(
                    By.CSS_SELECTOR, f"label[for='{radio_id}']"
                )
                return label.text.strip()
        except:
            pass

        try:
            parent = radio.find_element(By.XPATH, "./..")
            return parent.text.strip()
        except:
            pass

        return ""

    def _answer_matches_radio(self, answer, option):
        """Check if answer matches radio option"""
        if not answer or not option:
            return False

        answer_lower = answer.lower()
        option_lower = option.lower()

        # Yes/No logic
        if answer_lower in ["yes", "true", "1"] and option_lower in ["yes", "true"]:
            return True
        if answer_lower in ["no", "false", "0"] and option_lower in ["no", "false"]:
            return True

        # Direct or partial match
        return answer_lower == option_lower or answer_lower in option_lower

    def _fill_application_form(self):
        """Enhanced form filling with better field detection"""
        filled_count = 0

        try:
            # Fill text inputs
            filled_count += self._fill_text_inputs_enhanced()

            # Handle dropdowns
            filled_count += self._handle_dropdowns_enhanced()

            # Handle radio buttons
            filled_count += self._handle_radio_buttons_enhanced()

            # Handle checkboxes
            filled_count += self._handle_checkboxes_enhanced()

            # Handle file uploads
            filled_count += self._handle_file_upload_enhanced()

            if filled_count > 0:
                self.log_info(f"Filled {filled_count} form fields")

            return filled_count > 0

        except Exception as e:
            self.log_error(f"Error filling form: {str(e)}")
            return False

    def _fill_text_inputs_enhanced(self):
        """Enhanced text input handling with questions database"""
        filled_count = 0

        # Find all text inputs
        input_selectors = [
            "input[type='text']",
            "input[type='email']",
            "input[type='tel']",
            "input[type='number']",
            "textarea",
        ]

        for selector in input_selectors:
            try:
                inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for input_elem in inputs:
                    if not input_elem.is_displayed() or not input_elem.is_enabled():
                        continue

                    # Skip if already filled
                    if input_elem.get_attribute("value"):
                        continue

                    # Get field context
                    field_info = self._get_field_context(input_elem)

                    # Try to get answer from questions database
                    answer = self._get_answer_for_field(field_info)

                    if answer:
                        # Clear and fill with human-like typing
                        input_elem.clear()
                        self._type_human_like(input_elem, str(answer))
                        filled_count += 1
                        time.sleep(random.uniform(0.5, 1.5))

            except Exception as e:
                continue

        return filled_count

    def _get_field_context(self, element):
        """Get context for a form field to determine what it's asking for"""
        context = ""

        try:
            # Try multiple methods to get field context
            methods = [
                lambda: element.get_attribute("placeholder"),
                lambda: element.get_attribute("aria-label"),
                lambda: element.get_attribute("name"),
                lambda: element.get_attribute("id"),
                lambda: self._get_label_for_element(element),
                lambda: self._get_nearby_text(element),
            ]

            for method in methods:
                try:
                    result = method()
                    if result and result.strip():
                        context = result.strip()
                        break
                except:
                    continue

        except Exception as e:
            pass

        return context

    def _get_answer_for_field(self, field_context):
        """Get answer for a field from questions database or defaults"""
        if not field_context:
            return None

        # Load questions database
        try:
            with open("questions_log.json", "r") as f:
                questions_db = json.load(f)
        except:
            questions_db = {}

        # Direct match first
        if field_context in questions_db:
            return questions_db[field_context]

        # Fuzzy matching
        field_lower = field_context.lower()
        for question, answer in questions_db.items():
            if self._similarity_match(field_lower, question.lower()) > 0.8:
                return answer

        # Default answers for common fields
        defaults = {
            "phone": self.get("user_profile", {}).get("phone", ""),
            "email": self.get("username", ""),
            "first name": (
                self.get("user_profile", {}).get("name", "").split()[0]
                if self.get("user_profile", {}).get("name")
                else ""
            ),
            "last name": (
                self.get("user_profile", {}).get("name", "").split()[-1]
                if self.get("user_profile", {}).get("name")
                else ""
            ),
            "city": (
                self.get("user_profile", {}).get("location", "").split(",")[0]
                if self.get("user_profile", {}).get("location")
                else ""
            ),
            "years of experience": self.get("user_profile", {}).get(
                "years_experience", "10"
            ),
            "salary": "80000",
            "notice period": "2 weeks",
            "willing to relocate": "No",
        }

        for key, value in defaults.items():
            if key in field_lower:
                return value

        return None

    def _similarity_match(self, text1, text2):
        """Calculate similarity between two texts"""
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

    def _click_next_or_submit(self):
        """Enhanced next/submit button clicking"""
        # Button texts in order of preference
        button_texts = [
            "Submit application",
            "Submit",
            "Send application",
            "Apply",
            "Next",
            "Continue",
            "Review",
        ]

        for button_text in button_texts:
            if self._try_click_button_by_text(button_text):
                return True

        # Try data attributes
        data_selectors = [
            "button[data-easy-apply-next-button]",
            "button[data-easy-apply-submit-button]",
            "button[data-control-name*='continue']",
            "button[data-control-name*='submit']",
        ]

        for selector in data_selectors:
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        if click_element_safely(self.driver, button):
                            self.log_info(f"Clicked button via selector: {selector}")
                            return True
            except:
                continue

        return False

    def _try_click_button_by_text(self, text):
        """Try to click a button with specific text"""
        selectors = [
            f"button[aria-label*='{text}']",
            f"//button[contains(text(), '{text}')]",
            f"//button[contains(@aria-label, '{text}')]",
            f"//button[.//span[contains(text(), '{text}')]]",
        ]

        for selector in selectors:
            try:
                if selector.startswith("//"):
                    buttons = self.driver.find_elements(By.XPATH, selector)
                else:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        if click_element_safely(self.driver, button):
                            self.log_info(f"Clicked '{text}' button")
                            return True
            except:
                continue

        return False

    def _check_for_application_errors(self):
        """Check for application form errors"""
        error_indicators = [
            "required field",
            "please complete",
            "error",
            "invalid",
            "must be filled",
        ]

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            for indicator in error_indicators:
                if indicator in page_text:
                    self.log_error(f"Form error detected: {indicator}")
                    return True
        except:
            pass

        return False

    def _check_for_warnings(self):
        """Check for LinkedIn warnings or restrictions"""
        warning_texts = [
            "unusual activity",
            "account restricted",
            "temporarily limited",
            "please verify",
            "suspicious activity",
            "we've noticed",
            "security check",
            "verify your account",
        ]

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()

            for warning in warning_texts:
                if warning in page_text:
                    self.warning_detected = True
                    self.log_error(f"Warning detected: {warning}")
                    return True

        except Exception as e:
            self.log_error(f"Error checking for warnings: {e}")

        return False

    def _safe_delay(self, delay_type):
        """Add human-like delays"""
        delays = {
            "between_jobs": (8, 15),
            "page_scan": (3, 6),
            "form_fill": (1, 3),
            "after_submit": (10, 20),
        }

        min_delay, max_delay = delays.get(delay_type, (2, 4))
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def _simulate_human_behavior(self):
        """Simulate human-like behavior"""
        behaviors = [
            lambda: self.driver.execute_script(
                "window.scrollBy(0, Math.floor(Math.random() * 500));"
            ),
            lambda: time.sleep(random.uniform(2, 5)),
            lambda: self.driver.refresh() if random.random() < 0.1 else None,
        ]

        behavior = random.choice(behaviors)
        try:
            behavior()
        except:
            pass

    def _type_human_like(self, element, text):
        """Type text with human-like delays"""
        for char in str(text):
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))

    def _get_label_for_element(self, element):
        """Get label text for form element"""
        try:
            # Try label by for attribute
            element_id = element.get_attribute("id")
            if element_id:
                label = self.driver.find_element(
                    By.CSS_SELECTOR, f"label[for='{element_id}']"
                )
                return label.text
        except:
            pass

        try:
            # Try parent label
            parent = element.find_element(By.XPATH, "./..")
            if parent.tag_name.lower() == "label":
                return parent.text
        except:
            pass

        return ""

    def _get_nearby_text(self, element):
        """Get nearby text that might describe the field"""
        try:
            # Look for text in previous siblings
            prev_elements = element.find_elements(
                By.XPATH, "./preceding-sibling::*[position()<=3]"
            )
            for prev in reversed(prev_elements):
                text = prev.text.strip()
                if text and len(text) < 100:  # Reasonable label length
                    return text
        except:
            pass

        return ""

    def _handle_dropdowns_enhanced(self):
        """Enhanced dropdown handling"""
        filled_count = 0

        try:
            dropdowns = self.driver.find_elements(By.CSS_SELECTOR, "select")
            for dropdown in dropdowns:
                if not dropdown.is_displayed() or not dropdown.is_enabled():
                    continue

                # Get field context
                field_info = self._get_field_context(dropdown)
                answer = self._get_answer_for_field(field_info)

                if answer:
                    try:
                        select = Select(dropdown)
                        # Try exact match first
                        try:
                            select.select_by_visible_text(str(answer))
                            filled_count += 1
                        except:
                            # Try partial match
                            for option in select.options:
                                if str(answer).lower() in option.text.lower():
                                    select.select_by_visible_text(option.text)
                                    filled_count += 1
                                    break
                    except Exception as e:
                        continue
        except:
            pass

        return filled_count

    def _handle_radio_buttons_enhanced(self):
        """Enhanced radio button handling"""
        filled_count = 0

        try:
            radio_groups = {}
            radios = self.driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")

            # Group by name
            for radio in radios:
                name = radio.get_attribute("name")
                if name:
                    if name not in radio_groups:
                        radio_groups[name] = []
                    radio_groups[name].append(radio)

            # Process each group
            for group_name, group_radios in radio_groups.items():
                try:
                    # Get context for this radio group
                    field_info = self._get_field_context(group_radios[0])
                    answer = self._get_answer_for_field(field_info)

                    if answer:
                        for radio in group_radios:
                            radio_label = self._get_radio_button_label(radio)
                            if self._answer_matches_option(str(answer), radio_label):
                                if not radio.is_selected():
                                    click_element_safely(self.driver, radio)
                                    filled_count += 1
                                break
                except:
                    continue
        except:
            pass

        return filled_count

    def _get_radio_button_label(self, radio):
        """Get label for radio button"""
        try:
            # Try label by for attribute
            radio_id = radio.get_attribute("id")
            if radio_id:
                label = self.driver.find_element(
                    By.CSS_SELECTOR, f"label[for='{radio_id}']"
                )
                return label.text
        except:
            pass

        try:
            # Try parent label
            parent = radio.find_element(By.XPATH, "./..")
            return parent.text
        except:
            pass

        return ""

    def _answer_matches_option(self, answer, option):
        """Check if answer matches radio option"""
        if not answer or not option:
            return False

        answer_lower = answer.lower()
        option_lower = option.lower()

        # Direct matches
        if answer_lower == option_lower:
            return True

        # Yes/No logic
        if answer_lower in ["yes", "true", "1"] and option_lower in [
            "yes",
            "true",
            "agree",
        ]:
            return True
        if answer_lower in ["no", "false", "0"] and option_lower in [
            "no",
            "false",
            "disagree",
        ]:
            return True

        # Partial match
        return answer_lower in option_lower or option_lower in answer_lower

    def _handle_checkboxes_enhanced(self):
        """Enhanced checkbox handling"""
        filled_count = 0

        try:
            checkboxes = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='checkbox']"
            )
            for checkbox in checkboxes:
                if not checkbox.is_displayed() or not checkbox.is_enabled():
                    continue

                # Get context
                field_info = self._get_field_context(checkbox)
                answer = self._get_answer_for_field(field_info)

                if answer:
                    should_check = self._should_check_checkbox(str(answer))
                    if should_check and not checkbox.is_selected():
                        click_element_safely(self.driver, checkbox)
                        filled_count += 1
                    elif not should_check and checkbox.is_selected():
                        click_element_safely(self.driver, checkbox)
                        filled_count += 1
        except:
            pass

        return filled_count

    def _should_check_checkbox(self, answer):
        """Determine if checkbox should be checked"""
        answer_lower = answer.lower()
        positive_answers = ["yes", "true", "1", "agree", "accept", "check"]
        return any(pos in answer_lower for pos in positive_answers)

    def _handle_file_upload_enhanced(self):
        """Enhanced file upload handling"""
        filled_count = 0

        resume_path = self.get("resume_path")
        if not resume_path or not os.path.exists(resume_path):
            return 0

        try:
            file_inputs = self.driver.find_elements(
                By.CSS_SELECTOR, "input[type='file']"
            )
            for file_input in file_inputs:
                if not file_input.is_displayed():
                    continue

                # Check if already has file
                if file_input.get_attribute("value"):
                    continue

                try:
                    file_input.send_keys(resume_path)
                    filled_count += 1
                    self.log_info("Resume uploaded")
                    time.sleep(2)
                    break
                except:
                    continue
        except:
            pass

        return filled_count

    def _try_alternative_proceed_buttons(self):
        """Try alternative ways to proceed when standard buttons don't work"""
        # Try pressing Enter on focused element
        try:
            focused_element = self.driver.switch_to.active_element
            focused_element.send_keys(Keys.RETURN)
            time.sleep(1)
            return True
        except:
            pass

        # Try clicking any prominent buttons
        try:
            buttons = self.driver.find_elements(
                By.CSS_SELECTOR, "button[aria-label], button[type='submit']"
            )
            for button in buttons:
                if button.is_displayed() and button.is_enabled():
                    button_text = button.text.lower()
                    aria_label = (button.get_attribute("aria-label") or "").lower()

                    if any(
                        word in button_text + aria_label
                        for word in ["next", "continue", "submit", "apply"]
                    ):
                        if click_element_safely(self.driver, button):
                            return True
        except:
            pass

        return False

    def _process_job(self, card, idx):
        """Process a single job card CAREFULLY with user interaction"""
        try:
            self.log_info(f"\n{'='*60}")
            self.log_info(f"PROCESSING JOB {idx}")
            self.log_info(f"{'='*60}")

            # Check for warnings before proceeding
            if self._check_for_warnings():
                self.log_error("Warning detected! Stopping automation for safety.")
                return

            # Human-like delay before clicking job card
            self.log_info("Waiting before clicking job card...")
            time.sleep(random.uniform(3, 6))

            # Click on job card and wait for details to load
            self.log_info(f"Clicking job card {idx}...")
            if not click_element_safely(self.driver, card):
                self.log_error(f"Could not click job card {idx}")
                return

            # Wait for job details to fully load
            self.log_info("Waiting for job details to load...")
            time.sleep(random.uniform(4, 7))

            # Extract job information first
            job_info = self._extract_job_info()
            if not job_info:
                self.log_error(f"Could not extract job info for card {idx}")
                return

            self.log_info(f"📋 JOB DETAILS:")
            self.log_info(f"   Title: {job_info['title']}")
            self.log_info(f"   Company: {job_info['company']}")

            # Look for Easy Apply button (but don't click yet)
            self.log_info("Scanning for Easy Apply button...")
            apply_button = self._find_easy_apply_button_careful()

            if not apply_button:
                self.log_info(
                    f"❌ No Easy Apply found for {job_info['title']} - SKIPPING"
                )
                self.skipped_count += 1
                return

            self.log_info(f"✅ Easy Apply button found for {job_info['title']}")

            # Ask user if in demo mode or if many errors
            if self.demo_mode or self.consecutive_errors > 2:
                self.log_info(
                    f"\n🤔 READY TO APPLY TO: {job_info['title']} at {job_info['company']}"
                )
                if self.demo_mode:
                    self.log_info("DEMO MODE: Simulating application...")
                    self.applied_count += 1
                    time.sleep(2)
                    return
                else:
                    response = (
                        input(f"Apply to this job? (y/n/q to quit): ").strip().lower()
                    )
                    if response == "q":
                        raise KeyboardInterrupt("User requested quit")
                    elif response != "y":
                        self.log_info("Skipping this job per user request")
                        return

            # Rate limiting check
            if self._should_rate_limit():
                self.log_info("Rate limit reached. Taking a break...")
                time.sleep(random.uniform(300, 600))

            # Now actually apply
            self.log_info(f"🚀 STARTING APPLICATION PROCESS...")
            if self._handle_easy_apply_careful(apply_button, job_info):
                self.applied_count += 1
                self.last_application_time = time.time()
                self.consecutive_errors = 0  # Reset on success
                self.log_info(f"✅ SUCCESS: Applied to {job_info['title']}")

                # Longer delay after successful application
                self.log_info("Taking break after successful application...")
                time.sleep(random.uniform(15, 30))
            else:
                self.error_count += 1
                self.consecutive_errors += 1
                self.log_error(f"❌ FAILED: Could not apply to {job_info['title']}")

        except KeyboardInterrupt:
            raise
        except Exception as e:
            self.log_error(f"Error processing job {idx}: {str(e)}")
            self.error_count += 1
            self.consecutive_errors += 1

    def _find_easy_apply_button_careful(self):
        """CAREFULLY find Easy Apply button, avoiding popups and other elements"""
        self.log_info("🔍 Looking for Easy Apply button...")

        # First, dismiss any actual popups/overlays that might be blocking
        self._dismiss_blocking_elements()

        # Wait for page to stabilize
        time.sleep(2)

        # Look for Easy Apply button with VERY specific criteria
        easy_apply_selectors = [
            # Most specific LinkedIn Easy Apply selectors
            "button.jobs-apply-button[aria-label*='Easy Apply']",
            "button[data-control-name='jobdetails_topcard_inapply']",
            ".jobs-unified-top-card__content button[aria-label*='Easy Apply']",
            "div.jobs-apply-button button[aria-label*='Easy Apply']",
        ]

        for selector in easy_apply_selectors:
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    if self._is_valid_easy_apply_button(button):
                        self.log_info(
                            f"✅ Found Easy Apply button: {button.get_attribute('aria-label')}"
                        )
                        return button
            except Exception as e:
                continue

        # Try XPath with very specific criteria
        try:
            xpath = "//button[contains(@aria-label, 'Easy Apply') and not(contains(@class, 'modal')) and not(contains(@class, 'overlay'))]"
            buttons = self.driver.find_elements(By.XPATH, xpath)
            for button in buttons:
                if self._is_valid_easy_apply_button(button):
                    self.log_info(f"✅ Found Easy Apply button via XPath")
                    return button
        except:
            pass

        self.log_info("❌ No Easy Apply button found")
        return None

    def _is_valid_easy_apply_button(self, button):
        """Validate that this is actually an Easy Apply button and not a popup element"""
        try:
            # Must be displayed and enabled
            if not button.is_displayed() or not button.is_enabled():
                return False

            # Check text content
            button_text = button.text.lower().strip()
            aria_label = (button.get_attribute("aria-label") or "").lower()

            # Must contain "easy apply" or just "apply"
            has_apply_text = any(
                [
                    "easy apply" in button_text,
                    "easy apply" in aria_label,
                    (button_text == "apply" and "easy apply" in aria_label),
                ]
            )

            if not has_apply_text:
                return False

            # Must NOT be a popup/modal/overlay element
            button_classes = button.get_attribute("class") or ""
            parent_classes = ""
            try:
                parent = button.find_element(By.XPATH, "./..")
                parent_classes = parent.get_attribute("class") or ""
            except:
                pass

            # Exclude popup/modal elements
            excluded_terms = [
                "modal",
                "overlay",
                "popup",
                "dismiss",
                "close",
                "toast",
                "notification",
            ]
            all_classes = (button_classes + " " + parent_classes).lower()

            for term in excluded_terms:
                if term in all_classes:
                    self.log_info(f"Excluding button due to class: {term}")
                    return False

            # Check position - Easy Apply should be in the main content area
            try:
                location = button.location
                size = button.size
                # Should be reasonably positioned (not at very edges)
                if location["x"] < 100 or location["y"] < 100:
                    return False
            except:
                pass

            return True

        except Exception as e:
            return False

    def _dismiss_blocking_elements(self):
        """Dismiss actual blocking popups/overlays, not Easy Apply buttons"""
        self.log_info("Checking for blocking popups...")

        # Specific selectors for LinkedIn popups/overlays that should be dismissed
        blocking_selectors = [
            "button[aria-label='Dismiss']",
            "button[data-test-modal-close-btn]",
            ".msg-overlay-bubble-header__control--close",
            "button[aria-label*='Close']",
            ".artdeco-modal__dismiss",
            "button[data-control-name='overlay.close_overlay']",
        ]

        dismissed_count = 0
        for selector in blocking_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        # Double check this is not an Easy Apply button
                        aria_label = (element.get_attribute("aria-label") or "").lower()
                        if (
                            "easy apply" not in aria_label
                            and "apply" not in element.text.lower()
                        ):
                            click_element_safely(self.driver, element)
                            dismissed_count += 1
                            time.sleep(0.5)
            except:
                continue

        if dismissed_count > 0:
            self.log_info(f"Dismissed {dismissed_count} blocking popups")
            time.sleep(1)

    def _handle_easy_apply_careful(self, apply_button, job_info):
        """Handle Easy Apply process with careful step-by-step progression"""
        try:
            self.log_info(f"\n🚀 STARTING EASY APPLY FOR: {job_info['title']}")
            self.log_info(f"Company: {job_info['company']}")

            # Scroll to button and click
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", apply_button
            )
            time.sleep(2)

            self.log_info("Clicking Easy Apply button...")
            if not click_element_safely(self.driver, apply_button):
                self.log_error("Could not click Easy Apply button")
                return False

            # Wait for application form/modal to load
            self.log_info("Waiting for application form to load...")
            time.sleep(random.uniform(3, 5))

            # Process application step by step
            step_count = 0
            max_steps = 8  # Reasonable limit

            while step_count < max_steps:
                step_count += 1
                self.log_info(f"\n--- STEP {step_count} ---")

                # Check if we're done
                if self._is_application_complete():
                    self.log_info("🎉 APPLICATION COMPLETED SUCCESSFULLY!")
                    return True

                # Check for errors
                if self._check_for_application_errors():
                    self.log_error("Application form has errors")
                    self._ask_user_for_help("Form has errors. Check manually?")
                    return False

                # Fill current step
                self.log_info("Filling current step...")
                filled_fields = self._fill_application_form_step()
                if filled_fields:
                    self.log_info(f"✅ Filled form fields in step {step_count}")
                else:
                    self.log_info(f"ℹ️  No fillable fields found in step {step_count}")

                # Try to proceed to next step
                self.log_info("Looking for Next/Submit button...")
                if self._proceed_to_next_step_careful():
                    self.log_info("✅ Proceeded to next step")
                    # Wait for next step to load
                    time.sleep(random.uniform(2, 4))
                else:
                    self.log_error("❌ Could not find Next/Submit button")

                    # Ask user for help
                    if self._ask_user_for_help(
                        f"Stuck on step {step_count}. Check manually?"
                    ):
                        continue  # User said they handled it
                    else:
                        return False  # User wants to skip

                # Safety check for infinite loops
                if step_count >= 5:
                    self.log_info("⚠️  Many steps completed, checking for completion...")

            self.log_error(f"Reached maximum steps ({max_steps}) without completion")
            return False

        except Exception as e:
            self.log_error(f"Easy Apply error: {str(e)}")
            return False

    def _fill_current_step_careful(self):
        """Fill the current step carefully, field by field"""
        filled_count = 0

        # Text inputs
        inputs = self.driver.find_elements(
            By.CSS_SELECTOR,
            "input[type='text'], input[type='email'], input[type='tel'], textarea",
        )
        for input_elem in inputs:
            if (
                input_elem.is_displayed()
                and input_elem.is_enabled()
                and not input_elem.get_attribute("value")
            ):
                field_info = self._get_field_context(input_elem)
                answer = self._get_answer_for_field(field_info)
                if answer:
                    self.log_info(f"Filling field '{field_info}' with '{answer}'")
                    input_elem.clear()
                    self._type_human_like(input_elem, str(answer))
                    filled_count += 1
                    time.sleep(1)

        # Dropdowns
        selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
        for select_elem in selects:
            if select_elem.is_displayed() and select_elem.is_enabled():
                field_info = self._get_field_context(select_elem)
                answer = self._get_answer_for_field(field_info)
                if answer:
                    try:
                        select = Select(select_elem)
                        select.select_by_visible_text(str(answer))
                        self.log_info(
                            f"Selected '{answer}' for dropdown '{field_info}'"
                        )
                        filled_count += 1
                    except:
                        pass

        # File uploads
        file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
        for file_input in file_inputs:
            if file_input.is_displayed() and not file_input.get_attribute("value"):
                resume_path = self.get("resume_path")
                if resume_path and os.path.exists(resume_path):
                    file_input.send_keys(resume_path)
                    self.log_info("Uploaded resume")
                    filled_count += 1
                    time.sleep(2)

        return filled_count > 0

    def _proceed_to_next_step_careful(self):
        """Carefully find and click the next/submit button"""
        # Button priorities (most to least important)
        button_priorities = [
            ("Submit application", True),
            ("Submit", True),
            ("Send application", True),
            ("Review", False),
            ("Next", False),
            ("Continue", False),
        ]

        for button_text, is_final in button_priorities:
            if self._try_click_specific_button(button_text):
                if is_final:
                    self.log_info(f"Clicked final submit button: {button_text}")
                    time.sleep(3)  # Wait longer for submission
                else:
                    self.log_info(f"Clicked progression button: {button_text}")
                return True

        return False

    def _try_click_specific_button(self, text):
        """Try to click a button with specific text"""
        selectors = [
            f"button[aria-label*='{text}']",
            f"//button[contains(text(), '{text}')]",
            f"//button[contains(@aria-label, '{text}')]",
        ]

        for selector in selectors:
            try:
                if selector.startswith("//"):
                    buttons = self.driver.find_elements(By.XPATH, selector)
                else:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        if click_element_safely(self.driver, button):
                            return True
            except:
                continue

        return False

    def _ask_user_for_help(self, message):
        """Ask user to check manually when stuck"""
        if self.demo_mode:
            return True  # In demo mode, assume success

        print(f"\n⚠️  {message}")
        print("The browser window is open for you to check.")
        response = input("Did you handle it manually? (y/n): ").strip().lower()
        return response == "y"

    def _is_application_complete(self):
        """Check if application was successfully submitted"""
        success_indicators = [
            "application sent",
            "application submitted",
            "thank you for applying",
            "your application was sent",
            "application received",
        ]

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            for indicator in success_indicators:
                if indicator in page_text:
                    self.log_info(f"✅ Success indicator found: {indicator}")
                    return True
        except:
            pass

        return False

    def _check_for_application_errors(self):
        """Check for form validation errors"""
        error_indicators = [
            "required field",
            "please complete",
            "this field is required",
            "invalid entry",
        ]

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            for indicator in error_indicators:
                if indicator in page_text:
                    return True
        except:
            pass

        return False


def create_job_application_handler(config, logger=None):
    """Factory function to create job applicator instance."""
    return ModularJobApplicator(config, logger)
