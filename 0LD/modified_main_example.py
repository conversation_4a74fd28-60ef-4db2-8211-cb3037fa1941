#!/usr/bin/env python3
"""
Example showing how to modify the main LinkedIn automation script
to use enhanced logging and configuration
"""

# Add these imports at the top of your existing script
from enhanced_logging import EnhancedLogger, JobApplication
from enhanced_config import LinkedInConfig, ConfigManager
from integration_adapter import (
    LoggerAdapter, ConfigAdapter, EnhancedDataManager, 
    ModifiedBrowserManager
)

# Example of modified JobApplicationManager method
class ModifiedJobApplicationManager:
    """Example of how to modify existing JobApplicationManager"""
    
    def __init__(self, browser_manager, form_filler, data_manager, 
                 stats_manager, logger, enhanced_logger):
        self.browser = browser_manager
        self.form_filler = form_filler
        self.data = data_manager
        self.stats = stats_manager
        self.logger = logger
        self.enhanced_logger = enhanced_logger  # Keep reference to enhanced logger
        
    def _process_single_job(self, job_card, resume_path: str, applied_urls: set):
        """Modified to capture more details"""
        try:
            # ... existing code to click job and check if applied ...
            
            # Extract job details
            job_title = self._get_job_title()
            company = self._get_company_name()
            job_url = self._get_job_url()
            job_description = self._extract_job_description()
            
            # ... existing code to click Easy Apply ...
            
            # Fill form and get cover letter if generated
            cover_letter = ""
            if hasattr(self.form_filler, 'last_cover_letter'):
                cover_letter = self.form_filler.last_cover_letter
                
            # ... existing code to submit application ...
            
            # Enhanced logging with full details
            self.data.log_application(
                job_title=job_title,
                company=company,
                job_url=job_url,
                resume_used=resume_path,
                status='submitted',
                job_description=job_description,
                cover_letter=cover_letter
            )
            
            self.enhanced_logger.info(
                f"✓ Applied to {job_title} at {company} - "
                f"Description: {len(job_description)} chars"
            )
            
        except Exception as e:
            # Enhanced error logging with context
            self.enhanced_logger.log_error(
                str(e),
                error_type="application_error",
                context={
                    "job_url": job_url if 'job_url' in locals() else "unknown",
                    "job_title": job_title if 'job_title' in locals() else "unknown",
                    "company": company if 'company' in locals() else "unknown",
                    "step": "process_single_job"
                }
            )
            

# Example of modified CLI class
class ModifiedLinkedInAutomationCLI:
    """Example of modified CLI with enhanced features"""
    
    def __init__(self):
        # Initialize enhanced components
        self.enhanced_config = LinkedInConfig.load()
        self.enhanced_logger = EnhancedLogger()
        
        # Create adapters for compatibility
        self.config = ConfigAdapter(self.enhanced_config)
        self.logger = LoggerAdapter(self.enhanced_logger)
        
        # Use enhanced data manager
        self.data_manager = EnhancedDataManager(
            self.enhanced_config, 
            self.enhanced_logger
        )
        
        # Enhanced stats manager can track more metrics
        self.stats_manager = StatsManager(self.config)
        
    def run(self):
        """Main entry point with run tracking"""
        # Start run tracking
        self.enhanced_logger.start_run()
        
        try:
            # ... existing menu/mode selection code ...
            self.menu_mode()
            
        finally:
            # End run tracking and save summary
            self.enhanced_logger.end_run()
            
            # Show enhanced statistics
            self.show_enhanced_stats()
            
    def show_enhanced_stats(self):
        """Show detailed statistics from enhanced logger"""
        print("\n" + "="*60)
        print("ENHANCED RUN SUMMARY")
        print("="*60)
        
        summary = self.enhanced_logger.get_run_summary()
        print(f"Run ID: {summary['run_id']}")
        print(f"Duration: {summary.get('duration_formatted', 'N/A')}")
        print(f"Jobs Applied: {summary['jobs_applied']}")
        print(f"Jobs Skipped: {summary['jobs_skipped']}")
        print(f"Errors: {summary['errors']}")
        print(f"Network Actions: {summary['network_actions']}")
        
        # Show recent applications
        print("\nRecent Applications:")
        apps = self.enhanced_logger.get_recent_applications(5)
        for app in apps:
            print(f"- {app['job_title']} at {app['company']} ({app['applied_date']})")
            
    def view_enhanced_logs(self):
        """New menu option to view enhanced logs"""
        while True:
            print("\n" + "="*50)
            print("ENHANCED LOGS VIEWER")
            print("="*50)
            print("1. View Recent Applications")
            print("2. Search Applications by Company")
            print("3. Search Applications by Job Title")
            print("4. View Error Log")
            print("5. View Network Actions")
            print("6. Export Logs")
            print("7. Back to Main Menu")
            print("="*50)
            
            choice = input("Choose an option (1-7): ")
            
            if choice == '1':
                self.view_recent_applications()
            elif choice == '2':
                company = input("Enter company name to search: ")
                self.search_applications(company=company)
            elif choice == '3':
                title = input("Enter job title to search: ")
                self.search_applications(job_title=title)
            elif choice == '4':
                self.view_error_log()
            elif choice == '5':
                self.view_network_actions()
            elif choice == '6':
                self.export_logs()
            elif choice == '7':
                break
                
    def view_recent_applications(self):
        """View recent applications with details"""
        apps = self.enhanced_logger.get_recent_applications(20)
        
        if not apps:
            print("No applications found.")
            return
            
        print(f"\n{'Date':<20} {'Job Title':<30} {'Company':<25} {'Status':<10}")
        print("-" * 85)
        
        for app in apps:
            date = app['applied_date'][:19]  # Trim to datetime
            title = app['job_title'][:29]
            company = app['company'][:24]
            status = app['status'][:9]
            print(f"{date:<20} {title:<30} {company:<25} {status:<10}")
            
        input("\nPress Enter to continue...")
        
    def configure_enhanced_settings(self):
        """Configure enhanced settings through menu"""
        manager = ConfigManager()
        
        while True:
            print("\n" + "="*50)
            print("ENHANCED CONFIGURATION")
            print("="*50)
            print("1. Configure Search Settings")
            print("2. Configure Delays/Timing")
            print("3. Configure AI Settings")
            print("4. Configure Auto-Reply")
            print("5. Configure Network Settings")
            print("6. Test Configuration")
            print("7. Back to Main Menu")
            print("="*50)
            
            choice = input("Choose an option (1-7): ")
            
            if choice == '1':
                self.configure_search_settings(manager)
            elif choice == '2':
                self.configure_delays(manager)
            elif choice == '3':
                self.configure_ai_settings(manager)
            elif choice == '4':
                self.configure_auto_reply(manager)
            elif choice == '5':
                self.configure_network_settings(manager)
            elif choice == '6':
                self.test_configuration()
            elif choice == '7':
                break
                
    def configure_search_settings(self, manager: ConfigManager):
        """Configure job search settings"""
        config = manager.config
        
        print("\nCurrent Search Keywords:")
        for i, keyword in enumerate(config.search.keywords, 1):
            print(f"{i}. {keyword}")
            
        action = input("\n(A)dd keyword, (R)emove keyword, or (B)ack: ").lower()
        
        if action == 'a':
            keyword = input("Enter new keyword: ")
            manager.add_search_keyword(keyword)
            print(f"Added '{keyword}'")
        elif action == 'r':
            idx = int(input("Enter number to remove: ")) - 1
            if 0 <= idx < len(config.search.keywords):
                removed = config.search.keywords.pop(idx)
                manager.save()
                print(f"Removed '{removed}'")
                
    def test_configuration(self):
        """Test configuration validity"""
        issues = self.enhanced_config.validate()
        
        if not issues:
            print("\n✓ Configuration is valid!")
        else:
            print("\n⚠ Configuration issues found:")
            for issue in issues:
                print(f"  - {issue}")
                
        input("\nPress Enter to continue...")


# Example usage
if __name__ == "__main__":
    # Example of using enhanced logger directly
    logger = EnhancedLogger()
    logger.start_run()
    
    # Log a sample application
    job = JobApplication(
        job_id="123456",
        job_title="Senior Python Developer",
        company="Tech Corp",
        location="Remote",
        job_url="https://linkedin.com/jobs/123456",
        job_description_summary="Looking for experienced Python developer...",
        full_description="Full job description here...",
        resume_used="/path/to/resume.pdf",
        cover_letter="Dear Tech Corp...",
        applied_date=datetime.now().isoformat(),
        status="submitted",
        skills_matched=["Python", "Django", "PostgreSQL"]
    )
    
    logger.log_job_application(job)
    logger.end_run()
    
    print("Example run completed. Check linkedin_logs/ directory for output.")