#!/usr/bin/env python3
"""
Web Dashboard Backend Module for LinkedIn Automation
Provides API endpoints and real-time monitoring for the web dashboard
"""
import os
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

# Import your main modules
from enhanced_logging import EnhancedLogger
from enhanced_config import LinkedInConfig
from network_maintenance_module import NetworkMaintainer
from article_publishing_module import ContentCreator
from message_autoreply_module import MessageAutoResponder

class DashboardAPI:
    """API backend for the web dashboard"""
    
    def __init__(self):
        self.app = FastAPI(title="LinkedIn Automation API")
        self.config = LinkedInConfig.load()
        self.logger = EnhancedLogger(self.config)
        self.automation_running = False
        self.current_task = None
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_routes()
        
    def _setup_routes(self):
        """Setup all API routes"""
        
        @self.app.get("/")
        async def get_dashboard_stats():
            """Get current dashboard statistics"""
            return self.get_current_stats()
        
        @self.app.get("/applications")
        async def get_applications(limit: int = 50, offset: int = 0):
            """Get job applications with pagination"""
            return self.get_applications_data(limit, offset)
        
        @self.app.get("/network")
        async def get_network_stats():
            """Get network statistics"""
            return self.get_network_data()
        
        @self.app.get("/messages")
        async def get_messages():
            """Get message statistics"""
            return self.get_messages_data()
        
        @self.app.get("/content")
        async def get_content_stats():
            """Get content publishing statistics"""
            return self.get_content_data()
        
        @self.app.get("/config")
        async def get_config():
            """Get current configuration"""
            return self.get_config_summary()
        
        @self.app.post("/config")
        async def update_config(config_update: Dict[str, Any]):
            """Update configuration"""
            return self.update_configuration(config_update)
        
        @self.app.post("/start")
        async def start_automation(background_tasks: BackgroundTasks):
            """Start automation process"""
            if not self.automation_running:
                background_tasks.add_task(self.run_automation)
                return {"status": "started", "message": "Automation started successfully"}
            return {"status": "already_running", "message": "Automation is already running"}
        
        @self.app.post("/stop")
        async def stop_automation():
            """Stop automation process"""
            self.automation_running = False
            if self.current_task:
                self.current_task.cancel()
            return {"status": "stopped", "message": "Automation stopped"}
        
        @self.app.get("/status")
        async def get_status():
            """Get automation status"""
            return {
                "running": self.automation_running,
                "current_activity": self.get_current_activity(),
                "uptime": self.get_uptime()
            }
        
        @self.app.get("/logs")
        async def get_logs(log_type: str = "main", lines: int = 100):
            """Get recent logs"""
            return self.get_recent_logs(log_type, lines)
        
        @self.app.get("/analytics")
        async def get_analytics(period: str = "week"):
            """Get analytics data"""
            return self.get_analytics_data(period)
        
        @self.app.post("/manual/apply")
        async def manual_apply(job_url: str):
            """Manually trigger job application"""
            return await self.apply_to_job(job_url)
        
        @self.app.post("/manual/message")
        async def send_message(recipient: str, message: str):
            """Manually send a message"""
            return await self.send_manual_message(recipient, message)
        
        @self.app.get("/dashboard")
        async def serve_dashboard():
            """Serve the dashboard HTML"""
            return HTMLResponse(content=self.get_dashboard_html())
        
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        stats_file = Path(self.config.paths.data_directory) / "dashboard_data.json"
        
        if stats_file.exists():
            with open(stats_file, 'r') as f:
                stats = json.load(f)
        else:
            stats = {
                "applied": 0,
                "skipped": 0,
                "errors": 0,
                "jobs_processed": 0,
                "network_stats": {
                    "actions_accepted": 0,
                    "actions_sent": 0,
                    "attempts": 0
                }
            }
            
        # Add calculated metrics
        stats["success_rate"] = (
            (stats["applied"] / stats["jobs_processed"] * 100) 
            if stats["jobs_processed"] > 0 else 0
        )
        
        # Add time-based metrics
        stats["today"] = self.get_today_stats()
        stats["this_week"] = self.get_week_stats()
        
        return stats
        
    def get_applications_data(self, limit: int, offset: int) -> Dict[str, Any]:
        """Get applications with pagination"""
        applications = []
        total = 0
        
        # Get all application files
        log_dir = Path(self.config.paths.log_directory)
        
        for run_dir in sorted(log_dir.iterdir(), reverse=True):
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        applications.extend(run_apps)
                        
        total = len(applications)
        
        # Apply pagination
        paginated = applications[offset:offset + limit]
        
        return {
            "applications": paginated,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
        
    def get_network_data(self) -> Dict[str, Any]:
        """Get network statistics and recent actions"""
        network_file = Path(self.config.paths.data_directory) / "network_actions.json"
        
        actions = []
        if network_file.exists():
            with open(network_file, 'r') as f:
                actions = json.load(f)
                
        # Calculate statistics
        stats = {
            "total_connections": len([a for a in actions if a["action_type"] == "connect"]),
            "invitations_accepted": len([a for a in actions if a["action_type"] == "accept"]),
            "messages_sent": len([a for a in actions if a["action_type"].startswith("message_")]),
            "recent_actions": actions[-20:],  # Last 20 actions
            "growth_rate": self.calculate_growth_rate(actions)
        }
        
        return stats
        
    def get_messages_data(self) -> Dict[str, Any]:
        """Get message statistics"""
        messages_file = Path(self.config.paths.data_directory) / "processed_conversations.json"
        
        if messages_file.exists():
            with open(messages_file, 'r') as f:
                data = json.load(f)
        else:
            data = {"conversations": []}
            
        # Get response tracking
        tracking_file = Path(self.config.paths.data_directory) / "response_tracking.json"
        tracking_data = {"responses": []}
        
        if tracking_file.exists():
            with open(tracking_file, 'r') as f:
                tracking_data = json.load(f)
                
        # Calculate metrics
        total_responses = len(tracking_data["responses"])
        got_replies = len([r for r in tracking_data["responses"] if r.get("got_reply")])
        
        return {
            "total_conversations": len(data["conversations"]),
            "auto_responses_sent": total_responses,
            "response_rate": (got_replies / total_responses * 100) if total_responses > 0 else 0,
            "job_inquiries": len([r for r in tracking_data["responses"] if r["response_type"] == "job_inquiry"]),
            "last_check": data.get("last_updated", "Never")
        }
        
    def get_content_data(self) -> Dict[str, Any]:
        """Get content publishing statistics"""
        content_file = Path(self.config.paths.log_directory) / "published_content.json"
        performance_file = Path(self.config.paths.data_directory) / "content_performance.json"
        
        published = []
        if content_file.exists():
            with open(content_file, 'r') as f:
                published = json.load(f)
                
        performance = []
        if performance_file.exists():
            with open(performance_file, 'r') as f:
                performance = json.load(f)
                
        # Calculate metrics
        total_posts = len(published)
        posts_by_type = {}
        
        for post in published:
            post_type = post.get("content_type", "post")
            posts_by_type[post_type] = posts_by_type.get(post_type, 0) + 1
            
        return {
            "total_posts": total_posts,
            "posts_by_type": posts_by_type,
            "recent_posts": published[-10:],
            "performance_metrics": performance[-5:],
            "best_performing": self.get_best_performing_content(performance)
        }
        
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        return {
            "username": self.config.username,
            "has_password": bool(self.config.password),
            "has_api_key": bool(self.config.ai.openai_api_key),
            "search_keywords": self.config.search.keywords,
            "easy_apply_only": self.config.search.easy_apply_only,
            "auto_reply_enabled": self.config.auto_reply.enabled,
            "browser": self.config.browser.browser_type,
            "max_applications": self.config.application.max_applications_per_run,
            "max_connections": self.config.network.max_connections_per_run,
            "delays": {
                "page_load": self.config.delays.page_load,
                "between_applications": self.config.delays.between_applications
            }
        }
        
    def update_configuration(self, config_update: Dict[str, Any]) -> Dict[str, str]:
        """Update configuration settings"""
        try:
            # Update search keywords
            if "search_keywords" in config_update:
                self.config.search.keywords = config_update["search_keywords"]
                
            # Update limits
            if "max_applications" in config_update:
                self.config.application.max_applications_per_run = config_update["max_applications"]
                
            if "max_connections" in config_update:
                self.config.network.max_connections_per_run = config_update["max_connections"]
                
            if "auto_reply_enabled" in config_update:
                self.config.auto_reply.enabled = config_update["auto_reply_enabled"]
                
            # Save configuration
            self.config.save()
            
            return {"status": "success", "message": "Configuration updated"}
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
            
    async def run_automation(self):
        """Main automation loop"""
        self.automation_running = True
        self.logger.start_run()
        
        try:
            while self.automation_running:
                # Job applications
                if self.config.application.auto_use_defaults:
                    await self.run_job_applications()
                    
                # Network maintenance
                await self.run_network_maintenance()
                
                # Message checking
                if self.config.auto_reply.enabled:
                    await self.check_messages()
                    
                # Content creation (if scheduled)
                await self.check_content_schedule()
                
                # Wait before next cycle
                await asyncio.sleep(self.config.auto_reply.check_interval_minutes * 60)
                
        except asyncio.CancelledError:
            self.logger.info("Automation cancelled")
        finally:
            self.automation_running = False
            self.logger.end_run()
            
    def get_today_stats(self) -> Dict[str, int]:
        """Get today's statistics"""
        today = datetime.now().date()
        stats = {"applications": 0, "connections": 0, "messages": 0}
        
        # Count today's activities
        # This would need to parse logs with timestamps
        
        return stats
        
    def get_week_stats(self) -> Dict[str, Any]:
        """Get this week's statistics"""
        week_start = datetime.now() - timedelta(days=datetime.now().weekday())
        week_stats = {
            "applications": 0,
            "connections": 0,
            "messages": 0,
            "content_posts": 0
        }
        
        # Parse logs for this week's activities
        # This would aggregate data from the current week
        
        return week_stats
        
    def calculate_growth_rate(self, actions: List[Dict]) -> float:
        """Calculate network growth rate"""
        if not actions:
            return 0.0
            
        # Group actions by week
        weeks = {}
        for action in actions:
            date = datetime.fromisoformat(action["timestamp"])
            week = date.isocalendar()[1]
            weeks[week] = weeks.get(week, 0) + 1
            
        # Calculate average growth
        if len(weeks) > 1:
            values = list(weeks.values())
            growth = (values[-1] - values[-2]) / values[-2] * 100 if values[-2] > 0 else 0
            return round(growth, 2)
            
        return 0.0
        
    def get_best_performing_content(self, performance: List[Dict]) -> Dict[str, Any]:
        """Get best performing content"""
        if not performance:
            return {}
            
        # Sort by engagement (simplified - would use actual metrics)
        best = max(performance, key=lambda x: 
                  x.get("initial_metrics", {}).get("views", 0) +
                  x.get("initial_metrics", {}).get("likes", 0) * 10 +
                  x.get("initial_metrics", {}).get("comments", 0) * 20)
        
        return best
        
    def get_current_activity(self) -> str:
        """Get current automation activity"""
        if not self.automation_running:
            return "Idle"
            
        # Check recent logs for current activity
        return "Processing applications"
        
    def get_uptime(self) -> str:
        """Get automation uptime"""
        if hasattr(self, 'start_time') and self.automation_running:
            uptime = datetime.now() - self.start_time
            hours = int(uptime.total_seconds() // 3600)
            minutes = int((uptime.total_seconds() % 3600) // 60)
            return f"{hours}h {minutes}m"
        return "0h 0m"
        
    def get_recent_logs(self, log_type: str, lines: int) -> List[str]:
        """Get recent log entries"""
        log_file = Path(self.config.paths.log_directory) / f"{log_type}.log"
        
        if not log_file.exists():
            return []
            
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            return all_lines[-lines:]
            
    def get_analytics_data(self, period: str) -> Dict[str, Any]:
        """Get analytics data for specified period"""
        # Calculate date range
        end_date = datetime.now()
        if period == "day":
            start_date = end_date - timedelta(days=1)
        elif period == "week":
            start_date = end_date - timedelta(weeks=1)
        elif period == "month":
            start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=7)
            
        # Aggregate data for period
        analytics = {
            "period": period,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "applications": self.get_period_applications(start_date, end_date),
            "network_growth": self.get_period_network_growth(start_date, end_date),
            "message_stats": self.get_period_messages(start_date, end_date),
            "content_performance": self.get_period_content(start_date, end_date)
        }
        
        return analytics
        
    def get_period_applications(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get applications for a specific period"""
        applications = []
        log_dir = Path(self.config.paths.log_directory)
        
        for run_dir in log_dir.iterdir():
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        for app in run_apps:
                            app_date = datetime.fromisoformat(app["applied_date"])
                            if start_date <= app_date <= end_date:
                                applications.append(app)
                                
        # Group by day for chart
        daily_counts = {}
        for app in applications:
            date = app["applied_date"][:10]
            daily_counts[date] = daily_counts.get(date, 0) + 1
            
        return {
            "total": len(applications),
            "daily_breakdown": daily_counts,
            "by_company": self.group_by_field(applications, "company"),
            "by_position": self.group_by_field(applications, "job_title")
        }
        
    def group_by_field(self, items: List[Dict], field: str) -> Dict[str, int]:
        """Group items by a specific field"""
        grouped = {}
        for item in items:
            value = item.get(field, "Unknown")
            grouped[value] = grouped.get(value, 0) + 1
        return dict(sorted(grouped.items(), key=lambda x: x[1], reverse=True)[:10])
        
    def get_period_network_growth(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get network growth for period"""
        # This would analyze network actions within the date range
        return {
            "new_connections": 0,
            "invitations_sent": 0,
            "invitations_accepted": 0
        }
        
    def get_period_messages(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get message stats for period"""
        return {
            "messages_received": 0,
            "auto_replies_sent": 0,
            "job_inquiries": 0
        }
        
    def get_period_content(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get content performance for period"""
        return {
            "posts_published": 0,
            "total_engagement": 0,
            "best_post": {}
        }
        
    async def apply_to_job(self, job_url: str) -> Dict[str, str]:
        """Manually apply to a specific job"""
        # This would trigger the job application process for a specific URL
        return {
            "status": "success",
            "message": f"Application submitted for {job_url}"
        }
        
    async def send_manual_message(self, recipient: str, message: str) -> Dict[str, str]:
        """Send a manual message"""
        return {
            "status": "success",
            "message": f"Message sent to {recipient}"
        }
        
    def get_dashboard_html(self) -> str:
        """Get the dashboard HTML content"""
        # In production, this would serve the actual HTML file
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>LinkedIn Automation Dashboard</title>
        </head>
        <body>
            <h1>Dashboard</h1>
            <p>Please use the separate dashboard HTML file</p>
        </body>
        </html>
        """
        
    async def run_job_applications(self):
        """Run job application automation"""
        # This would integrate with the main automation
        self.logger.info("Running job applications...")
        
    async def run_network_maintenance(self):
        """Run network maintenance"""
        self.logger.info("Running network maintenance...")
        
    async def check_messages(self):
        """Check and respond to messages"""
        self.logger.info("Checking messages...")
        
    async def check_content_schedule(self):
        """Check if content needs to be published"""
        self.logger.info("Checking content schedule...")


# WebSocket support for real-time updates
from fastapi import WebSocket, WebSocketDisconnect
from typing import Set

class ConnectionManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.add(websocket)
        
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        
    async def broadcast(self, message: dict):
        """Broadcast message to all connected clients"""
        for connection in self.active_connections.copy():
            try:
                await connection.send_json(message)
            except:
                # Remove dead connections
                self.active_connections.remove(connection)


def create_dashboard_app() -> FastAPI:
    """Create and configure the dashboard application"""
    dashboard = DashboardAPI()
    manager = ConnectionManager()
    
    @dashboard.app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await manager.connect(websocket)
        try:
            while True:
                # Send periodic updates
                await asyncio.sleep(5)
                stats = dashboard.get_current_stats()
                await websocket.send_json({
                    "type": "stats_update",
                    "data": stats
                })
        except WebSocketDisconnect:
            manager.disconnect(websocket)
            
    return dashboard.app


# CLI Integration
class DashboardServer:
    """Dashboard server for CLI integration"""
    
    def __init__(self, config: LinkedInConfig):
        self.config = config
        self.app = create_dashboard_app()
        
    def run(self, host: str = "127.0.0.1", port: int = 8000):
        """Run the dashboard server"""
        uvicorn.run(self.app, host=host, port=port)


# Standalone runner
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="LinkedIn Automation Dashboard")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    app = create_dashboard_app()
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.reload
    )