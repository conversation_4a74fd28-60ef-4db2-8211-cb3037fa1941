{"username": "", "password": "", "delays": {"page_load": 10.0, "between_applications": [2, 5], "form_fill_delay": 0.5, "click_delay": 0.3, "typing_delay": 0.1, "network_action_delay": [1, 3], "scroll_delay": 0.5, "modal_wait": 2.0}, "paths": {"resume_path": "", "cover_letter_template": "", "log_directory": "linkedin_logs", "screenshot_directory": "screenshots", "data_directory": "data"}, "search": {"keywords": ["web developer", ".NET Developer", "C# Developer", "Full Stack Developer", "Software Engineer"], "locations": ["Remote", "United States"], "experience_levels": ["Entry level", "Associate", "Mid-Senior level"], "job_types": ["Full-time", "Contract", "Part-time"], "date_posted": "Past month", "easy_apply_only": true}, "network": {"max_connections_per_run": 50, "max_invitations_accept": 100, "connection_message_templates": ["Hi {name}, I'd love to connect and expand our professional networks!", "Hello {name}, I came across your profile and would like to connect."], "birthday_message_template": "Happy Birthday {name}! Wishing you a fantastic day!", "congrats_message_template": "Congratulations on your new position, {name}!"}, "auto_reply": {"enabled": false, "check_interval_minutes": 30, "reply_templates": {"job_inquiry": "Thank you for reaching out about this opportunity. Could you please share the job description so I can provide you with a tailored resume?", "default": "Thank you for your message. I'll get back to you soon."}, "keywords_job_inquiry": ["opportunity", "position", "role", "job", "opening", "hiring"]}, "ai": {"openai_api_key": "", "model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 500, "use_ai_for_cover_letters": true, "use_ai_for_form_fields": true}, "browser": {"browser_type": "chrome", "headless": false, "window_size": [1920, 1080], "user_agent": null, "chrome_binary_path": "/usr/bin/chromium-browser", "chromedriver_path": "/usr/bin/chromedriver", "disable_images": false, "disable_javascript": false}, "application": {"auto_use_defaults": false, "skip_applied_jobs": true, "max_applications_per_run": 100, "save_job_descriptions": true, "take_screenshots_on_error": true, "pause_on_captcha": true}}