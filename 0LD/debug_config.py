#!/usr/bin/env python3
"""
Debug script to check config loading
"""

import json
import os

print("Config Debug Script")
print("=" * 50)

# Check if config file exists
config_file = 'linkedin_config.json'
if os.path.exists(config_file):
    print(f"✓ Config file exists: {config_file}")
    
    # Load and display key values
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    print(f"✓ Username: {config.get('username', 'NOT SET')}")
    print(f"✓ Password: {'*' * len(config.get('password', '')) if config.get('password') else 'NOT SET'}")
    print(f"✓ Resume: {config.get('resume_path', 'NOT SET')}")
    print(f"✓ Browser: {config.get('browser', 'NOT SET')}")
    
    # Check search config
    search = config.get('search', {})
    keywords = search.get('keywords', [])
    print(f"✓ Keywords: {', '.join(keywords[:3]) if keywords else 'NOT SET'}")
else:
    print(f"✗ Config file not found: {config_file}")

# Check what main.py is actually loading
print("\nChecking main.py config loading...")

try:
    # Try the imports main.py uses
    from enhanced_config import ConfigManager
    print("✓ ConfigManager imported successfully")
    
    try:
        config_manager = ConfigManager()
        print("✓ ConfigManager instantiated")
        
        if hasattr(config_manager, 'config'):
            cfg = config_manager.config
            print(f"✓ Config loaded via ConfigManager")
            
            # Check what's in the config
            if hasattr(cfg, 'username'):
                print(f"  - Username (from manager): {cfg.username}")
            elif hasattr(cfg, '__dict__'):
                print(f"  - Config attributes: {list(cfg.__dict__.keys())[:5]}")
    except Exception as e:
        print(f"✗ ConfigManager instantiation failed: {e}")
        
except ImportError as e:
    print(f"✗ Could not import ConfigManager: {e}")

print("\n" + "=" * 50)
print("Recommendation:")
if os.path.exists(config_file):
    print("Config file exists. The issue is likely in how main.py loads it.")
    print("The config might not be getting passed correctly to the modules.")
else:
    print("Create linkedin_config.json first!")