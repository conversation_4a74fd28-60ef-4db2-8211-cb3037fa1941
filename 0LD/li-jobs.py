#   !/usr/bin/env python3
"""
Integrated LinkedIn Automation Script with ChatGPT Integration
- Automates LinkedIn login and job application workflow
- Integrates with ChatGPT for intelligent form filling
- Modular architecture with separated concerns
"""
import logging
import json
import os
import time
import random
import getpass
import sys
import threading
import webbrowser
import requests
import uvicorn

from datetime import datetime
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
import time
from selenium.webdriver.common.by import By

from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException,
    ElementClickInterceptedException, StaleElementReferenceException,
    ElementNotInteractableException, WebDriverException, InvalidSessionIdException
)

from selenium.webdriver.common.action_chains import Action<PERSON>hains
from fastapi import FastAPI


# ===================== CONFIGURATION =====================
@dataclass
class Config:
    """Application configuration management"""
    CONFIG_FILE = 'linkedin_config.json'
    APPLICATION_LOG = 'applications_log.json'
    QUESTIONS_LOG = 'questions_log.json'
    ERROR_LOG = 'error_log.txt'
    SKIPPED_LOG = 'skipped_log.json'
    OVERALL_STATS_FILE = 'overall_stats.json'
    DASHBOARD_FILE = 'dashboard_data.json'
    
    username: str = ""
    password: str = ""
    browser: str = "chrome"
    resume_path: str = ""
    openai_api_key: str = ""
    auto_use_defaults: bool = False
    
    @classmethod
    def load(cls) -> 'Config':
        """Load configuration from file"""
        if os.path.exists(cls.CONFIG_FILE):
            with open(cls.CONFIG_FILE, 'r') as f:
                data = json.load(f)
                return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})
        return cls()
    
    def save(self):
        """Save configuration to file"""
        data = {k: getattr(self, k) for k in self.__annotations__ 
                if not k.isupper() and not k.startswith('_')}
        with open(self.CONFIG_FILE, 'w') as f:
            json.dump(data, f, indent=4)

# ===================== LOGGING =====================
class Logger:
    """Centralized logging system"""
    def __init__(self, config: Config):
        self.config = config
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('LinkedInAutomation')
        
        # File handler for errors
        error_handler = logging.FileHandler(config.ERROR_LOG)
        error_handler.setLevel(logging.ERROR)
        self.logger.addHandler(error_handler)
    
    def info(self, message: str):
        self.logger.info(message)
        print(message)
    
    def error(self, message: str):
        self.logger.error(message)
        print(f"ERROR: {message}")
    
    def warning(self, message: str):
        self.logger.warning(message)
        print(f"WARNING: {message}")

# ===================== STATISTICS =====================
@dataclass
class Statistics:
    """Statistics tracking for current run"""
    applied: int = 0
    skipped: int = 0
    errors: int = 0
    jobs_processed: int = 0
    start_time: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'applied': self.applied,
            'skipped': self.skipped,
            'errors': self.errors,
            'jobs_processed': self.jobs_processed,
            'uptime': time.time() - self.start_time
        }
    
    def get_throughput(self) -> Dict[str, float]:
        uptime = time.time() - self.start_time
        if uptime > 0:
            return {
                'per_hour': self.applied / (uptime / 3600),
                'per_day': self.applied / (uptime / 86400)
            }
        return {'per_hour': 0.0, 'per_day': 0.0}

@dataclass
class NetworkStatistics:
    """Network expansion statistics"""
    actions_accepted: int = 0
    actions_sent: int = 0
    attempts: int = 0

class StatsManager:
    """Manages overall statistics across runs"""
    def __init__(self, config: Config):
        self.config = config
        self.current_stats = Statistics()
        self.network_stats = NetworkStatistics()
    
    def load_overall_stats(self) -> Dict[str, Any]:
        """Load cumulative stats from file"""
        if os.path.exists(self.config.OVERALL_STATS_FILE):
            with open(self.config.OVERALL_STATS_FILE, 'r') as f:
                return json.load(f)
        return {
            "run_count": 0,
            "applied": 0,
            "skipped": 0,
            "errors": 0,
            "jobs_processed": 0,
            "network": {
                "actions_accepted": 0,
                "actions_sent": 0,
                "attempts": 0
            },
            "last_run_timestamp": None
        }
    
    def update_overall_stats(self) -> Dict[str, Any]:
        """Update overall stats with current run data"""
        overall = self.load_overall_stats()
        overall["run_count"] += 1
        overall["applied"] += self.current_stats.applied
        overall["skipped"] += self.current_stats.skipped
        overall["errors"] += self.current_stats.errors
        overall["jobs_processed"] += self.current_stats.jobs_processed
        overall["network"]["actions_accepted"] += self.network_stats.actions_accepted
        overall["network"]["actions_sent"] += self.network_stats.actions_sent
        overall["network"]["attempts"] += self.network_stats.attempts
        overall["last_run_timestamp"] = datetime.now().isoformat()
        
        with open(self.config.OVERALL_STATS_FILE, "w") as f:
            json.dump(overall, f, indent=4)
        return overall
    
    def update_dashboard(self):
        """Update dashboard data file"""
        dashboard_data = {
            **self.current_stats.to_dict(),
            "network_stats": {
                "actions_accepted": self.network_stats.actions_accepted,
                "actions_sent": self.network_stats.actions_sent,
                "attempts": self.network_stats.attempts
            }
        }
        with open(self.config.DASHBOARD_FILE, "w") as f:
            json.dump(dashboard_data, f, indent=4)

# ===================== DATA PERSISTENCE =====================
class DataManager:
    """Handles all data persistence operations"""
    def __init__(self, config: Config):
        self.config = config
    
    def load_questions(self) -> Dict[str, str]:
        """Load saved question answers"""
        if os.path.exists(self.config.QUESTIONS_LOG):
            with open(self.config.QUESTIONS_LOG, 'r') as f:
                return json.load(f)
        return {}
    
    def save_questions(self, questions: Dict[str, str]):
        """Save question answers"""
        with open(self.config.QUESTIONS_LOG, 'w') as f:
            json.dump(questions, f, indent=4)
    
    def log_application(self, job_title: str, company: str, job_url: str, 
                       resume_used: str, status: str):
        """Log job application"""
        entry = {
            'job_title': job_title,
            'company': company,
            'job_url': job_url,
            'resume_used': resume_used,
            'status': status,
            'date_applied': datetime.now().isoformat()
        }
        
        logs = []
        if os.path.exists(self.config.APPLICATION_LOG):
            with open(self.config.APPLICATION_LOG, 'r') as f:
                logs = json.load(f)
        
        logs.append(entry)
        with open(self.config.APPLICATION_LOG, 'w') as f:
            json.dump(logs, f, indent=4)
    
    def get_applied_job_urls(self) -> Set[str]:
        """Get set of applied job URLs"""
        urls = set()
        if os.path.exists(self.config.APPLICATION_LOG):
            with open(self.config.APPLICATION_LOG, 'r') as f:
                logs = json.load(f)
                urls = {log['job_url'] for log in logs if log.get('job_url')}
        return urls
    
    def log_skipped(self, job_url: str, reason: str):
        """Log skipped job"""
        entry = {
            'job_url': job_url,
            'reason': reason,
            'date': datetime.now().isoformat()
        }
        
        skipped = []
        if os.path.exists(self.config.SKIPPED_LOG):
            with open(self.config.SKIPPED_LOG, 'r') as f:
                skipped = json.load(f)
        
        skipped.append(entry)
        with open(self.config.SKIPPED_LOG, 'w') as f:
            json.dump(skipped, f, indent=4)

# ===================== AI INTEGRATION =====================
class AIProvider(ABC):
    """Abstract base class for AI providers"""
    @abstractmethod
    def generate_answer(self, field_label: str, field_type: str, 
                       context: str = "") -> str:
        pass

class ChatGPTProvider(AIProvider):
    """OpenAI ChatGPT integration"""
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
    
    def generate_answer(self, field_label: str, field_type: str, 
                       context: str = "") -> str:
        """Generate answer using ChatGPT"""
        if not self.api_key:
            return f"Default answer for {field_label}"
        
        prompt = (
            f"Provide a concise, professional answer for the following LinkedIn job application field.\n"
            f"Field: {field_label}\nType: {field_type}\n"
        )
        if context:
            prompt += f"Context: {context}\n"
        prompt += "Answer:"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 200
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, 
                                   json=payload, timeout=15)
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"].strip()
            else:
                return f"Default answer for {field_label}"
        except Exception:
            return f"Default answer for {field_label}"

    def generate_cover_letter(self, job_title: str, company: str, job_description: str, resume_text: str = "") -> str:
        """Generate a professional cover letter using ChatGPT"""
        if not self.api_key:
            return f"Dear {company}, I am very interested in the {job_title} position."

        prompt = (
            f"Write a professional cover letter for the position of '{job_title}' at '{company}'.\n"
            f"Job Description:\n{job_description}\n\n"
        )
        if resume_text:
            prompt += f"Candidate Resume Excerpt:\n{resume_text}\n\n"
        prompt += "The letter should be tailored, concise, and compelling.\n\nCover Letter:"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 500
        }

        try:
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=20)
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"].strip()
            else:
                return f"Dear {company}, I am very interested in the {job_title} position."
        except Exception:
            return f"Dear {company}, I am very interested in the {job_title} position."


# ===================== BROWSER AUTOMATION =====================
import sys
import shutil
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, ElementClickInterceptedException,
    ElementNotInteractableException, StaleElementReferenceException
)

class BrowserManager:
    """Manages browser operations"""
    def __init__(self, config: Config, logger: Logger):
        self.config    = config
        self.logger    = logger
        self.driver    = None
        self.wait_time = 10

    def init_browser(self) -> webdriver.Remote:
        """Initialize browser driver pointing at Pop!_OS’s Chromium and its chromedriver."""
        browser_choice = self.config.browser.lower()

        if browser_choice == 'chrome':
            binary = "/usr/bin/chromium-browser"
            options = webdriver.ChromeOptions()
            options.binary_location = binary
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument('--disable-blink-features=AutomationControlled')
            self.logger.info(f"Using browser binary at {binary}")

            # Point at the system-installed chromedriver
            service = Service('/usr/bin/chromedriver')
            try:
                self.driver = webdriver.Chrome(service=service, options=options)
            except Exception as e:
                self.logger.error(f"Failed to launch ChromeDriver: {e}")
                sys.exit(1)

        elif browser_choice == 'edge':
            self.driver = webdriver.Edge()

        else:
            # fallback to default if someone really wants plain Chrome
            self.driver = webdriver.Chrome()

        self.driver.implicitly_wait(self.wait_time)
        return self.driver

    def _init_real_chromium(self) -> webdriver.Remote:
        """Initialize browser driver pointing at real Chromium"""
        browser_choice = self.config.browser.lower()

        if browser_choice == 'chrome':
            binary = "/usr/bin/chromium-browser"
            options = webdriver.ChromeOptions()
            options.binary_location = binary
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument('--disable-blink-features=AutomationControlled')
            self.logger.info(f"Using browser binary at {binary}")

            try:
                self.driver = webdriver.Chrome(options=options)
            except Exception as e:
                self.logger.error(f"Failed to launch ChromeDriver: {e}")
                sys.exit(1)

        elif browser_choice == 'edge':
            self.driver = webdriver.Edge()
        else:
            self.driver = webdriver.Chrome()

        self.driver.implicitly_wait(self.wait_time)
        return self.driver

    def login(self, username: str, password: str) -> bool:
        """Login to LinkedIn"""
        try:
            self.driver.get("https://www.linkedin.com/login")
            WebDriverWait(self.driver, self.wait_time).until(
                EC.presence_of_element_located((By.ID, 'username'))
            )
            self.driver.find_element(By.ID, 'username').send_keys(username)
            self.driver.find_element(By.ID, 'password').send_keys(password + Keys.RETURN)
            WebDriverWait(self.driver, 20).until(EC.url_contains('feed'))
            self.logger.info("Successfully logged in to LinkedIn")
            return True
        except TimeoutException:
            self.logger.error("Login failed: timeout")
            return False
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False

    def safe_click(self, element, retries: int = 3, delay: float = 1.0) -> bool:
        """Click an element with retries, scrolling into view if necessary"""
        for attempt in range(retries):
            try:
                element.click()
                return True
            except (ElementClickInterceptedException, ElementNotInteractableException):
                time.sleep(delay)
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            except StaleElementReferenceException:
                return False
            except Exception as e:
                self.logger.error(f"Click error on attempt {attempt+1}: {e}")
                time.sleep(delay)
        return False

    def take_screenshot(self, label: str = "error"):
        """Take and save screenshot"""
        if not self.driver:
            self.logger.error("Cannot take screenshot: no driver instance")
            return
        filename = f"screenshot_{label}_{datetime.now():%Y%m%d_%H%M%S}.png"
        self.driver.save_screenshot(filename)
        self.logger.info(f"Screenshot saved: {filename}")

    def quit(self):
        """Quit browser"""
        if self.driver:
            self.driver.quit()

"""
#OLD browser automation code
class BrowserManager:
    # Manages browser operations
    def __init__(self, config: Config, logger: Logger):
        self.config = config
        self.logger = logger
        self.driver = None
        self.wait_time = 10
    
    def init_browser(self) -> webdriver.Remote:
        #Initialize browser driver
        browser_choice = self.config.browser.lower()
        
        if browser_choice == 'chrome':
            options = webdriver.ChromeOptions()
            options.add_argument('--disable-blink-features=AutomationControlled')
            self.driver = webdriver.Chrome(options=options)
        elif browser_choice == 'edge':
            self.driver = webdriver.Edge()
        else:
            self.driver = webdriver.Chrome()
        
        self.driver.implicitly_wait(self.wait_time)
        return self.driver
    
    def login(self, username: str, password: str) -> bool:
        #Login to LinkedIn
        try:
            self.driver.get("https://www.linkedin.com/login")
            
            WebDriverWait(self.driver, self.wait_time).until(
                EC.presence_of_element_located((By.ID, 'username'))
            )
            
            self.driver.find_element(By.ID, 'username').send_keys(username)
            self.driver.find_element(By.ID, 'password').send_keys(password)
            self.driver.find_element(By.ID, 'password').send_keys(Keys.RETURN)
            
            WebDriverWait(self.driver, 20).until(EC.url_contains('feed'))
            self.logger.info("Successfully logged in to LinkedIn")
            return True
            
        except TimeoutException:
            self.logger.error("Login failed: timeout")
            return False
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
    
    def safe_click(self, element, retries: int = 3, delay: float = 2) -> bool:
        #afely click an element with retry logic
        for attempt in range(retries):
            try:
                element.click()
                return True
            except (ElementClickInterceptedException, 
                    ElementNotInteractableException):
                time.sleep(delay)
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            except StaleElementReferenceException:
                return False
            except Exception as e:
                self.logger.error(f"Click error: {e}")
                time.sleep(delay)
        return False
    
    def take_screenshot(self, label: str = "error"):
        #Take and save screenshot
        filename = f"screenshot_{label}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        self.driver.save_screenshot(filename)
        self.logger.info(f"Screenshot saved: {filename}")
    
    def quit(self):
        #Quit browser
        if self.driver:
            self.driver.quit()
"""
class FormFiller:
    """Handles form filling operations"""
    def __init__(self, browser_manager: BrowserManager, ai_provider: AIProvider, 
                 data_manager: DataManager, config: Config, logger: Logger):
        self.browser = browser_manager
        self.ai = ai_provider
        self.data = data_manager
        self.config = config
        self.logger = logger
    
    def fill_text_field(self, input_field, label: str, questions: Dict[str, str]) -> str:
        """Fill a text input field"""
        # Special handling for cover letters and resumes

        #if "cover" in label.lower() or "resume" in label.lower():
        #    answer = self.ai.generate_answer(label, "text")
        #
        # 
        if "cover" in label.lower():
            job_title = self._get_job_title()
            company = self._get_company_name()
            job_description = self._get_job_description_from_page()
            resume_excerpt = self._get_resume_excerpt()
            answer = self.ai.generate_cover_letter(job_title, company, job_description, resume_excerpt)
            self.logger.info(f"Generated cover letter for {job_title} at {company}")
            self.logger.info(f"AI generated answer for '{label}'")
        else:
            # Check for saved answer
            if label in questions and questions[label].strip():
                answer = questions[label].strip()
                self.logger.info(f"Using saved answer for '{label}'")
            else:
                # Check for prefilled value
                prefilled = input_field.get_attribute("value").strip()
                if prefilled:
                    if self.config.auto_use_defaults:
                        answer = prefilled
                        self.logger.info(f"Using prefilled value for '{label}'")
                    else:
                        use_prefilled = input(
                            f"Field '{label}' is prefilled with '{prefilled}'. "
                            f"Use this? (Y/n): "
                        ).strip().lower() or "y"
                        
                        if use_prefilled == "y":
                            answer = prefilled
                        else:
                            suggestion = self.ai.generate_answer(label, "text")
                            print(f"AI suggestion: {suggestion}")
                            answer = input(f"Enter answer for '{label}': ").strip() or suggestion
                else:
                    # No prefilled value, get AI suggestion
                    suggestion = self.ai.generate_answer(label, "text")
                    print(f"\nField: {label}")
                    print(f"AI suggestion: {suggestion}")
                    answer = input(f"Enter answer for '{label}': ").strip() or suggestion
            
            questions[label] = answer
        
        # Fill the field
        try:
            input_field.clear()
            input_field.send_keys(answer)
            
            # Handle autocomplete suggestions
            try:
                suggestion_elem = WebDriverWait(self.browser.driver, 3).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "li.artdeco-typeahead__hit"))
                )
                suggestion_elem.click()
                self.logger.info(f"Selected autocomplete suggestion for '{label}'")
            except TimeoutException:
                pass
                
        except ElementNotInteractableException:
            self.logger.warning(f"Field '{label}' is not interactable")
        
        return answer
    
    def fill_form(self):
        """Fill all form fields in the Easy Apply modal"""
        questions = self.data.load_questions()
        
        try:
            form_section = self.browser.driver.find_element(
                By.CSS_SELECTOR, "div.jobs-easy-apply-modal"
            )
        except NoSuchElementException:
            self.logger.warning("Easy Apply modal not found")
            return
        
        # Process text inputs
        text_inputs = form_section.find_elements(
            By.CSS_SELECTOR, 
            "input[type='text'], input[type='number'], input[type='tel'], input[type='email'], textarea"
        )
        
        for input_field in text_inputs:
            label = self._get_field_label(input_field)
            self.fill_text_field(input_field, label, questions)
        
        # Process dropdowns
        self._fill_dropdowns(form_section, questions)
        
        # Process checkboxes
        self._fill_checkboxes(form_section, questions)
        
        # Process radio buttons
        self._fill_radio_buttons(form_section, questions)
        
        # Save updated questions
        self.data.save_questions(questions)
 
    def _get_field_label(self, field) -> str:
        """Extract and normalize the label text for a field"""
        try:
            label_elem = field.find_element(
                By.XPATH, "./ancestor::div[label]//label"
            )
            return label_elem.text.strip().replace('\n', ' ').splitlines()[0]
        except NoSuchElementException:
            return "Unnamed field"
 
    def _fill_dropdowns(self, form_section, questions: Dict[str, str]):
        """Fill dropdown fields"""
        selects = form_section.find_elements(By.TAG_NAME, "select")
        
        for sel in selects:
            label = self._get_field_label(sel)
            select_obj = Select(sel)
            
            try:
                current_sel = select_obj.first_selected_option.text.strip()
            except Exception:
                current_sel = ""
            
            if label in questions and questions[label].strip():
                answer = questions[label].strip()
                self.logger.info(f"Using saved choice for '{label}': {answer}")
            elif current_sel and current_sel.lower() not in ["select an option", ""]:
                if self.config.auto_use_defaults:
                    answer = current_sel
                    self.logger.info(f"Using prefilled dropdown for '{label}'")
                else:
                    options = [o.text for o in select_obj.options if o.text.strip()]
                    answer = self._prompt_dropdown_choice(label, current_sel, options)
                questions[label] = answer
            else:
                options = [o.text for o in select_obj.options if o.text.strip()]
                answer = self._prompt_dropdown_choice(label, "", options)
                questions[label] = answer
            
            try:
                select_obj.select_by_visible_text(answer)
            except Exception:
                select_obj.select_by_index(0)
    
    def _prompt_dropdown_choice(self, label: str, current: str, 
                               options: List[str]) -> str:
        """Prompt user for dropdown choice"""
        if current:
            use_current = input(
                f"Dropdown '{label}' is set to '{current}'. Keep it? (Y/n): "
            ).strip().lower() or "y"
            if use_current == "y":
                return current
        
        print(f"\nDropdown: {label}")
        for idx, opt in enumerate(options):
            print(f"{idx}: {opt}")
        
        choice_str = input(f"Select option index: ").strip()
        try:
            return options[int(choice_str)]
        except (ValueError, IndexError):
            return options[0] if options else ""
    
    def _fill_checkboxes(self, form_section, questions: Dict[str, str]):
        """Fill checkbox fields"""
        checkboxes = form_section.find_elements(
            By.CSS_SELECTOR, "input[type='checkbox']"
        )
        
        for checkbox in checkboxes:
            label = self._get_field_label(checkbox)
            
            # Auto-check "follow" checkboxes
            if "follow" in label.lower():
                if not checkbox.is_selected():
                    self.browser.safe_click(checkbox)
                    self.logger.info(f"Auto-checked '{label}'")
                continue
            
            current = checkbox.is_selected()
            
            if label in questions:
                keep = (questions[label].lower() == 'true')
                self.logger.info(f"Using saved preference for '{label}': {keep}")
            else:
                if self.config.auto_use_defaults:
                    keep = current
                    self.logger.info(f"Keeping checkbox '{label}' as is")
                else:
                    if current:
                        keep = input(
                            f"Checkbox '{label}' is checked. Keep it? (Y/n): "
                        ).strip().lower() != "n"
                    else:
                        keep = input(
                            f"Checkbox '{label}' is unchecked. Check it? (Y/n): "
                        ).strip().lower() == "y"
                
                questions[label] = str(keep)
            
            if keep and not current:
                self.browser.safe_click(checkbox)
            elif not keep and current:
                self.browser.safe_click(checkbox)
    
    def _fill_radio_buttons(self, form_section, questions: Dict[str, str]):
        """Fill radio button fields"""
        radios = form_section.find_elements(
            By.CSS_SELECTOR, "input[type='radio']"
        )
        
        if not radios:
            return
        
        # Group radios by name
        radio_groups = {}
        for r in radios:
            name = r.get_attribute("name") or "radio-group"
            radio_groups.setdefault(name, []).append(r)
        
        for group_name, group_radios in radio_groups.items():
            labels_list = []
            preselected = None
            
            for idx, r in enumerate(group_radios):
                lbl = self._get_field_label(r)
                if lbl == "Unnamed field":
                    lbl = f"Option {idx + 1}"
                labels_list.append(lbl)
                if r.is_selected():
                    preselected = lbl
            
            if group_name in questions:
                chosen = questions[group_name]
                self.logger.info(f"Using saved choice for '{group_name}': {chosen}")
            elif preselected:
                chosen = preselected
                questions[group_name] = preselected
                self.logger.info(f"Using preselected radio: {preselected}")
            else:
                print(f"\nRadio group: {group_name}")
                for idx, lbl in enumerate(labels_list):
                    print(f"{idx}: {lbl}")
                
                idx_str = input("Select option index: ").strip()
                try:
                    chosen = labels_list[int(idx_str)]
                except (ValueError, IndexError):
                    chosen = labels_list[0] if labels_list else ""
                
                questions[group_name] = chosen
            
            # Click the chosen radio
            for r, lbl in zip(group_radios, labels_list):
                if lbl == chosen:
                    self.browser.safe_click(r)
                    break
    
    def _get_job_description_from_page(self) -> str:
        """Extract job description text from the LinkedIn job view"""
        try:
            desc_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-description__content"))
            )
            return desc_elem.text.strip()
        except Exception:
            self.logger.warning("Failed to extract job description.")
            return ""
        
    def _get_resume_excerpt(self, lines: int = 20) -> str:
        """Load the first N lines of the resume as plain text excerpt"""
        try:
            if not os.path.exists(self.config.resume_path):
                self.logger.warning("Resume file does not exist.")
                return ""

            with open(self.config.resume_path, "r", encoding="utf-8") as f:
                return "\n".join([next(f).strip() for _ in range(lines)])
        except Exception as e:
            self.logger.warning(f"Failed to read resume excerpt: {e}")
            return ""
   
    def _get_job_title(self) -> str:
        try:
            elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__job-title'
            )
            return elem.text.strip()
        except Exception:
            return "Unknown Job"

    def _get_company_name(self) -> str:
        try:
            elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__company-name'
            )
            return elem.text.strip()
        except Exception:
            return "Unknown Company"
        
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
class JobApplicationManager:
    """Manages the job application process"""
    def __init__(self, browser_manager: BrowserManager, form_filler: FormFiller,
                 data_manager: DataManager, stats_manager: StatsManager, 
                 logger: Logger):
        self.browser = browser_manager
        self.form_filler = form_filler
        self.data = data_manager
        self.stats = stats_manager
        self.logger = logger
        self.search_keywords = [
            "web developer", ".NET Developer", ".NET Engineer", "C# Developer",
            "Azure Developer", "Full Stack Developer", "Senior Software Engineer",
            "Application Developer", "Full Stack Engineer", "Information Technology Consultant"
        ]
    
    def get_applied_jobs_from_linkedin(self) -> Set[str]:
        """Retrieve applied jobs from LinkedIn using updated selectors"""
        applied_jobs = set()
        try:
            self.browser.driver.get("https://www.linkedin.com/my-items/saved-jobs/?cardType=APPLIED")
            WebDriverWait(self.browser.driver, 10).until(
                EC.presence_of_all_elements_located(
                    (By.CSS_SELECTOR, "ul.jobs-saved-jobs-list li a.job-card-list__title")
                )
            )
            jobs = self.browser.driver.find_elements(
                By.CSS_SELECTOR, "ul.jobs-saved-jobs-list li a.job-card-list__title"
            )
            for job in jobs:
                href = job.get_attribute("href")
                if href:
                    applied_jobs.add(href)
        except Exception as e:
            self.logger.error(f"Could not retrieve applied jobs: {e}")
        return applied_jobs


    def handle_application_popup(self):
        """Close the application confirmation popup"""
        popup_xpaths = [
            "//button[contains(text(), 'Done')]",
            "//button[@aria-label='Dismiss']",
            "button.artdeco-modal__dismiss"
        ]
        
        for xpath in popup_xpaths:
            try:
                button = WebDriverWait(self.browser.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, xpath))
                )
                button.click()
                self.logger.info("Closed application popup")
                break
            except TimeoutException:
                continue
        
        # Wait for modal to disappear
        try:
            WebDriverWait(self.browser.driver, 5).until(
                EC.invisibility_of_element_located(
                    (By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
                )
            )
        except TimeoutException:
            self.logger.warning("Modal did not close in time")
   
    def handle_multi_step_application(self, resume_path: str) -> bool:
        """Handle Easy Apply multi-step applications with resume and Review step support"""
        for step in range(6):
            self.logger.info(f"Step {step + 1} of Easy Apply")
            time.sleep(1)

            if step == 0:
                try:
                    upload = WebDriverWait(self.browser.driver, self.browser.wait_time).until(
                        EC.presence_of_element_located(
                            (By.CSS_SELECTOR, "div.jobs-easy-apply-modal input[type='file']")
                        )
                    )
                    if os.path.isfile(resume_path):
                        upload.send_keys(resume_path)
                        self.logger.info(f"Uploaded resume: {resume_path}")
                except Exception as e:
                    self.logger.warning(f"Resume upload skipped or failed: {e}")

            # Insert Review step
            if self._click_review_button():
                time.sleep(1)
                continue

            # Try to submit
            if self._click_submit_button():
                return True

            # Else, try next
            if self._click_next_button():
                continue

            self.logger.warning("No Next or Submit button found; stopping multi-step loop")
            break

        return True

    def _try_resume_upload(self, resume_path: str):
        """Upload the configured resume file without any Windows fallbacks."""
        driver = self.browser.driver

        # 1) Log exactly what path we're using
        self.logger.info(f"Attempting resume upload from: {resume_path}")

        # 2) Find *any* file-upload inputs on the modal
        try:
            inputs = WebDriverWait(driver, self.browser.wait_time).until(
                EC.presence_of_all_elements_located(
                    (By.CSS_SELECTOR, "input[type='file']")
                )
            )
        except Exception as e:
            self.logger.warning(f"No file inputs found on step: {e}")
            return

        if not inputs:
            self.logger.warning("No <input type='file'> elements present.")
            return

        # 3) Use the first one
        upload = inputs[0]
        if not os.path.isabs(resume_path):
            self.logger.warning("resume_path is not absolute; upload may fail.")
        if os.path.isfile(resume_path):
            try:
                upload.send_keys(resume_path)
                self.logger.info("Uploaded resume successfully.")
            except Exception as e:
                self.logger.error(f"Upload.send_keys failed: {e}")
        else:
            self.logger.error(f"Resume file not found at: {resume_path}")

    
    def _try_default_resume(self):
        """Try to select default LinkedIn resume"""
        try:
            default_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//label[contains(., 'Default LinkedIn Resume')]")
                )
            )
            if self.browser.safe_click(default_elem):
                self.logger.info("Selected default LinkedIn resume")
        except Exception:
            self.logger.warning("Could not select default resume")
    
    def _click_next_button(self) -> bool:
        """Click the ‘Next’ or ‘Continue to next step’ button in Easy Apply."""
        driver = self.browser.driver

        try:
            # 1) Preferred: data-live-test or data-easy-apply hook
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (By.CSS_SELECTOR,
                     "button[data-live-test-easy-apply-next-button],"
                     "button[data-easy-apply-next-button]")
                )
            )
            driver.execute_script("arguments[0].scrollIntoView({block:'center'});", btn)
            time.sleep(0.3)
            btn.click()
            self.logger.info("Clicked Next (data-easy-apply-next-button)")
            return True

        except Exception:
            pass

        try:
            # 2) Fallback: aria-label or button text
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (By.XPATH,
                     "//button[normalize-space(span)='Next' or "
                     "contains(@aria-label,'Continue to next step')]"
                    )
                )
            )
            driver.execute_script("arguments[0].scrollIntoView({block:'center'});", btn)
            time.sleep(0.3)
            btn.click()
            self.logger.info("Clicked Next (aria-label/text fallback)")
            return True

        except Exception as e:
            self.logger.error(f"Failed to click Next: {e}")
            return False
        

    def _wait_for_step_advance(self, timeout: float = 10):
        """Wait for the progress spinner or bar to move past the current step."""
        driver = self.browser.driver
        try:
            # wait until the spinner disappears or the progress bar width increases
            WebDriverWait(driver, timeout).until_not(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, ".artdeco-spinner, .jobs-easy-apply-modal .artdeco-progress-indicator__label")
                )
            )
            # tiny pause to let the next form render
            time.sleep(1)
        except Exception:
            # if it never goes away, just move on
            self.logger.warning("Timed out waiting for Next step to load")
 
    def _click_review_button(self) -> bool:
        """Click the Review button if present"""
        driver = self.browser.driver
        try:
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (
                        By.CSS_SELECTOR,
                        "button[data-live-test-easy-apply-review-button], "
                        "button[aria-label='Review your application'], "
                        "button span:contains('Review')"
                    )
                )
            )
            self.browser.safe_click(btn)
            self.logger.info("Clicked Review button")
            return True
        except Exception:
            return False

    
    def _click_submit_button(self) -> bool:
        """Click the ‘Submit application’ button and wait for the modal to close."""
        driver = self.browser.driver

        try:
            # 1) Wait for the submit button (aria-label or data-live-test hook)
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (
                        By.CSS_SELECTOR,
                        "button[data-live-test-easy-apply-submit-button],"
                        "button[aria-label='Submit application']"
                    )
                )
            )

            # 2) Scroll into view & click
            driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", btn
            )
            time.sleep(0.5)
            btn.click()
            self.logger.info("Clicked ‘Submit application’")

            # 3) Wait for the Easy Apply modal to disappear
            WebDriverWait(driver, self.browser.wait_time).until(
                EC.invisibility_of_element_located(
                    (By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
                )
            )
            self.logger.info("Modal closed after submit")
            return True

        except Exception as e:
            self.logger.error(f"Failed to click Submit: {e}")
            return False

    

    
    def apply_for_jobs(self, resume_path: str):
        """Main job application loop"""
        # Get already applied jobs
        applied_urls = self.data.get_applied_job_urls()
        applied_urls |= self.get_applied_jobs_from_linkedin()
        
        for keyword in self.search_keywords:
            self.logger.info(f"Searching for: {keyword}")
            self._search_and_apply(keyword, resume_path, applied_urls)
    
    
    def _search_and_apply(self, keyword: str, resume_path: str, applied_urls: Set[str]):
        """Search for jobs and apply, ensuring fresh elements each loop"""
        page = 0
        while True:
            if not self._navigate_to_page(keyword, page):
                break

            self.logger.info(f"Processing jobs on page {page + 1}")

            jobs = self._get_job_cards()
            if not jobs:
                self.logger.info("No more jobs found")
                break

            for i in range(len(jobs)):
                jobs = self._get_job_cards()  # Refresh to avoid stale elements
                if i >= len(jobs): break
                try:
                    self._process_single_job(jobs[i], resume_path, applied_urls)
                except Exception as e:
                    self.logger.error(f"Error processing job: {e}")
                    self.stats.current_stats.errors += 1
                self.stats.update_dashboard()
                time.sleep(random.uniform(1, 3))

            page += 1
            
    def _navigate_to_page(self, keyword: str, page: int) -> bool:
        """Navigate to search results page"""
        if page == 0:
            # First page - use search
            search_url = (
                f"https://www.linkedin.com/jobs/search/"
                f"?f_AL=true&keywords={keyword}&location=Remote"
            )
            self.browser.driver.get(search_url)
        else:
            # Try clicking Next button first
            try:
                next_btn = WebDriverWait(self.browser.driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[.//span[contains(text(), 'Next')]]")
                    )
                )
                next_btn.click()
                self.logger.info("Clicked Next button")
                time.sleep(random.uniform(5, 10))
                return True
            except TimeoutException:
                # Fallback to URL pagination
                page_url = (
                    f"https://www.linkedin.com/jobs/search/"
                    f"?f_AL=true&keywords={keyword}&location=Remote"
                    f"&start={page * 25}"
                )
                self.browser.driver.get(page_url)
        
        time.sleep(random.uniform(5, 10))
        
        # Wait for job cards to load
        try:
            WebDriverWait(self.browser.driver, 15).until(
                EC.presence_of_all_elements_located(
                    (By.CLASS_NAME, 'job-card-container')
                )
            )
            return True
        except TimeoutException:
            return False
    
    def _get_job_cards(self) -> List:
        """Get all job cards on current page"""
        return self.browser.driver.find_elements(By.CLASS_NAME, 'job-card-container')
    
    def _process_single_job(self, job_card, resume_path: str, 
                           applied_urls: Set[str]):
        """Process a single job application"""
        self.stats.current_stats.jobs_processed += 1
        
        # Check if already applied (from card text)
        card_text = job_card.text.lower()
        if "applied" in card_text:
            self.logger.info("Skipping job - already applied")
            self.stats.current_stats.skipped += 1
            return
        
        # Get job URL
        try:
            job_link = job_card.find_element(By.CSS_SELECTOR, "a")
            job_url = job_link.get_attribute("href") or ""
        except Exception:
            job_url = ""
        
        # Check if URL already processed
        if job_url in applied_urls:
            self.logger.info(f"Skipping duplicate job: {job_url}")
            self.stats.current_stats.skipped += 1
            return
        
        # Click on job card
        self.browser.driver.execute_script(
            "arguments[0].scrollIntoView(true);", job_card
        )
        if not self.browser.safe_click(job_card):
            self.stats.current_stats.skipped += 1
            return
        
        job_description = self._extract_job_description()

        time.sleep(random.uniform(2, 4))
        
        # Look for Easy Apply button
        try:
            easy_apply_btn = WebDriverWait(self.browser.driver, 5).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//button[contains(@class, 'jobs-apply-button')]")
                )
            )
        except TimeoutException:
            self.logger.info("No Easy Apply button found")
            self.stats.current_stats.skipped += 1
            return
        
        # Click Easy Apply
        if not self.browser.safe_click(easy_apply_btn):
            self.stats.current_stats.skipped += 1
            return
        
        time.sleep(random.uniform(2, 3))
        
        # Handle application
        if not self.handle_multi_step_application(resume_path):
            self.logger.info("Application not submitted")
            self.stats.current_stats.skipped += 1
            return
        
        # Get job details for logging
        job_title, company = self._get_job_details()
        
        # Log successful application
        self.data.log_application(job_title, company, job_url, 
                                 resume_path, 'submitted')
        self.logger.info(f"Applied to {job_title} at {company}")
        self.stats.current_stats.applied += 1
        applied_urls.add(job_url)
    
    def _extract_job_description(self) -> str:
        """Extracts the job description text from the current job page"""
        try:
            desc_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-description__content"))
            )
            return desc_elem.text.strip()
        except Exception:
            return ""
        
    def _get_job_details(self) -> tuple:
        """Extract job title and company from job page"""
        job_title = "Unknown Job"
        company = "Unknown Company"
        
        try:
            title_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__job-title')
                )
            )
            job_title = title_elem.text.strip() or job_title
            
            company_elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__company-name'
            )
            company = company_elem.text.strip() or company
            
        except Exception:
            self.logger.warning("Could not extract job details")
        
        return job_title, company

class NetworkExpander:
    """Manages LinkedIn network expansion"""
    def __init__(self, browser_manager: BrowserManager, stats_manager: StatsManager,
                 logger: Logger):
        self.browser = browser_manager
        self.stats   = stats_manager
        self.logger  = logger

    def _click_load_more(self):
        """Click the “Load more” button if it’s present"""
        try:
            btn = self.browser.driver.find_element(
                By.XPATH, "//button[normalize-space()='Load more']"
            )
            if self.browser.safe_click(btn):
                self.logger.info("Clicked ‘Load more’")
                time.sleep(2)
        except Exception:
            # no Load more button — skip
            pass

    def expand_network(self):
        """Main network expansion process"""
        self.logger.info("Starting network expansion")
        
        # Go to Grow page
        self.browser.driver.get("https://www.linkedin.com/mynetwork/grow/")
        time.sleep(5)
        
        # Load more suggestions once up front
        self._click_load_more()
        
        max_retries = 5
        for attempt in range(max_retries):
            self.stats.network_stats.attempts += 1

            # (Re-)click load more every time we refresh
            if attempt > 0:
                self._click_load_more()

            # Find Accept & Connect buttons
            accept_buttons  = self._find_accept_buttons()
            connect_buttons = self._find_connect_buttons()
            total = len(accept_buttons) + len(connect_buttons)

            if total == 0:
                self.logger.info("No network actions found, refreshing...")
                self.browser.driver.refresh()
                time.sleep(5)
                continue

            self.logger.info(f"Found {total} network actions")

            for btn in accept_buttons:
                if self.browser.safe_click(btn):
                    self.logger.info("Accepted an invitation")
                    self.stats.network_stats.actions_accepted += 1
                time.sleep(1)

            for btn in connect_buttons:
                if self.browser.safe_click(btn):
                    self.logger.info("Sent a connection request")
                    self.stats.network_stats.actions_sent += 1
                time.sleep(1)

            # Done this batch — loop again to pick up more
            time.sleep(3)

        self.logger.info("Network expansion complete")

# ===================== DASHBOARD =====================
class DashboardServer:
    """FastAPI dashboard server"""
    def __init__(self, config: Config):
        self.config = config
        self.app = FastAPI()
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup API routes"""
        @self.app.get("/")
        def read_dashboard():
            try:
                with open(self.config.DASHBOARD_FILE, "r") as f:
                    data = json.load(f)
            except Exception as e:
                data = {"error": str(e)}
            return data
        
        @self.app.get("/stats")
        def read_stats():
            stats_manager = StatsManager(self.config)
            return stats_manager.load_overall_stats()
    
    def run(self, host: str = "127.0.0.1", port: int = 8000):
        """Run the dashboard server"""
        uvicorn.run(self.app, host=host, port=port)

# ===================== CLI INTERFACE =====================
class LinkedInAutomationCLI:
    """Command-line interface for the automation"""
    def __init__(self):
        self.config = Config.load()
        self.logger = Logger(self.config)
        self.stats_manager = StatsManager(self.config)
        self.data_manager = DataManager(self.config)
        self.browser_manager = None
        self.ai_provider = None
    
    def run(self):
        """Main entry point"""
        if len(sys.argv) > 1:
            if sys.argv[1] == "--apply":
                self.direct_apply_mode()
            elif sys.argv[1] == "--menu":
                self.menu_mode()
            else:
                print("Unknown parameter. Use --apply or --menu")
                self.menu_mode()
        else:
            self.menu_mode()
        
        # Print final stats
        self.print_stats()
        self.logger.info("Exiting")
    
    def menu_mode(self):
        """Interactive menu mode"""
        while True:
            print("\n" + "="*50)
            print("LinkedIn Automation System")
            print("="*50)
            print("1. Apply for Jobs")
            print("2. Expand Network")
            print("3. View Application Log")
            print("4. View Statistics")
            print("5. Update Configuration")
            print("6. Launch Dashboard Server")
            print("7. Quit")
            print("="*50)
            
            choice = input("Choose an option (1-7): ")
            
            if choice == '1':
                self.job_application_mode()
            elif choice == '2':
                self.network_expansion_mode()
            elif choice == '3':
                self.view_application_log()
            elif choice == '4':
                self.print_stats()
            elif choice == '5':
                self.update_configuration()
            elif choice == '6':
                self.launch_dashboard()
            elif choice == '7':
                break
            else:
                print("Invalid choice. Please try again.")
    
    def direct_apply_mode(self):
        """Direct application mode with minimal interaction"""
        # Ask about auto-defaults
        auto_defaults = input(
            "Auto-use saved/prefilled answers? (Y/n): "
        ).strip().lower() != "n"
        self.config.auto_use_defaults = auto_defaults
        
        # Continuous application loop
        while True:
            self.job_application_mode()
            
            continue_prompt = input(
                "\nCompleted search cycle. Continue? (Y/n): "
            ).strip().lower()
            if continue_prompt == "n":
                break
    
    def job_application_mode(self):
        """Run job application process"""
        # Ensure credentials
        if not self.config.username or not self.config.password:
            self.update_credentials()
        
        # Ensure resume path
        if not self.config.resume_path:
            self.config.resume_path = input(
                "Enter full path to resume: "
            ).strip()
            self.config.save()
        
        # Initialize components
        self.browser_manager = BrowserManager(self.config, self.logger)
        self.ai_provider = ChatGPTProvider(self.config.openai_api_key)
        
        form_filler = FormFiller(
            self.browser_manager, self.ai_provider, 
            self.data_manager, self.config, self.logger
        )
        
        job_manager = JobApplicationManager(
            self.browser_manager, form_filler, self.data_manager,
            self.stats_manager, self.logger
        )

        try:
            self.browser_manager.init_browser()
            if not self.browser_manager.login(self.config.username, self.config.password):
                self.logger.error("Login failed")
                return

            try:
                job_manager.apply_for_jobs(self.config.resume_path)
            except InvalidSessionIdException as e:
                self.logger.error(f"Session lost ({e}); restarting and retrying…")
                self.browser_manager.quit()
                # one retry only:
                self.browser_manager.init_browser()
                if self.browser_manager.login(self.config.username, self.config.password):
                    job_manager.apply_for_jobs(self.config.resume_path)
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            try:
                self.browser_manager.take_screenshot("error")
            except Exception:
                pass
        finally:
            self.browser_manager.quit()
    
        """
        try:
            # Initialize browser and login
            self.browser_manager.init_browser()
            
            if self.browser_manager.login(self.config.username, 
                                        self.config.password):
                # Start applying
                job_manager.apply_for_jobs(self.config.resume_path)
            else:
                self.logger.error("Login failed")
        
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            self.browser_manager.take_screenshot("error")
        
        finally:
            if self.browser_manager:
                self.browser_manager.quit()
        """
        
    def network_expansion_mode(self):
        """Run network expansion process"""
        # Ensure credentials
        if not self.config.username or not self.config.password:
            self.update_credentials()
        
        # Initialize components
        self.browser_manager = BrowserManager(self.config, self.logger)
        network_expander = NetworkExpander(
            self.browser_manager, self.stats_manager, self.logger
        )
        
        try:
            # Initialize browser and login
            self.browser_manager.init_browser()
            
            if self.browser_manager.login(self.config.username, 
                                        self.config.password):
                # Expand network
                network_expander.expand_network()
            else:
                self.logger.error("Login failed")
        
        except Exception as e:
            self.logger.error(f"Network expansion error: {e}")
        
        finally:
            if self.browser_manager:
                self.browser_manager.quit()
    
    def view_application_log(self):
        """View application history"""
        if os.path.exists(self.config.APPLICATION_LOG):
            with open(self.config.APPLICATION_LOG, 'r') as f:
                logs = json.load(f)
                
                print(f"\n{'='*80}")
                print(f"{'Job Title':<30} {'Company':<25} {'Date':<25}")
                print(f"{'='*80}")
                
                for log in logs[-20:]:  # Show last 20
                    title = log['job_title'][:29]
                    company = log['company'][:24]
                    date = log['date_applied'][:24]
                    print(f"{title:<30} {company:<25} {date:<25}")
                
                print(f"\nTotal applications: {len(logs)}")
        else:
            print("No applications logged yet.")
    
    def print_stats(self):
        """Print statistics"""
        # Current run stats
        current = self.stats_manager.current_stats
        throughput = current.get_throughput()
        
        print("\n" + "="*50)
        print("CURRENT RUN STATISTICS")
        print("="*50)
        print(f"Jobs Processed: {current.jobs_processed}")
        print(f"Jobs Applied: {current.applied}")
        print(f"Jobs Skipped: {current.skipped}")
        print(f"Errors: {current.errors}")
        print(f"Throughput: {throughput['per_hour']:.2f} jobs/hour")
        print(f"Uptime: {time.time() - current.start_time:.0f} seconds")
        
        # Network stats
        network = self.stats_manager.network_stats
        print(f"\nNetwork Actions:")
        print(f"  Invitations Accepted: {network.actions_accepted}")
        print(f"  Connections Sent: {network.actions_sent}")
        print(f"  Total Attempts: {network.attempts}")
        
        # Overall stats
        overall = self.stats_manager.update_overall_stats()
        
        print("\n" + "="*50)
        print("OVERALL STATISTICS")
        print("="*50)
        print(f"Total Runs: {overall['run_count']}")
        print(f"Total Jobs Processed: {overall['jobs_processed']}")
        print(f"Total Jobs Applied: {overall['applied']}")
        print(f"Total Jobs Skipped: {overall['skipped']}")
        print(f"Total Errors: {overall['errors']}")
        print(f"Total Network Actions: {overall['network']['actions_accepted'] + overall['network']['actions_sent']}")
        print(f"Last Run: {overall['last_run_timestamp'] or 'N/A'}")
    
    def update_credentials(self):
        """Update login credentials"""
        print("\nUpdate LinkedIn Credentials")
        self.config.username = input("Username/Email: ").strip()
        self.config.password = getpass.getpass("Password: ")
        self.config.save()
        self.logger.info("Credentials updated")
    
    def update_configuration(self):
        """Update full configuration"""
        print("\nUpdate Configuration")
        
        # Credentials
        self.update_credentials()
        
        # Resume
        resume = input(
            f"Resume path [{self.config.resume_path}]: "
        ).strip()
        if resume:
            self.config.resume_path = resume
        
        # OpenAI API key
        api_key = input(
            "OpenAI API key (press Enter to skip): "
        ).strip()
        if api_key:
            self.config.openai_api_key = api_key
        
        # Browser choice
        browser = input(
            f"Browser [{self.config.browser}]: "
        ).strip().lower()
        if browser in ['chrome', 'edge']:
            self.config.browser = browser
        
        self.config.save()
        self.logger.info("Configuration updated")
    
    def launch_dashboard(self):
        """Launch dashboard server"""
        dashboard = DashboardServer(self.config)
        
        # Run in separate thread
        server_thread = threading.Thread(
            target=dashboard.run,
            daemon=True
        )
        server_thread.start()
        
        # Open browser
        webbrowser.open("http://127.0.0.1:8000")
        self.logger.info("Dashboard launched at http://127.0.0.1:8000")
        
        input("Press Enter to continue...")

# ===================== MAIN ENTRY POINT =====================
if __name__ == '__main__':
    cli = LinkedInAutomationCLI()
    cli.run()