#!/usr/bin/env python3
"""
Integrated LinkedIn Automation Script with ChatGPT Integration

- Automates LinkedIn login and job application workflow.
- For each free text field (including resume/cover letter fields),
  calls ChatGPT via the OpenAI API to generate a tailored answer if no saved answer exists.
- Uses a configuration file ("linkedin_config.json") for credentials and API keys.
- Tracks run statistics and logs errors.
"""

import json
import os
import time
import random
import getpass
import requests
import sys
import threading
import webbrowser
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException,
    ElementClickInterceptedException, StaleElementReferenceException,
    ElementNotInteractableException, WebDriverException
)
from selenium.webdriver.common.action_chains import ActionChains
from fastapi import FastAPI
import uvicorn

# ===================== CONFIG & LOGGING =====================

CONFIG_FILE = 'linkedin_config.json'
APPLICATION_LOG = 'applications_log.json'
QUESTIONS_LOG = 'questions_log.json'
ERROR_LOG = 'error_log.txt'
SKIPPED_LOG = 'skipped_log.json'
OVERALL_STATS_FILE = 'overall_stats.json'  # To hold overall cumulative stats

# Global flag: if True, auto-use saved/prefilled answers without prompting
AUTO_USE_DEFAULTS = False

# These stats track the current run.
stats = {
    "applied": 0,
    "skipped": 0,
    "errors": 0,
    "jobs_processed": 0,
    "start_time": time.time()
}

# These stats track network expansion for the current run.
network_stats = {
    "actions_accepted": 0,
    "actions_sent": 0,
    "attempts": 0
}

# ----------------- Overall Stats Functions -----------------
def load_overall_stats():
    """Load cumulative stats from a JSON file; if not available, initialize defaults."""
    if os.path.exists(OVERALL_STATS_FILE):
        with open(OVERALL_STATS_FILE, 'r') as f:
            return json.load(f)
    # Default overall stats
    return {
        "run_count": 0,
        "applied": 0,
        "skipped": 0,
        "errors": 0,
        "jobs_processed": 0,
        "network": {
            "actions_accepted": 0,
            "actions_sent": 0,
            "attempts": 0
        },
        "last_run_timestamp": None
    }

def update_overall_stats():
    """Update overall stats by adding the current run's stats to the cumulative stats."""
    overall = load_overall_stats()
    overall["run_count"] += 1
    overall["applied"] += stats["applied"]
    overall["skipped"] += stats["skipped"]
    overall["errors"] += stats["errors"]
    overall["jobs_processed"] += stats["jobs_processed"]
    overall["network"]["actions_accepted"] += network_stats["actions_accepted"]
    overall["network"]["actions_sent"] += network_stats["actions_sent"]
    overall["network"]["attempts"] += network_stats["attempts"]
    overall["last_run_timestamp"] = datetime.now().isoformat()
    with open(OVERALL_STATS_FILE, "w") as f:
        json.dump(overall, f, indent=4)
    return overall

# ----------------- End Overall Stats Functions -----------------

def load_config():
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, 'r') as file:
            return json.load(file)
    return {}

def save_config(config):
    with open(CONFIG_FILE, 'w') as file:
        json.dump(config, file, indent=4)

def load_questions():
    if os.path.exists(QUESTIONS_LOG):
        with open(QUESTIONS_LOG, 'r') as file:
            return json.load(file)
    return {}

def save_questions(questions):
    with open(QUESTIONS_LOG, 'w') as file:
        json.dump(questions, file, indent=4)

def load_application_log():
    if os.path.exists(APPLICATION_LOG):
        with open(APPLICATION_LOG, 'r') as file:
            return json.load(file)
    return []

def log_application(job_title, company, job_url, resume_used, status):
    entry = {
        'job_title': job_title,
        'company': company,
        'job_url': job_url,
        'resume_used': resume_used,
        'status': status,
        'date_applied': datetime.now().isoformat()
    }
    logs = load_application_log()
    logs.append(entry)
    with open(APPLICATION_LOG, 'w') as file:
        json.dump(logs, file, indent=4)

def log_error(message):
    with open(ERROR_LOG, "a") as f:
        f.write(f"{datetime.now().isoformat()} - {message}\n")

def load_skipped_log():
    if os.path.exists(SKIPPED_LOG):
        with open(SKIPPED_LOG, 'r') as file:
            return json.load(file)
    return []

def log_skipped(job_url, reason):
    entry = {
        'job_url': job_url,
        'reason': reason,
        'date': datetime.now().isoformat()
    }
    skipped = load_skipped_log()
    skipped.append(entry)
    with open(SKIPPED_LOG, 'w') as file:
        json.dump(skipped, file, indent=4)

# ===================== ADVANCED HELPERS =====================
backoff_time = 5
backoff_multiplier = 2
max_backoff = 300

def exponential_backoff():
    global backoff_time
    print(f"Backing off for {backoff_time} seconds due to rate limiting.")
    time.sleep(backoff_time)
    backoff_time = min(backoff_time * backoff_multiplier, max_backoff)

def reset_backoff():
    global backoff_time
    backoff_time = 5

def take_screenshot(driver, label="error"):
    filename = f"screenshot_{label}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    driver.save_screenshot(filename)
    print(f"Screenshot saved: {filename}")

# --- Notification stub ---
def notify_error(message):
    # TODO: Integrate with an actual notification system (email, Slack, etc.)
    print("NOTIFY ERROR:", message)

# --- Dashboard update: writes current (run) stats including network stats to a JSON file ---
def update_dashboard():
    dashboard_data = {
        "applied": stats["applied"],
        "skipped": stats["skipped"],
        "errors": stats["errors"],
        "jobs_processed": stats["jobs_processed"],
        "uptime": time.time() - stats["start_time"],
        "network_stats": network_stats
    }
    try:
        with open("dashboard_data.json", "w") as f:
            json.dump(dashboard_data, f, indent=4)
        print("Dashboard updated.")
    except Exception as e:
        log_error(f"Dashboard update error: {e}")
        notify_error(f"Dashboard update error: {e}")

# ===================== BROWSER & LOGIN =====================
# Initializes Selenium WebDriver and logs into LinkedIn.

def init_browser(browser_choice='chrome'):
    if browser_choice.lower() == 'chrome':
        return webdriver.Chrome()
    elif browser_choice.lower() == 'edge':
        return webdriver.Edge()
    else:
        return webdriver.Chrome()

def linkedin_login(driver, username, password):
    driver.get("https://www.linkedin.com/login")
    try:
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, 'username')))
    except TimeoutException:
        log_error("Login page did not load.")
        notify_error("Login page did not load.")
        return False
    driver.find_element(By.ID, 'username').send_keys(username)
    driver.find_element(By.ID, 'password').send_keys(password)
    driver.find_element(By.ID, 'password').send_keys(Keys.RETURN)
    try:
        WebDriverWait(driver, 20).until(EC.url_contains('feed'))
    except TimeoutException:
        log_error("Login timeout: feed did not load.")
        notify_error("Login timeout: feed did not load.")
        return False
    return "feed" in driver.current_url

def get_applied_jobs(driver):
    """Retrieve applied jobs to avoid duplicates."""
    applied_jobs = set()
    try:
        driver.get("https://www.linkedin.com/my-items/saved-jobs/?cardType=APPLIED")
        WebDriverWait(driver, 10).until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "a.job-card-container__link")))
        jobs = driver.find_elements(By.CSS_SELECTOR, "a.job-card-container__link")
        for job in jobs:
            job_url = job.get_attribute("href")
            if job_url:
                applied_jobs.add(job_url)
    except Exception as e:
        log_error(f"Could not retrieve applied jobs: {e}")
        print(f"Could not retrieve applied jobs: {e}")
    return applied_jobs

# ===================== ACTION HELPERS =====================
# safe_click ensures the element is clicked even if transient errors occur.
def safe_click(driver, element, retries=3, delay=2):
    for _ in range(retries):
        try:
            element.click()
            return True
        except (ElementClickInterceptedException, ElementNotInteractableException) as e:
            time.sleep(delay)
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
        except StaleElementReferenceException:
            break
        except Exception as e:
            log_error(f"Click error: {e}")
            time.sleep(delay)
    return False

def get_default_resume(driver):
    try:
        default_resume_element = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//label[contains(., 'Default LinkedIn Resume')]"))
        )
        if safe_click(driver, default_resume_element):
            return "Default LinkedIn Resume"
    except Exception as e:
        log_error(f"Default resume error: {e}")
        print("No default resume found or error:", e)
    return None

# ===================== NETWORK EXPANSION =====================
# Expands your network by accepting invitations and sending connect requests.
# Tracks network actions in network_stats.
# TODO: Integrate creation of work item cards (e.g., Trello, GitHub, or Azure DevOps) for monitoring network expansion issues.


def expand_network(driver):
    print("Expanding your LinkedIn network...")
    driver.get("https://www.linkedin.com/mynetwork/")
    time.sleep(5)
    max_retries = 5
    global network_stats
    for attempt in range(max_retries):
        network_stats["attempts"] += 1
        accept_buttons = driver.find_elements(By.XPATH, "//span[text()='Accept']/ancestor::button")
        connect_buttons = driver.find_elements(By.XPATH, "//span[text()='Connect']/ancestor::button")
        total_buttons = len(accept_buttons) + len(connect_buttons)
        if total_buttons == 0:
            print("No invitations or connect suggestions found. Refreshing the page...")
            driver.refresh()
            time.sleep(5)
            accept_buttons = driver.find_elements(By.XPATH, "//span[text()='Accept']/ancestor::button")
            connect_buttons = driver.find_elements(By.XPATH, "//span[text()='Connect']/ancestor::button")
            total_buttons = len(accept_buttons) + len(connect_buttons)
            if total_buttons == 0:
                print("No further network actions available.")
                break

        print(f"Found {total_buttons} network actions. Processing...")
        for btn in accept_buttons:
            if safe_click(driver, btn):
                print("Accepted an invitation.")
                network_stats["actions_accepted"] += 1
            time.sleep(random.uniform(1, 2))
        for btn in connect_buttons:
            if safe_click(driver, btn):
                print("Sent a connect invitation.")
                network_stats["actions_sent"] += 1
            time.sleep(random.uniform(1, 2))
        print("Waiting for new suggestions...")
        time.sleep(3)
    print("Finished expanding network.")
    update_dashboard()

# ===================== CHATGPT INTEGRATION =====================
# This function calls the OpenAI ChatGPT API to generate suggestions for any free text field.

def get_chatgpt_suggestion(field_label, field_type, base_prompt=""):
    """
    Uses ChatGPT (via OpenAI API) to generate an answer for a given field.
    Expects an API key stored in config as "openai_api_key".
    """
    prompt = (f"Provide a concise, professional answer for the following LinkedIn job application field.\n"
              f"Field: {field_label}\nType: {field_type}\n")
    if base_prompt:
        prompt += f"Context: {base_prompt}\n"
    prompt += "Answer:"
    
    config_local = load_config()  # reload config to get API key
    api_key = config_local.get("openai_api_key")
    if not api_key:
        print("No OpenAI API key found in config. Using default answer.")
        return f"Default answer for {field_label}"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    payload = {
        "model": "gpt-3.5-turbo",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": 200
    }
    try:
        response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload, timeout=15)
        if response.status_code == 200:
            data = response.json()
            return data["choices"][0]["message"]["content"].strip()
        else:
            error_msg = f"ChatGPT API response: {response.status_code} {response.text}"
            log_error(error_msg)
            notify_error(error_msg)
    except Exception as e:
        log_error(f"Error calling ChatGPT API: {e}")
        notify_error(f"Error calling ChatGPT API: {e}")
    return f"Default answer for {field_label}"

# ===================== FORM FILLING =====================
# Now integrated with ChatGPT so that every free text field uses ChatGPT if no saved answer is available.

def fill_form_fields_in_modal(driver):
    """
    Fills in each field inside the Easy Apply modal.
    Uses saved answers, prefilled values (with prompt unless AUTO_USE_DEFAULTS is enabled),
    or LLM suggestions as needed.
    """
    questions = load_questions()
    try:
        form_section = driver.find_element(By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
    except NoSuchElementException:
        print("Easy Apply modal not found. Skipping form fill.")
        return

    # Process text inputs and textareas
    text_inputs = form_section.find_elements(By.CSS_SELECTOR, "input[type='text'], input[type='tel'], input[type='email'], textarea")
    for input_field in text_inputs:
        label = "Unnamed text field"
        try:
            label_elem = input_field.find_element(By.XPATH, "./ancestor::div[label]//label")
            label = label_elem.text.strip()
        except NoSuchElementException:
            pass

        # For cover letters and resume fields we always call ChatGPT for a fresh suggestion
        if "cover" in label.lower() or "resume" in label.lower():
            suggestion = get_chatgpt_suggestion(label, "text")
            print(f"ChatGPT suggestion for '{label}': {suggestion}")
            answer = suggestion
        else:
            if label in questions and questions[label].strip():
                answer = questions[label].strip()
                print(f"Auto using saved answer for '{label}': {answer}")
            else:
                prefilled = input_field.get_attribute("value").strip()
                if prefilled:
                    if AUTO_USE_DEFAULTS:
                        print(f"Auto using prefilled value for '{label}': {prefilled}")
                        answer = prefilled
                    else:
                        use_prefilled = input(f"Field '{label}' is prefilled with '{prefilled}'. Use this? (Y/n): ").strip().lower() or "y"
                        if use_prefilled == "y":
                            answer = prefilled
                        else:
                            # Use ChatGPT for a suggestion
                            suggestion = get_chatgpt_suggestion(label, "text")
                            print(f"ChatGPT suggestion: {suggestion}")
                            answer = input(f"Enter answer for '{label}': ").strip() or suggestion
                else:
                    suggestion = get_chatgpt_suggestion(label, "text")
                    print(f"\nField: {label}")
                    print(f"ChatGPT suggestion: {suggestion}")
                    answer = input(f"Enter answer for '{label}': ").strip() or suggestion
            questions[label] = answer

        try:
            input_field.clear()
            input_field.send_keys(answer)
            try:
                suggestion_elem = WebDriverWait(driver, 3).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "li.artdeco-typeahead__hit"))
                )
                suggestion_elem.click()
                print(f"Selected top autocomplete suggestion for '{label}'.")
            except TimeoutException:
                print(f"No autocomplete suggestion found for '{label}', using entered value.")
        except ElementNotInteractableException:
            print(f"Field '{label}' is not interactable; skipping.")

    # Process dropdowns, checkboxes, and radio buttons (unchanged)
    selects = form_section.find_elements(By.TAG_NAME, "select")
    for sel in selects:
        label = "Unnamed drop-down"
        try:
            label_elem = sel.find_element(By.XPATH, "./ancestor::div[label]//label")
            label = label_elem.text.strip()
        except NoSuchElementException:
            pass
        select_obj = Select(sel)
        try:
            current_sel = select_obj.first_selected_option.text.strip()
        except Exception:
            current_sel = ""
        if label in questions and questions[label].strip():
            answer = questions[label].strip()
            print(f"Auto using saved choice for '{label}': {answer}")
        elif current_sel and current_sel.lower() not in ["select an option", ""]:
            if AUTO_USE_DEFAULTS:
                print(f"Auto using prefilled drop-down for '{label}': {current_sel}")
                answer = current_sel
            else:
                use_prefilled = input(f"Drop-down '{label}' is prefilled with '{current_sel}'. Use this? (Y/n): ").strip().lower() or "y"
                if use_prefilled == "y":
                    answer = current_sel
                else:
                    options = [o.text for o in select_obj.options if o.text.strip()]
                    print(f"Available options for '{label}':")
                    for idx, opt in enumerate(options):
                        print(f"{idx}: {opt}")
                    choice_str = input(f"Select an option index for '{label}': ").strip()
                    try:
                        idx_choice = int(choice_str)
                        answer = options[idx_choice]
                    except Exception:
                        answer = options[0]
                questions[label] = answer
        else:
            options = [o.text for o in select_obj.options if o.text.strip()]
            print(f"\nDrop-down Field: {label}")
            for idx, opt in enumerate(options):
                print(f"{idx}: {opt}")
            choice_str = input(f"Select an option index for '{label}': ").strip()
            try:
                idx_choice = int(choice_str)
                answer = options[idx_choice]
            except Exception:
                answer = options[0]
            questions[label] = answer

        try:
            select_obj.select_by_visible_text(answer)
        except Exception:
            select_obj.select_by_index(0)

    # Process checkboxes
    checkboxes = form_section.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
    for checkbox in checkboxes:
        label = "Unnamed checkbox"
        try:
            label_elem = checkbox.find_element(By.XPATH, "./ancestor::div[label]//label")
            label = label_elem.text.strip()
        except NoSuchElementException:
            pass
        # Automatically accept any checkbox that asks to "follow" their page.
        if "follow" in label.lower():
            if not checkbox.is_selected():
                safe_click(driver, checkbox)
                print(f"Auto-checking checkbox '{label}' (Follow Page).")
            continue

        current = checkbox.is_selected()
        if label in questions:
            keep = (questions[label].lower() == 'true')
            print(f"Auto using saved preference for '{label}': {keep}")
        else:
            if AUTO_USE_DEFAULTS:
                print(f"Auto keeping checkbox '{label}' as {'checked' if current else 'unchecked'}.")
                keep = current
            else:
                if current:
                    use_prefilled = input(f"Checkbox '{label}' is checked. Keep it? (Y/n): ").strip().lower() or "y"
                    keep = (use_prefilled == "y")
                else:
                    use_prefilled = input(f"Checkbox '{label}' is unchecked. Check it? (Y/n): ").strip().lower() or "y"
                    keep = (use_prefilled == "y")
            questions[label] = str(keep)
        if keep and not current:
            safe_click(driver, checkbox)
        elif not keep and current:
            safe_click(driver, checkbox)

    # Process radio buttons
    radios = form_section.find_elements(By.CSS_SELECTOR, "input[type='radio']")
    if radios:
        radio_groups = {}
        for r in radios:
            name = r.get_attribute("name") or "radio-group"
            radio_groups.setdefault(name, []).append(r)
        for group_name, group_radios in radio_groups.items():
            preselected = None
            labels_list = []
            for idx, r in enumerate(group_radios):
                try:
                    lbl = r.find_element(By.XPATH, "./ancestor::div[label]//label").text.strip()
                except NoSuchElementException:
                    lbl = f"Option {idx+1}"
                labels_list.append(lbl)
                if r.is_selected():
                    preselected = lbl
            if group_name in questions:
                print(f"Auto using saved choice for radio group '{group_name}': {questions[group_name]}")
                chosen = questions[group_name]
            elif preselected:
                print(f"Auto using preselected radio for group '{group_name}': {preselected}")
                chosen = preselected
                questions[group_name] = preselected
            else:
                print(f"\nRadio group: {group_name}")
                for idx, lbl in enumerate(labels_list):
                    print(f"{idx}: {lbl}")
                idx_str = input(f"Select an option index for radio group '{group_name}': ").strip()
                try:
                    chosen = labels_list[int(idx_str)]
                except Exception:
                    chosen = labels_list[0]
                questions[group_name] = chosen

            for r, lbl in zip(group_radios, labels_list):
                if lbl == questions[group_name]:
                    safe_click(driver, r)
                    break

    save_questions(questions)
    update_dashboard()

# ===================== APPLICATION POPUP HANDLING =====================
# Closes the confirmation popup after submitting an application.
def close_application_sent_popup(driver):
    try:
        done_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Done')]"))
        )
        done_button.click()
        print("Clicked 'Done' button on popup.")
    except TimeoutException:
        print("'Done' button not found. Trying 'Dismiss'...")
        try:
            dismiss_button = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@aria-label='Dismiss']"))
            )
            dismiss_button.click()
            print("Clicked 'Dismiss' button on popup.")
        except TimeoutException:
            print("No 'Dismiss' button found. Trying X button...")
            try:
                x_button = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button.artdeco-modal__dismiss"))
                )
                x_button.click()
                print("Clicked 'X' button to close popup.")
            except TimeoutException:
                print("No close mechanism found for popup. Manual intervention may be required.")
    try:
        WebDriverWait(driver, 5).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, "div.jobs-easy-apply-modal"))
        )
        print("Popup closed and modal disappeared.")
    except TimeoutException:
        print("Modal did not close in time; proceeding anyway.")

def handle_multi_step_application(driver, resume_path):
    """
    Handles multi-step application process:
      1. Attempts resume upload.
      2. Fills out form fields.
      3. Navigates through 'Next', 'Review', and 'Submit' buttons.
    If a step fails after retries, logs the error and moves on.
    """
    max_steps = 6
    for step in range(max_steps):
        print(f"\n--- Step {step+1} of Easy Apply ---")
        time.sleep(2)
        try:
            upload_resume = WebDriverWait(driver, 2).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.jobs-easy-apply-modal input[type='file']"))
            )
            if resume_path and os.path.isfile(resume_path):
                upload_resume.send_keys(resume_path)
                print("Uploaded local resume.")
            else:
                print("Local resume invalid. Trying default resume.")
                default_resume = get_default_resume(driver)
                if not default_resume:
                    print("No resume available; skipping application.")
                    return True
        except TimeoutException:
            pass

        fill_form_fields_in_modal(driver)

        next_xpath = "//div[contains(@class, 'jobs-easy-apply-modal')]//button[contains(., 'Next') or contains(., 'Continue')]"
        next_buttons = driver.find_elements(By.XPATH, next_xpath)
        if next_buttons:
            next_btn = next_buttons[0]
            print(f"Found Next/Continue button: displayed={next_btn.is_displayed()}, enabled={next_btn.is_enabled()}")
            if not next_btn.is_enabled():
                try:
                    driver.execute_script("arguments[0].click();", next_btn)
                    print("Forced click on Next/Continue button.")
                    continue
                except Exception as e:
                    log_error(f"Forced click failed: {e}")
                    notify_error(f"Forced click failed: {e}")
                    return True
            else:
                if safe_click(driver, next_btn):
                    print("Clicked Next/Continue button.")
                    continue
                else:
                    print("Failed to click Next/Continue button.")
                    return True

        review_buttons = driver.find_elements(By.XPATH,
            "//div[contains(@class, 'jobs-easy-apply-modal')]//button[contains(., 'Review')]"
        )
        if review_buttons:
            if safe_click(driver, review_buttons[0]):
                print("Clicked Review button.")
                time.sleep(2)
                continue

        submit_buttons = driver.find_elements(By.XPATH,
            "//div[contains(@class, 'jobs-easy-apply-modal')]//button[contains(@aria-label, 'Submit application') or contains(., 'Submit')]"
        )
        if submit_buttons:
            if safe_click(driver, submit_buttons[0]):
                print("Clicked Submit button.")
                close_application_sent_popup(driver)
                return True

        print("No Next/Review/Submit button found on this step; assuming form complete or stuck.")
        break
    return True

# ===================== JOB APPLICATION LOGIC =====================
# This function iterates through job listings and applies to jobs not already applied.
# It uses pagination (either button or URL-based) and handles errors gracefully.
# TODO: Integrate work item card creation on Trello/GitHub/Azure DevOps when critical errors occur.

def apply_for_jobs(driver, resume_path, search_keyword, duplicate_urls):
    page = 0
    while True:
        try:
            next_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[.//span[contains(text(), 'Next')]]"))
            )
            next_btn.click()
            print("Clicked Next button to load the next page.")
            time.sleep(random.uniform(10, 20))
        except TimeoutException:
            print("Next button not found. Falling back to URL pagination.")
            page += 1
            next_url = f"https://www.linkedin.com/jobs/search/?f_AL=true&keywords={search_keyword}&location=Remote&start={page * 25}"
            driver.get(next_url)
            print(f"Navigated to: {next_url}")
            time.sleep(random.uniform(10, 20))
        
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_all_elements_located((By.CLASS_NAME, 'job-card-container'))
            )
        except TimeoutException:
            print(f"No jobs found for keyword: {search_keyword} on current page.")
            break

        jobs = driver.find_elements(By.CLASS_NAME, 'job-card-container')
        if not jobs:
            print("No jobs on current page; ending pagination.")
            break

        print(f"Processing page with {len(jobs)} job cards...")
        for job in jobs:
            try:
                stats["jobs_processed"] += 1
                card_text = job.text.lower()
                if "applied" in card_text:
                    print("Skipping job (job-card indicates 'applied').")
                    stats["skipped"] += 1
                    continue

                job_link = job.find_element(By.CSS_SELECTOR, "a")
                job_url = job_link.get_attribute("href") or ""
                if job_url in duplicate_urls:
                    print(f"Skipping duplicate job: {job_url}")
                    stats["skipped"] += 1
                    continue

                driver.execute_script("arguments[0].scrollIntoView(true);", job)
                WebDriverWait(driver, 10).until(EC.element_to_be_clickable(job))
                if not safe_click(driver, job):
                    stats["skipped"] += 1
                    continue
                time.sleep(random.uniform(2, 4))

                try:
                    easy_apply_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'jobs-apply-button')]"))
                    )
                except TimeoutException:
                    print("Easy Apply button not found for this job.")
                    stats["skipped"] += 1
                    continue

                if not safe_click(driver, easy_apply_button):
                    stats["skipped"] += 1
                    continue
                time.sleep(random.uniform(2, 3))

                submitted = handle_multi_step_application(driver, resume_path)
                if not submitted:
                    print("Application was not submitted for this job.")
                    stats["skipped"] += 1
                    continue

                job_title, company = "Unknown Job", "Unknown Company"
                try:
                    job_title_elem = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__job-title'))
                    )
                    job_title = job_title_elem.text.strip() or job_title
                    company_elem = driver.find_element(By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__company-name')
                    company = company_elem.text.strip() or company
                except Exception:
                    print("Could not retrieve job details, using defaults.")

                log_application(job_title, company, job_url, resume_path, 'submitted')
                print(f"Successfully applied to {job_title} at {company}")
                stats["applied"] += 1
                duplicate_urls.add(job_url)
                time.sleep(random.uniform(1, 2))
                update_dashboard()
            except StaleElementReferenceException:
                print("Stale element encountered, skipping this job.")
                stats["skipped"] += 1
                continue
            except Exception as e:
                print(f"Encountered an error with this job: {e}")
                stats["errors"] += 1
                log_error(f"Job error: {e}")
                notify_error(f"Job error: {e}")
        # End of page processing
    update_dashboard()

# ===================== STATS PRINTING =====================
# Prints the current run’s statistics and updates the dashboard.
# Also updates and then prints overall cumulative stats.
def print_stats():
    uptime = time.time() - stats["start_time"]
    applied = stats.get("applied", 0)
    throughput_per_hour = applied / (uptime/3600) if uptime > 0 else 0
    throughput_per_day = applied / (uptime/86400) if uptime > 0 else 0
    print("\n=== Application Stats (This Run) ===")
    print(f"Uptime: {uptime:.2f} seconds")
    print(f"Jobs processed: {stats.get('jobs_processed',0)}")
    print(f"Jobs applied: {applied}")
    print(f"Jobs skipped: {stats.get('skipped',0)}")
    print(f"Errors encountered: {stats.get('errors',0)}")
    print(f"Throughput: {throughput_per_hour:.2f} jobs/hour, {throughput_per_day:.2f} jobs/day")
    update_dashboard()
    overall = update_overall_stats()  # Update and get overall stats
    print("\n=== Overall Cumulative Stats ===")
    print(f"Run Count: {overall['run_count']}")
    print(f"Total Jobs Processed: {overall['jobs_processed']}")
    print(f"Total Jobs Applied: {overall['applied']}")
    print(f"Total Jobs Skipped: {overall['skipped']}")
    print(f"Total Errors: {overall['errors']}")
    print("Overall Network Stats:")
    print(f"  Actions Accepted: {overall['network']['actions_accepted']}")
    print(f"  Actions Sent: {overall['network']['actions_sent']}")
    print(f"  Attempts: {overall['network']['attempts']}")

# ===================== DASHBOARD SERVER =====================
# FastAPI server to serve dashboard data for external monitoring.
app = FastAPI()

@app.get("/")
def read_dashboard():
    try:
        with open("dashboard_data.json", "r") as f:
            data = json.load(f)
    except Exception as e:
        data = {"error": str(e)}
    return data

def run_dashboard():
    uvicorn.run(app, host="127.0.0.1", port=8000)

def launch_dashboard_server():
    # Launch the FastAPI server in a separate thread and open the browser.
    server_thread = threading.Thread(target=run_dashboard, daemon=True)
    server_thread.start()
    webbrowser.open("http://127.0.0.1:8000")
    print("Dashboard server launched at http://127.0.0.1:8000")

# ===================== MENU MODES =====================
# Provides interactive CLI for running different operations.
def menu_mode():
    global config
    while True:
        print("\nLinkedIn Automation")
        print("1. Apply for Jobs")
        print("2. Expand my Network")
        print("3. View Application Log")
        print("4. Update Credentials")
        print("5. Launch Dashboard Server")
        print("6. Quit")
        choice = input("Choose an option: ")
        if choice == '1':
            username = config.get('username') or input("LinkedIn username: ")
            password = config.get('password') or getpass.getpass("LinkedIn password: ")
            browser_choice = config.get('browser', 'chrome')
            resume_path = config.get('resume_path', "")
            if not resume_path:
                resume_path = input("Enter the full path to your resume file: ").strip()
                config['resume_path'] = resume_path
                save_config(config)

            driver = init_browser(browser_choice)
            if linkedin_login(driver, username, password):
                print("Logged in successfully.")
                duplicate_urls = set(log['job_url'] for log in load_application_log() if log.get('job_url'))
                duplicate_urls |= get_applied_jobs(driver)
                search_keywords = [
                    "web developer", ".NET Developer", ".NET Engineer", "C# Developer",
                    "Azure Developer", "Full Stack Developer", "Senior Software Engineer",
                    "Application Developer", "Full Stack Engineer", "Information Technology Consultant"
                ]
                for keyword in search_keywords:
                    print(f"\nSearching for jobs with keyword: {keyword}")
                    apply_for_jobs(driver, resume_path, keyword, duplicate_urls)
            driver.quit()
            print_stats()
        elif choice == '2':
            browser_choice = config.get('browser', 'chrome')
            driver = init_browser(browser_choice)
            username = config.get('username') or input("LinkedIn username: ")
            password = config.get('password') or getpass.getpass("LinkedIn password: ")
            if linkedin_login(driver, username, password):
                expand_network(driver)
            driver.quit()
        elif choice == '3':
            if os.path.exists(APPLICATION_LOG):
                with open(APPLICATION_LOG, 'r') as file:
                    logs = json.load(file)
                    for log_item in logs:
                        print(log_item)
            else:
                print("No applications logged yet.")
        elif choice == '4':
            config['username'] = input("LinkedIn username: ")
            config['password'] = getpass.getpass("LinkedIn password: ")
            new_resume = input("Enter the full path to your resume file: ").strip()
            config['resume_path'] = new_resume
            hf_token = input("Enter your Hugging Face API token (or leave blank to use default): ").strip()
            if hf_token:
                config['hf_api_token'] = hf_token
            # Also ask for ChatGPT/OpenAI API key:
            openai_key = input("Enter your OpenAI API key (for ChatGPT integration): ").strip()
            if openai_key:
                config['openai_api_key'] = openai_key
            save_config(config)
            print("Credentials updated.")
        elif choice == '5':
            launch_dashboard_server()
        elif choice == '6':
            print("Exiting.")
            break
        else:
            print("Invalid choice.")

def direct_apply_mode():
    global AUTO_USE_DEFAULTS, config
    default_pref = input("Auto-use saved defaults for all jobs? (Y/n): ").strip().lower() or "y"
    AUTO_USE_DEFAULTS = (default_pref == "y")
    username = config.get('username') or input("LinkedIn username: ")
    password = config.get('password') or getpass.getpass("LinkedIn password: ")
    browser_choice = config.get('browser', 'chrome')
    resume_path = config.get('resume_path', "")
    if not resume_path:
        resume_path = input("Enter the full path to your resume file: ").strip()
        config['resume_path'] = resume_path
        save_config(config)
    
    while True:
        driver = init_browser(browser_choice)
        if linkedin_login(driver, username, password):
            print("Logged in successfully.")
            duplicate_urls = set(log['job_url'] for log in load_application_log() if log.get('job_url'))
            duplicate_urls |= get_applied_jobs(driver)
            search_keywords = [
                "web developer", ".NET Developer", ".NET Engineer", "C# Developer",
                "Azure Developer", "Full Stack Developer", "Senior Software Engineer",
                "Application Developer", "Full Stack Engineer"
            ]
            for keyword in search_keywords:
                print(f"\nSearching for jobs with keyword: {keyword}")
                apply_for_jobs(driver, resume_path, keyword, duplicate_urls)
        driver.quit()
        print_stats()
        print("Completed a full round of searches. Press Q to quit or any other key to continue...")
        if input().strip().lower() == 'q':
            break

# ===================== MAIN =====================

if __name__ == '__main__':
    config = load_config()
    if len(sys.argv) > 1:
        if sys.argv[1] == "--apply":
            direct_apply_mode()
        elif sys.argv[1] == "--menu":
            menu_mode()
        else:
            print("Unknown parameter. Defaulting to interactive menu mode.")
            menu_mode()
    else:
        menu_mode()
    print("Done.")
    print_stats()
    update_dashboard()
    print("Exiting.")
    sys.exit(0)
