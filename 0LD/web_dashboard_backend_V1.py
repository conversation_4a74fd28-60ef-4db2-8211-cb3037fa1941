#!/usr/bin/env python3
"""
Web Dashboard Backend Module for LinkedIn Automation
Provides API endpoints and real-time monitoring for the web dashboard
"""
import os
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

# Import your main modules
from enhanced_logging import EnhancedLogger
from enhanced_config import LinkedInConfig
from network_maintenance_module import NetworkMaintainer
from article_publishing_module import ContentCreator
from message_autoreply_module import MessageAutoResponder

class DashboardAPI:
    """API backend for the web dashboard"""
    
    def __init__(self):
        self.app = FastAPI(title="LinkedIn Automation API")
        self.config = LinkedInConfig.load()
        self.logger = EnhancedLogger(self.config)
        self.automation_running = False
        self.current_task = None
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_routes()
        
    def _setup_routes(self):
        """Setup all API routes"""
        
        @self.app.get("/")
        async def get_dashboard_stats():
            """Get current dashboard statistics"""
            return self.get_current_stats()
        
        @self.app.get("/applications")
        async def get_applications(limit: int = 50, offset: int = 0):
            """Get job applications with pagination"""
            return self.get_applications_data(limit, offset)
        
        @self.app.get("/network")
        async def get_network_stats():
            """Get network statistics"""
            return self.get_network_data()
        
        @self.app.get("/messages")
        async def get_messages():
            """Get message statistics"""
            return self.get_messages_data()
        
        @self.app.get("/content")
        async def get_content_stats():
            """Get content publishing statistics"""
            return self.get_content_data()
        
        @self.app.get("/config")
        async def get_config():
            """Get current configuration"""
            return self.get_config_summary()
        
        @self.app.post("/config")
        async def update_config(config_update: Dict[str, Any]):
            """Update configuration"""
            return self.update_configuration(config_update)
        
        @self.app.post("/start")
        async def start_automation(background_tasks: BackgroundTasks):
            """Start automation process"""
            if not self.automation_running:
                background_tasks.add_task(self.run_automation)
                return {"status": "started", "message": "Automation started successfully"}
            return {"status": "already_running", "message": "Automation is already running"}
        
        @self.app.post("/stop")
        async def stop_automation():
            """Stop automation process"""
            self.automation_running = False
            if self.current_task:
                self.current_task.cancel()
            return {"status": "stopped", "message": "Automation stopped"}
        
        @self.app.get("/status")
        async def get_status():
            """Get automation status"""
            return {
                "running": self.automation_running,
                "current_activity": self.get_current_activity(),
                "uptime": self.get_uptime()
            }
        
        @self.app.get("/logs")
        async def get_logs(log_type: str = "main", lines: int = 100):
            """Get recent logs"""
            return self.get_recent_logs(log_type, lines)
        
        @self.app.get("/analytics")
        async def get_analytics(period: str = "week"):
            """Get analytics data"""
            return self.get_analytics_data(period)
        
        @self.app.post("/manual/apply")
        async def manual_apply(job_url: str):
            """Manually trigger job application"""
            return await self.apply_to_job(job_url)
        
        @self.app.post("/manual/message")
        async def send_message(recipient: str, message: str):
            """Manually send a message"""
            return await self.send_manual_message(recipient, message)
        
        @self.app.get("/dashboard")
        async def serve_dashboard():
            """Serve the dashboard HTML"""
            return HTMLResponse(content=self.get_dashboard_html())
        
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        stats_file = Path(self.config.paths.data_directory) / "dashboard_data.json"
        
        if stats_file.exists():
            with open(stats_file, 'r') as f:
                stats = json.load(f)
        else:
            stats = {
                "applied": 0,
                "skipped": 0,
                "errors": 0,
                "jobs_processed": 0,
                "network_stats": {
                    "actions_accepted": 0,
                    "actions_sent": 0,
                    "attempts": 0
                }
            }
            
        # Add calculated metrics
        stats["success_rate"] = (
            (stats["applied"] / stats["jobs_processed"] * 100) 
            if stats["jobs_processed"] > 0 else 0
        )
        
        # Add time-based metrics
        stats["today"] = self.get_today_stats()
        stats["this_week"] = self.get_week_stats()
        
        return stats
        
    def get_applications_data(self, limit: int, offset: int) -> Dict[str, Any]:
        """Get applications with pagination"""
        applications = []
        total = 0
        
        # Get all application files
        log_dir = Path(self.config.paths.log_directory)
        
        for run_dir in sorted(log_dir.iterdir(), reverse=True):
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        applications.extend(run_apps)
                        
        total = len(applications)
        
        # Apply pagination
        paginated = applications[offset:offset + limit]
        
        return {
            "applications": paginated,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
        
    def get_network_data(self) -> Dict[str, Any]:
        """Get network statistics and recent actions"""
        network_file = Path(self.config.paths.data_directory) / "network_actions.json"
        
        actions = []
        if network_file.exists():
            with open(network_file, 'r') as f:
                actions = json.load(f)
                
        # Calculate statistics
        stats = {
            "total_connections": len([a for a in actions if a["action_type"] == "connect"]),
            "invitations_accepted": len([a for a in actions if a["action_type"] == "accept"]),
            "messages_sent": len([a for a in actions if a["action_type"].startswith("message_")]),
            "recent_actions": actions[-20:],  # Last 20 actions
            "growth_rate": self.calculate_growth_rate(actions)
        }
        
        return stats
        
    def get_messages_data(self) -> Dict[str, Any]:
        """Get message statistics"""
        messages_file = Path(self.config.paths.data_directory) / "processed_conversations.json"
        
        if messages_file.exists():
            with open(messages_file, 'r') as f:
                data = json.load(f)
        else:
            data = {"conversations": []}
            
        # Get response tracking
        tracking_file = Path(self.config.paths.data_directory) / "response_tracking.json"
        tracking_data = {"responses": []}
        
        if tracking_file.exists():
            with open(tracking_file, 'r') as f:
                tracking_data = json.load(f)
                
        # Calculate metrics
        total_responses = len(tracking_data["responses"])
        got_replies = len([r for r in tracking_data["responses"] if r.get("got_reply")])
        
        return {
            "total_conversations": len(data["conversations"]),
            "auto_responses_sent": total_responses,
            "response_rate": (got_replies / total_responses * 100) if total_responses > 0 else 0,
            "job_inquiries": len([r for r in tracking_data["responses"] if r["response_type"] == "job_inquiry"]),
            "last_check": data.get("last_updated", "Never")
        }
        
    def get_content_data(self) -> Dict[str, Any]:
        """Get content publishing statistics"""
        content_file = Path(self.config.paths.log_directory) / "published_content.json"
        performance_file = Path(self.config.paths.data_directory) / "content_performance.json"
        
        published = []
        if content_file.exists():
            with open(content_file, 'r') as f:
                published = json.load(f)
                
        performance = []
        if performance_file.exists():
            with open(performance_file, 'r') as f:
                performance = json.load(f)
                
        # Calculate metrics
        total_posts = len(published)
        posts_by_type = {}
        
        for post in published:
            post_type = post.get("content_type", "post")
            posts_by_type[post_type] = posts_by_type.get(post_type, 0) + 1
            
        return {
            "total_posts": total_posts,
            "posts_by_type": posts_by_type,
            "recent_posts": published[-10:],
            "performance_metrics": performance[-5:],
            "best_performing": self.get_best_performing_content(performance)
        }
        
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        return {
            "username": self.config.username,
            "has_password": bool(self.config.password),
            "has_api_key": bool(self.config.ai.openai_api_key),
            "search_keywords": self.config.search.keywords,
            "easy_apply_only": self.config.search.easy_apply_only,
            "auto_reply_enabled": self.config.auto_reply.enabled,
            "browser": self.config.browser.browser_type,
            "max_applications": self.config.application.max_applications_per_run,
            "max_connections": self.config.network.max_connections_per_run,
            "delays": {
                "page_load": self.config.delays.page_load,
                "between_applications": self.config.delays.between_applications
            }
        }
        
    def update_configuration(self, config_update: Dict[str, Any]) -> Dict[str, str]:
        """Update configuration settings"""
        try:
            # Update search keywords
            if "search_keywords" in config_update:
                self.config.search.keywords = config_update["search_keywords"]
                
            # Update limits
            if "max_applications" in config_update:
                self.config.application.max_applications_per_run = config_update["max_applications"]
                
            if "max_connections" in config_update:
                self.config.network.max_connections_per_run = config_update["max_connections"]
                
            if "auto_reply_enabled" in config_update:
                self.config.auto_reply.enabled = config_update["auto_reply_enabled"]
                
            # Save configuration
            self.config.save()
            
            return {"status": "success", "message": "Configuration updated"}
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
            
    async def run_automation(self):
        """Main automation loop"""
        self.automation_running = True
        self.logger.start_run()
        
        try:
            while self.automation_running:
                # Job applications
                if self.config.application.auto_use_defaults:
                    await self.run_job_applications()
                    
                # Network maintenance
                await self.run_network_maintenance()
                
                # Message checking
                if self.config.auto_reply.enabled:
                    await self.check_messages()
                    
                # Content creation (if scheduled)
                await self.check_content_schedule()
                
                # Wait before next cycle
                await asyncio.sleep(self.config.auto_reply.check_interval_minutes * 60)
                
        except asyncio.CancelledError:
            self.logger.info("Automation cancelled")
        finally:
            self.automation_running = False
            self.logger.end_run()
            
    def get_today_stats(self) -> Dict[str, int]:
        """Get today's statistics"""
        today = datetime.now().date()
        stats = {"applications": 0, "connections": 0, "messages": 0}
        
        # Count today's activities
        # This would need to parse logs with timestamps
        
        return stats
        
    def get_week_stats(self) -> Dict[str, Any]:
        """Get this week's statistics"""
        week_start = datetime.now() - timedelta(days=datetime.now().