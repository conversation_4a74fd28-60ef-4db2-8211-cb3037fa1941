#!/usr/bin/env python3
"""
Complete Enhanced LinkedIn Automation Script
- Integrated enhanced logging with timestamps and detailed tracking
- All configuration externalized to JSON
- Full job description and cover letter logging
- Network expansion tracking
"""
import logging
import json
import os
import time
import random
import getpass
import sys
import threading
import webbrowser
import requests
import uvicorn
from datetime import datetime
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from abc import ABC, abstractmethod
from pathlib import Path

# Selenium imports
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException,
    ElementClickInterceptedException, StaleElementReferenceException,
    ElementNotInteractableException, WebDriverException, InvalidSessionIdException
)
from selenium.webdriver.common.action_chains import ActionChains
from fastapi import FastAPI

# ===================== ENHANCED CONFIGURATION =====================
@dataclass
class DelayConfig:
    """All delay/timing configurations"""
    page_load: float = 10.0
    between_applications: Tuple[float, float] = (2, 5)
    form_fill_delay: float = 0.5
    click_delay: float = 0.3
    typing_delay: float = 0.1
    network_action_delay: Tuple[float, float] = (1, 3)
    scroll_delay: float = 0.5
    modal_wait: float = 2.0
    
@dataclass
class PathConfig:
    """All file path configurations"""
    resume_path: str = ""
    cover_letter_template: str = ""
    log_directory: str = "linkedin_logs"
    screenshot_directory: str = "screenshots"
    data_directory: str = "data"
    
@dataclass
class SearchConfig:
    """Job search configurations"""
    keywords: List[str] = field(default_factory=lambda: [
        "web developer", ".NET Developer", "C# Developer", 
        "Full Stack Developer", "Software Engineer"
    ])
    locations: List[str] = field(default_factory=lambda: ["Remote", "United States"])
    experience_levels: List[str] = field(default_factory=lambda: [
        "Entry level", "Associate", "Mid-Senior level"
    ])
    job_types: List[str] = field(default_factory=lambda: ["Full-time", "Contract"])
    date_posted: str = "Past month"
    easy_apply_only: bool = True
    
@dataclass
class NetworkConfig:
    """Network expansion configurations"""
    max_connections_per_run: int = 50
    max_invitations_accept: int = 100
    connection_message_templates: List[str] = field(default_factory=lambda: [
        "Hi {name}, I'd love to connect and expand our professional networks!",
        "Hello {name}, I came across your profile and would like to connect.",
    ])
    
@dataclass
class AIConfig:
    """AI provider configurations"""
    openai_api_key: str = ""
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 500
    use_ai_for_cover_letters: bool = True
    use_ai_for_form_fields: bool = True
    
@dataclass
class BrowserConfig:
    """Browser configurations"""
    browser_type: str = "chrome"
    headless: bool = False
    window_size: Tuple[int, int] = (1920, 1080)
    user_agent: Optional[str] = None
    chrome_binary_path: str = "/usr/bin/chromium-browser"
    chromedriver_path: str = "/usr/bin/chromedriver"
    
@dataclass
class ApplicationConfig:
    """Application behavior configurations"""
    auto_use_defaults: bool = False
    skip_applied_jobs: bool = True
    max_applications_per_run: int = 100
    save_job_descriptions: bool = True
    take_screenshots_on_error: bool = True

@dataclass
class LinkedInConfig:
    """Main configuration class"""
    username: str = ""
    password: str = ""
    delays: DelayConfig = field(default_factory=DelayConfig)
    paths: PathConfig = field(default_factory=PathConfig)
    search: SearchConfig = field(default_factory=SearchConfig)
    network: NetworkConfig = field(default_factory=NetworkConfig)
    ai: AIConfig = field(default_factory=AIConfig)
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    application: ApplicationConfig = field(default_factory=ApplicationConfig)
    
    @classmethod
    def load(cls, config_file: str = "linkedin_config.json") -> 'LinkedInConfig':
        """Load configuration from JSON file"""
        if not os.path.exists(config_file):
            config = cls()
            config.save(config_file)
            return config
            
        with open(config_file, 'r') as f:
            data = json.load(f)
            
        # Convert nested dictionaries to dataclass instances
        for attr in ['delays', 'paths', 'search', 'network', 'ai', 'browser', 'application']:
            if attr in data and isinstance(data[attr], dict):
                attr_class = cls.__annotations__[attr].__args__[0]
                data[attr] = attr_class(**data[attr])
                
        return cls(**data)
        
    def save(self, config_file: str = "linkedin_config.json"):
        """Save configuration to JSON file"""
        data = asdict(self)
        with open(config_file, 'w') as f:
            json.dump(data, f, indent=2)
            
    def create_directories(self):
        """Create all configured directories"""
        for path_attr in ['log_directory', 'screenshot_directory', 'data_directory']:
            path = getattr(self.paths, path_attr)
            if path:
                Path(path).mkdir(parents=True, exist_ok=True)

# ===================== ENHANCED LOGGING =====================
@dataclass
class JobApplication:
    """Detailed job application record"""
    job_id: str
    job_title: str
    company: str
    location: str
    job_url: str
    job_description_summary: str
    full_description: str
    resume_used: str
    cover_letter: str
    applied_date: str
    status: str
    
@dataclass
class NetworkAction:
    """Network action record"""
    action_type: str
    profile_name: str
    profile_url: str
    message_sent: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    status: str = 'success'

class EnhancedLogger:
    """Enhanced logging system with detailed tracking"""
    
    def __init__(self, config: LinkedInConfig):
        self.config = config
        self.base_dir = Path(config.paths.log_directory)
        self.base_dir.mkdir(exist_ok=True)
        
        # Create run-specific directory
        self.run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.base_dir / f"run_{self.run_id}"
        self.run_dir.mkdir(exist_ok=True)
        
        # Setup loggers
        self.setup_loggers()
        
        # Track current run stats
        self.run_stats = {
            "run_id": self.run_id,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "jobs_applied": 0,
            "jobs_skipped": 0,
            "errors": 0,
            "network_actions": 0
        }
        
    def setup_loggers(self):
        """Setup different logger instances"""
        formatter = logging.Formatter(
            '%(asctime)s | %(name)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Main logger
        self.main_logger = logging.getLogger('main')
        self.main_logger.setLevel(logging.INFO)
        self.main_logger.handlers = []
        
        # Add handlers
        main_handler = logging.FileHandler(self.run_dir / 'main.log')
        main_handler.setFormatter(formatter)
        self.main_logger.addHandler(main_handler)
        
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.main_logger.addHandler(console_handler)
        
    def info(self, message: str):
        self.main_logger.info(message)
        
    def error(self, message: str):
        self.main_logger.error(message)
        self.run_stats["errors"] += 1
        
    def warning(self, message: str):
        self.main_logger.warning(message)
        
    def start_run(self):
        """Mark the start of a new run"""
        self.info(f"{'='*60}")
        self.info(f"STARTING NEW RUN - ID: {self.run_id}")
        self.info(f"{'='*60}")
        
    def end_run(self):
        """Mark the end of a run and save summary"""
        self.run_stats["end_time"] = datetime.now().isoformat()
        
        # Save run summary
        summary_file = self.run_dir / 'run_summary.json'
        with open(summary_file, 'w') as f:
            json.dump(self.run_stats, f, indent=2)
            
        self.info(f"{'='*60}")
        self.info(f"RUN COMPLETE - ID: {self.run_id}")
        self.info(f"Jobs Applied: {self.run_stats['jobs_applied']}")
        self.info(f"Jobs Skipped: {self.run_stats['jobs_skipped']}")
        self.info(f"Errors: {self.run_stats['errors']}")
        self.info(f"{'='*60}")
        
    def log_job_application(self, job: JobApplication):
        """Log a detailed job application"""
        self.run_stats["jobs_applied"] += 1
        
        # Save to JSON
        apps_file = self.run_dir / 'applications.json'
        apps = []
        if apps_file.exists():
            with open(apps_file, 'r') as f:
                apps = json.load(f)
                
        apps.append(asdict(job))
        with open(apps_file, 'w') as f:
            json.dump(apps, f, indent=2)
            
        self.info(f"Applied to: {job.job_title} at {job.company}")
        
    def log_skipped_job(self, job_url: str, reason: str, job_title: str = "", company: str = ""):
        """Log a skipped job"""
        self.run_stats["jobs_skipped"] += 1
        self.info(f"Skipped: {job_title or job_url} - Reason: {reason}")
        
    def log_network_action(self, action: NetworkAction):
        """Log a network action"""
        self.run_stats["network_actions"] += 1
        self.info(f"{action.action_type}: {action.profile_name}")

# ===================== STATISTICS =====================
@dataclass
class Statistics:
    """Statistics tracking for current run"""
    applied: int = 0
    skipped: int = 0
    errors: int = 0
    jobs_processed: int = 0
    start_time: float = field(default_factory=time.time)
    
@dataclass
class NetworkStatistics:
    """Network expansion statistics"""
    actions_accepted: int = 0
    actions_sent: int = 0
    attempts: int = 0

class StatsManager:
    """Manages overall statistics"""
    def __init__(self, config: LinkedInConfig):
        self.config = config
        self.current_stats = Statistics()
        self.network_stats = NetworkStatistics()
        
    def update_dashboard(self):
        """Update dashboard data file"""
        dashboard_file = Path(self.config.paths.data_directory) / "dashboard_data.json"
        dashboard_data = {
            "applied": self.current_stats.applied,
            "skipped": self.current_stats.skipped,
            "errors": self.current_stats.errors,
            "jobs_processed": self.current_stats.jobs_processed,
            "uptime": time.time() - self.current_stats.start_time,
            "network_stats": {
                "actions_accepted": self.network_stats.actions_accepted,
                "actions_sent": self.network_stats.actions_sent,
                "attempts": self.network_stats.attempts
            }
        }
        dashboard_file.parent.mkdir(exist_ok=True)
        with open(dashboard_file, "w") as f:
            json.dump(dashboard_data, f, indent=4)

# ===================== DATA PERSISTENCE =====================
class DataManager:
    """Handles all data persistence operations"""
    def __init__(self, config: LinkedInConfig, logger: EnhancedLogger):
        self.config = config
        self.logger = logger
        self.data_dir = Path(config.paths.data_directory)
        self.data_dir.mkdir(exist_ok=True)
        
    def load_questions(self) -> Dict[str, str]:
        """Load saved question answers"""
        questions_file = self.data_dir / "questions_log.json"
        if questions_file.exists():
            with open(questions_file, 'r') as f:
                return json.load(f)
        return {}
        
    def save_questions(self, questions: Dict[str, str]):
        """Save question answers"""
        questions_file = self.data_dir / "questions_log.json"
        with open(questions_file, 'w') as f:
            json.dump(questions, f, indent=4)
            
    def log_application(self, job_title: str, company: str, job_url: str, 
                       resume_used: str, status: str, job_description: str = "",
                       cover_letter: str = ""):
        """Enhanced application logging"""
        job_app = JobApplication(
            job_id=job_url.split('/')[-1] if job_url else "unknown",
            job_title=job_title,
            company=company,
            location="",
            job_url=job_url,
            job_description_summary=job_description[:500] if job_description else "",
            full_description=job_description,
            resume_used=resume_used,
            cover_letter=cover_letter,
            applied_date=datetime.now().isoformat(),
            status=status
        )
        self.logger.log_job_application(job_app)
        
    def get_applied_job_urls(self) -> Set[str]:
        """Get set of applied job URLs"""
        urls = set()
        
        # Check all run directories
        for run_dir in self.logger.base_dir.iterdir():
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        apps = json.load(f)
                        for app in apps:
                            if app.get('job_url'):
                                urls.add(app['job_url'])
        return urls
        
    def log_skipped(self, job_url: str, reason: str, job_title: str = "", company: str = ""):
        """Log skipped job"""
        self.logger.log_skipped_job(job_url, reason, job_title, company)
        
    def log_network_action(self, action_type: str, profile_name: str, 
                          profile_url: str, message: str = ""):
        """Log network actions"""
        action = NetworkAction(
            action_type=action_type,
            profile_name=profile_name,
            profile_url=profile_url,
            message_sent=message
        )
        self.logger.log_network_action(action)

# ===================== AI INTEGRATION =====================
class ChatGPTProvider:
    """OpenAI ChatGPT integration"""
    def __init__(self, config: AIConfig):
        self.config = config
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
    def generate_answer(self, field_label: str, field_type: str, context: str = "") -> str:
        """Generate answer using ChatGPT"""
        if not self.config.openai_api_key:
            return f"Default answer for {field_label}"
            
        prompt = (
            f"Provide a concise, professional answer for the following LinkedIn job application field.\n"
            f"Field: {field_label}\nType: {field_type}\n"
        )
        if context:
            prompt += f"Context: {context}\n"
        prompt += "Answer:"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.openai_api_key}"
        }
        
        payload = {
            "model": self.config.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.config.temperature,
            "max_tokens": 200
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, 
                                   json=payload, timeout=15)
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"].strip()
        except Exception:
            pass
        return f"Default answer for {field_label}"
        
    def generate_cover_letter(self, job_title: str, company: str, 
                            job_description: str, resume_text: str = "") -> str:
        """Generate a professional cover letter using ChatGPT"""
        if not self.config.openai_api_key:
            return f"Dear {company}, I am very interested in the {job_title} position."
            
        prompt = (
            f"Write a professional cover letter for the position of '{job_title}' at '{company}'.\n"
            f"Job Description:\n{job_description[:1000]}\n\n"
        )
        if resume_text:
            prompt += f"Candidate Resume Excerpt:\n{resume_text[:500]}\n\n"
        prompt += "The letter should be tailored, concise, and compelling.\n\nCover Letter:"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.openai_api_key}"
        }
        
        payload = {
            "model": self.config.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, 
                                   json=payload, timeout=20)
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"].strip()
        except Exception:
            pass
        return f"Dear {company}, I am very interested in the {job_title} position."

# ===================== BROWSER AUTOMATION =====================
class BrowserManager:
    """Manages browser operations"""
    def __init__(self, config: LinkedInConfig, logger: EnhancedLogger):
        self.config = config
        self.logger = logger
        self.driver = None
        self.wait_time = config.delays.page_load
        
    def init_browser(self) -> webdriver.Remote:
        """Initialize browser driver"""
        browser_config = self.config.browser
        
        if browser_config.browser_type.lower() == 'chrome':
            options = webdriver.ChromeOptions()
            
            if browser_config.chrome_binary_path:
                options.binary_location = browser_config.chrome_binary_path
                
            if browser_config.headless:
                options.add_argument('--headless')
                
            if browser_config.window_size:
                options.add_argument(f'--window-size={browser_config.window_size[0]},{browser_config.window_size[1]}')
                
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument('--disable-blink-features=AutomationControlled')
            
            self.logger.info(f"Using browser binary at {browser_config.chrome_binary_path}")
            
            if browser_config.chromedriver_path:
                service = Service(browser_config.chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=options)
            else:
                self.driver = webdriver.Chrome(options=options)
                
        elif browser_config.browser_type.lower() == 'edge':
            self.driver = webdriver.Edge()
        else:
            self.driver = webdriver.Chrome()
            
        self.driver.implicitly_wait(self.wait_time)
        return self.driver
        
    def login(self, username: str, password: str) -> bool:
        """Login to LinkedIn"""
        try:
            self.driver.get("https://www.linkedin.com/login")
            WebDriverWait(self.driver, self.wait_time).until(
                EC.presence_of_element_located((By.ID, 'username'))
            )
            
            # Type with delay
            username_field = self.driver.find_element(By.ID, 'username')
            for char in username:
                username_field.send_keys(char)
                time.sleep(self.config.delays.typing_delay)
                
            password_field = self.driver.find_element(By.ID, 'password')
            for char in password:
                password_field.send_keys(char)
                time.sleep(self.config.delays.typing_delay)
                
            password_field.send_keys(Keys.RETURN)
            
            WebDriverWait(self.driver, 20).until(EC.url_contains('feed'))
            self.logger.info("Successfully logged in to LinkedIn")
            return True
            
        except TimeoutException:
            self.logger.error("Login failed: timeout")
            return False
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
            
    def safe_click(self, element, retries: int = 3) -> bool:
        """Click an element with retries"""
        delay = self.config.delays.click_delay
        
        for attempt in range(retries):
            try:
                element.click()
                time.sleep(delay)
                return True
            except (ElementClickInterceptedException, ElementNotInteractableException):
                time.sleep(delay)
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            except StaleElementReferenceException:
                return False
            except Exception as e:
                self.logger.error(f"Click error on attempt {attempt+1}: {e}")
                time.sleep(delay)
        return False
        
    def take_screenshot(self, label: str = "error"):
        """Take and save screenshot"""
        if not self.driver:
            return
            
        screenshot_dir = Path(self.config.paths.screenshot_directory)
        screenshot_dir.mkdir(exist_ok=True)
        
        filename = screenshot_dir / f"screenshot_{label}_{datetime.now():%Y%m%d_%H%M%S}.png"
        self.driver.save_screenshot(str(filename))
        self.logger.info(f"Screenshot saved: {filename}")
        
    def quit(self):
        """Quit browser"""
        if self.driver:
            self.driver.quit()

# ===================== FORM FILLING =====================
class FormFiller:
    """Handles form filling operations"""
    def __init__(self, browser_manager: BrowserManager, ai_provider: ChatGPTProvider,
                 data_manager: DataManager, config: LinkedInConfig, logger: EnhancedLogger):
        self.browser = browser_manager
        self.ai = ai_provider
        self.data = data_manager
        self.config = config
        self.logger = logger
        self.last_cover_letter = ""  # Store for logging
        
    def fill_text_field(self, input_field, label: str, questions: Dict[str, str]) -> str:
        """Fill a text input field"""
        if "cover" in label.lower():
            # Generate cover letter
            job_title = self._get_job_title()
            company = self._get_company_name()
            job_description = self._get_job_description_from_page()
            resume_excerpt = self._get_resume_excerpt()
            
            answer = self.ai.generate_cover_letter(job_title, company, 
                                                  job_description, resume_excerpt)
            self.last_cover_letter = answer  # Store for logging
            self.logger.info(f"Generated cover letter for {job_title} at {company}")
        else:
            # Check for saved answer
            if label in questions and questions[label].strip():
                answer = questions[label].strip()
                self.logger.info(f"Using saved answer for '{label}'")
            else:
                # Check for prefilled value
                prefilled = input_field.get_attribute("value").strip()
                if prefilled:
                    if self.config.application.auto_use_defaults:
                        answer = prefilled
                        self.logger.info(f"Using prefilled value for '{label}'")
                    else:
                        use_prefilled = input(
                            f"Field '{label}' is prefilled with '{prefilled}'. "
                            f"Use this? (Y/n): "
                        ).strip().lower() or "y"
                        
                        if use_prefilled == "y":
                            answer = prefilled
                        else:
                            suggestion = self.ai.generate_answer(label, "text")
                            print(f"AI suggestion: {suggestion}")
                            answer = input(f"Enter answer for '{label}': ").strip() or suggestion
                else:
                    # No prefilled value, get AI suggestion
                    if self.config.ai.use_ai_for_form_fields:
                        suggestion = self.ai.generate_answer(label, "text")
                        print(f"\nField: {label}")
                        print(f"AI suggestion: {suggestion}")
                        answer = input(f"Enter answer for '{label}': ").strip() or suggestion
                    else:
                        answer = input(f"Enter answer for '{label}': ").strip()
            
            questions[label] = answer
        
        # Fill the field
        try:
            input_field.clear()
            for char in answer:
                input_field.send_keys(char)
                time.sleep(self.config.delays.typing_delay)
                
            # Handle autocomplete suggestions
            try:
                suggestion_elem = WebDriverWait(self.browser.driver, 3).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "li.artdeco-typeahead__hit"))
                )
                suggestion_elem.click()
                self.logger.info(f"Selected autocomplete suggestion for '{label}'")
            except TimeoutException:
                pass
                
        except ElementNotInteractableException:
            self.logger.warning(f"Field '{label}' is not interactable")
            
        return answer
        
    def fill_form(self):
        """Fill all form fields in the Easy Apply modal"""
        questions = self.data.load_questions()
        
        try:
            form_section = self.browser.driver.find_element(
                By.CSS_SELECTOR, "div.jobs-easy-apply-modal"
            )
        except NoSuchElementException:
            self.logger.warning("Easy Apply modal not found")
            return
            
        # Process text inputs
        text_inputs = form_section.find_elements(
            By.CSS_SELECTOR,
            "input[type='text'], input[type='number'], input[type='tel'], "
            "input[type='email'], textarea"
        )
        
        for input_field in text_inputs:
            label = self._get_field_label(input_field)
            self.fill_text_field(input_field, label, questions)
            time.sleep(self.config.delays.form_fill_delay)
            
        # Process dropdowns
        self._fill_dropdowns(form_section, questions)
        
        # Process checkboxes
        self._fill_checkboxes(form_section, questions)
        
        # Process radio buttons
        self._fill_radio_buttons(form_section, questions)
        
        # Save updated questions
        self.data.save_questions(questions)
        
    def _get_field_label(self, field) -> str:
        """Extract and normalize the label text for a field"""
        try:
            label_elem = field.find_element(
                By.XPATH, "./ancestor::div[label]//label"
            )
            return label_elem.text.strip().replace('\n', ' ').splitlines()[0]
        except NoSuchElementException:
            return "Unnamed field"
            
    def _fill_dropdowns(self, form_section, questions: Dict[str, str]):
        """Fill dropdown fields"""
        selects = form_section.find_elements(By.TAG_NAME, "select")
        
        for sel in selects:
            label = self._get_field_label(sel)
            select_obj = Select(sel)
            
            try:
                current_sel = select_obj.first_selected_option.text.strip()
            except Exception:
                current_sel = ""
                
            if label in questions and questions[label].strip():
                answer = questions[label].strip()
                self.logger.info(f"Using saved choice for '{label}': {answer}")
            elif current_sel and current_sel.lower() not in ["select an option", ""]:
                if self.config.application.auto_use_defaults:
                    answer = current_sel
                    self.logger.info(f"Using prefilled dropdown for '{label}'")
                else:
                    options = [o.text for o in select_obj.options if o.text.strip()]
                    answer = self._prompt_dropdown_choice(label, current_sel, options)
                questions[label] = answer
            else:
                options = [o.text for o in select_obj.options if o.text.strip()]
                answer = self._prompt_dropdown_choice(label, "", options)
                questions[label] = answer
                
            try:
                select_obj.select_by_visible_text(answer)
            except Exception:
                select_obj.select_by_index(0)
                
    def _prompt_dropdown_choice(self, label: str, current: str, 
                               options: List[str]) -> str:
        """Prompt user for dropdown choice"""
        if current:
            use_current = input(
                f"Dropdown '{label}' is set to '{current}'. Keep it? (Y/n): "
            ).strip().lower() or "y"
            if use_current == "y":
                return current
                
        print(f"\nDropdown: {label}")
        for idx, opt in enumerate(options):
            print(f"{idx}: {opt}")
            
        choice_str = input(f"Select option index: ").strip()
        try:
            return options[int(choice_str)]
        except (ValueError, IndexError):
            return options[0] if options else ""
            
    def _fill_checkboxes(self, form_section, questions: Dict[str, str]):
        """Fill checkbox fields"""
        checkboxes = form_section.find_elements(
            By.CSS_SELECTOR, "input[type='checkbox']"
        )
        
        for checkbox in checkboxes:
            label = self._get_field_label(checkbox)
            
            # Auto-check "follow" checkboxes
            if "follow" in label.lower():
                if not checkbox.is_selected():
                    self.browser.safe_click(checkbox)
                    self.logger.info(f"Auto-checked '{label}'")
                continue
                
            current = checkbox.is_selected()
            
            if label in questions:
                keep = (questions[label].lower() == 'true')
                self.logger.info(f"Using saved preference for '{label}': {keep}")
            else:
                if self.config.application.auto_use_defaults:
                    keep = current
                    self.logger.info(f"Keeping checkbox '{label}' as is")
                else:
                    if current:
                        keep = input(
                            f"Checkbox '{label}' is checked. Keep it? (Y/n): "
                        ).strip().lower() != "n"
                    else:
                        keep = input(
                            f"Checkbox '{label}' is unchecked. Check it? (Y/n): "
                        ).strip().lower() == "y"
                        
                questions[label] = str(keep)
                
            if keep and not current:
                self.browser.safe_click(checkbox)
            elif not keep and current:
                self.browser.safe_click(checkbox)
                
    def _fill_radio_buttons(self, form_section, questions: Dict[str, str]):
        """Fill radio button fields"""
        radios = form_section.find_elements(
            By.CSS_SELECTOR, "input[type='radio']"
        )
        
        if not radios:
            return
            
        # Group radios by name
        radio_groups = {}
        for r in radios:
            name = r.get_attribute("name") or "radio-group"
            radio_groups.setdefault(name, []).append(r)
            
        for group_name, group_radios in radio_groups.items():
            labels_list = []
            preselected = None
            
            for idx, r in enumerate(group_radios):
                lbl = self._get_field_label(r)
                if lbl == "Unnamed field":
                    lbl = f"Option {idx + 1}"
                labels_list.append(lbl)
                if r.is_selected():
                    preselected = lbl
                    
            if group_name in questions:
                chosen = questions[group_name]
                self.logger.info(f"Using saved choice for '{group_name}': {chosen}")
            elif preselected:
                chosen = preselected
                questions[group_name] = preselected
                self.logger.info(f"Using preselected radio: {preselected}")
            else:
                print(f"\nRadio group: {group_name}")
                for idx, lbl in enumerate(labels_list):
                    print(f"{idx}: {lbl}")
                    
                idx_str = input("Select option index: ").strip()
                try:
                    chosen = labels_list[int(idx_str)]
                except (ValueError, IndexError):
                    chosen = labels_list[0] if labels_list else ""
                    
                questions[group_name] = chosen
                
            # Click the chosen radio
            for r, lbl in zip(group_radios, labels_list):
                if lbl == chosen:
                    self.browser.safe_click(r)
                    break
                    
    def _get_job_description_from_page(self) -> str:
        """Extract job description text from the LinkedIn job view"""
        try:
            desc_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-description__content"))
            )
            return desc_elem.text.strip()
        except Exception:
            self.logger.warning("Failed to extract job description.")
            return ""
            
    def _get_resume_excerpt(self, lines: int = 20) -> str:
        """Load the first N lines of the resume as plain text excerpt"""
        try:
            if not os.path.exists(self.config.paths.resume_path):
                self.logger.warning("Resume file does not exist.")
                return ""
            with open(self.config.paths.resume_path, "r", encoding="utf-8") as f:
                return "\n".join([next(f).strip() for _ in range(lines)])
        except Exception as e:
            self.logger.warning(f"Failed to read resume excerpt: {e}")
            return ""
            
    def _get_job_title(self) -> str:
        try:
            elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__job-title'
            )
            return elem.text.strip()
        except Exception:
            return "Unknown Job"
            
    def _get_company_name(self) -> str:
        try:
            elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__company-name'
            )
            return elem.text.strip()
        except Exception:
            return "Unknown Company"

# ===================== JOB APPLICATION MANAGER =====================
class JobApplicationManager:
    """Manages the job application process"""
    def __init__(self, browser_manager: BrowserManager, form_filler: FormFiller,
                 data_manager: DataManager, stats_manager: StatsManager,
                 config: LinkedInConfig, logger: EnhancedLogger):
        self.browser = browser_manager
        self.form_filler = form_filler
        self.data = data_manager
        self.stats = stats_manager
        self.config = config
        self.logger = logger
        self.search_keywords = config.search.keywords
        
    def _wait_for_page_load(self, timeout: int = 10):
        """Wait for page to be fully loaded"""
        try:
            WebDriverWait(self.browser.driver, timeout).until_not(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, ".artdeco-spinner, .initial-load-animation")
                )
            )
            time.sleep(1)
        except TimeoutException:
            pass
            
    def get_applied_jobs_from_linkedin(self) -> Set[str]:
        """Retrieve applied jobs from LinkedIn saved-jobs"""
        applied_jobs = set()
        try:
            url = "https://www.linkedin.com/my-items/saved-jobs/?cardType=APPLIED"
            self.logger.info(f"Loading applied-jobs page: {url}")
            self.browser.driver.get(url)
            WebDriverWait(self.browser.driver, 10).until(
                EC.presence_of_all_elements_located(
                    (By.CSS_SELECTOR, "ul.jobs-saved-jobs__list li a.job-card-list__title")
                )
            )
            elems = self.browser.driver.find_elements(
                By.CSS_SELECTOR, "ul.jobs-saved-jobs__list li a.job-card-list__title"
            )
            for e in elems:
                href = e.get_attribute("href")
                if href:
                    applied_jobs.add(href)
        except TimeoutException as e:
            self.logger.error(f"Could not retrieve applied jobs (TimeoutException): {e}")
        except Exception as e:
            self.logger.error(f"Could not retrieve applied jobs: {e}")
        return applied_jobs
        
    def handle_application_popup(self):
        """Close the application confirmation popup"""
        popup_xpaths = [
            "//button[contains(text(), 'Done')]",
            "//button[@aria-label='Dismiss']",
            "button.artdeco-modal__dismiss"
        ]
        
        for xpath in popup_xpaths:
            try:
                button = WebDriverWait(self.browser.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, xpath))
                )
                button.click()
                self.logger.info("Closed application popup")
                break
            except TimeoutException:
                continue
                
        # Wait for modal to disappear
        try:
            WebDriverWait(self.browser.driver, 5).until(
                EC.invisibility_of_element_located(
                    (By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
                )
            )
        except TimeoutException:
            self.logger.warning("Modal did not close in time")
            
    def handle_multi_step_application(self, resume_path: str, job_description: str) -> bool:
        """Enhanced multi-step application handling"""
        max_steps = 10
        
        for step in range(max_steps):
            self.logger.info(f"Step {step + 1} of Easy Apply")
            time.sleep(self.config.delays.modal_wait)
            
            # Try resume upload on first step
            if step == 0:
                self._try_resume_upload(resume_path)
                
            # Fill any form fields on this step
            self.form_filler.fill_form()
            
            # Check for Review button
            if self._click_button_by_text("Review"):
                time.sleep(self.config.delays.modal_wait)
                continue
                
            # Check for Submit button
            if self._click_submit_button():
                return True
                
            # Check for Next/Continue button
            if self._click_next_button():
                time.sleep(self.config.delays.modal_wait)
                continue
                
            # If we can't find any buttons, check if modal is still open
            try:
                modal = self.browser.driver.find_element(
                    By.CSS_SELECTOR, "div.jobs-easy-apply-modal"
                )
                if not modal.is_displayed():
                    return True
            except NoSuchElementException:
                return True
                
        return False
        
    def _click_button_by_text(self, text: str) -> bool:
        """Generic method to click button by text"""
        try:
            btn = WebDriverWait(self.browser.driver, 3).until(
                EC.element_to_be_clickable(
                    (By.XPATH, f"//button[contains(., '{text}')]")
                )
            )
            self.browser.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center'});", btn
            )
            time.sleep(self.config.delays.scroll_delay)
            btn.click()
            self.logger.info(f"Clicked '{text}' button")
            return True
        except:
            return False
            
    def _try_resume_upload(self, resume_path: str):
        """Upload resume with better error handling"""
        driver = self.browser.driver
        self.logger.info(f"Attempting resume upload from: {resume_path}")
        
        if not os.path.exists(resume_path):
            self.logger.error(f"Resume file not found: {resume_path}")
            return False
            
        time.sleep(2)
        
        try:
            file_inputs = driver.find_elements(
                By.CSS_SELECTOR,
                "div.jobs-easy-apply-modal input[type='file']"
            )
            
            if not file_inputs:
                self.logger.warning("No file upload inputs found")
                return False
                
            for upload_input in file_inputs:
                try:
                    driver.execute_script("arguments[0].style.display = 'block';", upload_input)
                    upload_input.send_keys(os.path.abspath(resume_path))
                    self.logger.info("Resume uploaded successfully")
                    return True
                except Exception as e:
                    continue
                    
        except Exception as e:
            self.logger.error(f"Resume upload failed: {e}")
            
        return False
        
    def _click_next_button(self) -> bool:
        """Click the 'Next' or 'Continue to next step' button"""
        driver = self.browser.driver
        try:
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (By.CSS_SELECTOR,
                     "button[data-live-test-easy-apply-next-button],"
                     "button[data-easy-apply-next-button]")
                )
            )
            driver.execute_script("arguments[0].scrollIntoView({block:'center'});", btn)
            time.sleep(self.config.delays.scroll_delay)
            btn.click()
            self.logger.info("Clicked Next button")
            return True
        except Exception:
            pass
            
        try:
            btn = WebDriverWait(driver, self.browser.wait_time).until(
                EC.element_to_be_clickable(
                    (By.XPATH,
                     "//button[normalize-space(span)='Next' or "
                     "contains(@aria-label,'Continue to next step')]")
                )
            )
            driver.execute_script("arguments[0].scrollIntoView({block:'center'});", btn)
            time.sleep(self.config.delays.scroll_delay)
            btn.click()
            self.logger.info("Clicked Next button (fallback)")
            return True
        except Exception as e:
            self.logger.error(f"Failed to click Next: {e}")
            return False
            
    def _click_submit_button(self) -> bool:
        """Click submit with multiple selector strategies"""
        driver = self.browser.driver
        time.sleep(2)
        
        submit_selectors = [
            "button[aria-label='Submit application']",
            "button[data-control-name='submit_unify']",
            "button.jobs-apply-button--submit",
            "//button[contains(@class, 'jobs-apply-button') and contains(text(), 'Submit')]",
            "//button[@type='submit' and contains(@class, 'artdeco-button--primary')]"
        ]
        
        for selector in submit_selectors:
            try:
                if selector.startswith("//"):
                    btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                else:
                    btn = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", btn)
                time.sleep(self.config.delays.scroll_delay)
                
                try:
                    btn.click()
                except:
                    driver.execute_script("arguments[0].click();", btn)
                    
                self.logger.info("Clicked Submit button")
                
                try:
                    WebDriverWait(driver, 10).until(
                        EC.invisibility_of_element_located(
                            (By.CSS_SELECTOR, "div.jobs-easy-apply-modal")
                        )
                    )
                    self.logger.info("Application submitted successfully")
                    return True
                except TimeoutException:
                    self._handle_submit_confirmation()
                    return True
                    
            except Exception:
                continue
                
        self.logger.error("Failed to find/click Submit button")
        return False
        
    def _handle_submit_confirmation(self):
        """Handle any confirmation dialogs after submit"""
        try:
            done_btn = self.browser.driver.find_element(
                By.XPATH, "//button[contains(text(), 'Done') or contains(text(), 'OK')]"
            )
            self.browser.safe_click(done_btn)
            time.sleep(1)
        except:
            pass
            
    def apply_for_jobs(self, resume_path: str):
        """Main job application loop"""
        # Get already applied jobs
        applied_urls = self.data.get_applied_job_urls()
        applied_urls |= self.get_applied_jobs_from_linkedin()
        
        for keyword in self.search_keywords:
            self.logger.info(f"Searching for: {keyword}")
            self._search_and_apply(keyword, resume_path, applied_urls)
            
            # Check max applications limit
            if self.stats.current_stats.applied >= self.config.application.max_applications_per_run:
                self.logger.info(f"Reached max applications limit: {self.config.application.max_applications_per_run}")
                break
                
    def _search_and_apply(self, keyword: str, resume_path: str, applied_urls: Set[str]):
        """Search for jobs and apply"""
        page = 0
        while True:
            if not self._navigate_to_page(keyword, page):
                break
                
            self.logger.info(f"Processing jobs on page {page + 1}")
            jobs = self._get_job_cards()
            
            if not jobs:
                self.logger.info("No more jobs found")
                break
                
            for i in range(len(jobs)):
                jobs = self._get_job_cards()  # Refresh to avoid stale elements
                if i >= len(jobs):
                    break
                    
                try:
                    self._process_single_job(jobs[i], resume_path, applied_urls)
                except Exception as e:
                    self.logger.error(f"Error processing job: {e}")
                    self.stats.current_stats.errors += 1
                    
                self.stats.update_dashboard()
                
                # Random delay between applications
                delay = random.uniform(*self.config.delays.between_applications)
                time.sleep(delay)
                
                # Check max applications
                if self.stats.current_stats.applied >= self.config.application.max_applications_per_run:
                    return
                    
            page += 1
            
    def _navigate_to_page(self, keyword: str, page: int) -> bool:
        """Navigate to search results page"""
        self._close_modal_if_present()
        
        if page == 0:
            search_url = (
                f"https://www.linkedin.com/jobs/search/"
                f"?f_AL=true&keywords={keyword}&location={self.config.search.locations[0]}"
            )
            self.browser.driver.get(search_url)
        else:
            try:
                next_btn = WebDriverWait(self.browser.driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[.//span[contains(text(), 'Next')]]")
                    )
                )
                self.browser.driver.execute_script(
                    "arguments[0].scrollIntoView({block: 'center'});", next_btn
                )
                time.sleep(self.config.delays.scroll_delay)
                next_btn.click()
                self.logger.info("Clicked Next button")
            except (TimeoutException, ElementClickInterceptedException):
                page_url = (
                    f"https://www.linkedin.com/jobs/search/"
                    f"?f_AL=true&keywords={keyword}&location={self.config.search.locations[0]}"
                    f"&start={page * 25}"
                )
                self.browser.driver.get(page_url)
                
        self._wait_for_page_load()
        time.sleep(random.uniform(3, 5))
        
        try:
            WebDriverWait(self.browser.driver, 15).until(
                EC.presence_of_all_elements_located(
                    (By.CLASS_NAME, 'job-card-container')
                )
            )
            return True
        except TimeoutException:
            return False
            
    def _get_job_cards(self) -> List:
        """Get all job cards on current page"""
        return self.browser.driver.find_elements(By.CLASS_NAME, 'job-card-container')
        
    def _process_single_job(self, job_card, resume_path: str, applied_urls: Set[str]):
        """Process a single job application"""
        self.stats.current_stats.jobs_processed += 1
        
        # Check if already applied
        card_text = job_card.text.lower()
        if "applied" in card_text:
            self.logger.info("Skipping job - already applied")
            self.stats.current_stats.skipped += 1
            return
            
        # Get job URL
        try:
            job_link = job_card.find_element(By.CSS_SELECTOR, "a")
            job_url = job_link.get_attribute("href") or ""
        except Exception:
            job_url = ""
            
        # Check if URL already processed
        if job_url in applied_urls:
            self.logger.info(f"Skipping duplicate job: {job_url}")
            self.stats.current_stats.skipped += 1
            return
            
        # Click on job card
        self.browser.driver.execute_script(
            "arguments[0].scrollIntoView(true);", job_card
        )
        if not self.browser.safe_click(job_card):
            self.stats.current_stats.skipped += 1
            return
            
        # Extract job description
        job_description = self._extract_job_description()
        time.sleep(random.uniform(2, 4))
        
        # Look for Easy Apply button
        try:
            easy_apply_btn = WebDriverWait(self.browser.driver, 5).until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//button[contains(@class, 'jobs-apply-button')]")
                )
            )
        except TimeoutException:
            self.logger.info("No Easy Apply button found")
            self.stats.current_stats.skipped += 1
            self.data.log_skipped(job_url, "No Easy Apply button")
            return
            
        # Get job details before clicking
        job_title, company = self._get_job_details()
        
        # Click Easy Apply
        if not self.browser.safe_click(easy_apply_btn):
            self.stats.current_stats.skipped += 1
            self.data.log_skipped(job_url, "Failed to click Easy Apply", job_title, company)
            return
            
        time.sleep(random.uniform(2, 3))
        
        # Handle application
        if not self.handle_multi_step_application(resume_path, job_description):
            self.logger.info("Application not submitted")
            self.stats.current_stats.skipped += 1
            self.data.log_skipped(job_url, "Application not completed", job_title, company)
            return
            
        # Get cover letter if generated
        cover_letter = ""
        if hasattr(self.form_filler, 'last_cover_letter'):
            cover_letter = self.form_filler.last_cover_letter
            
        # Log successful application
        self.data.log_application(
            job_title=job_title,
            company=company,
            job_url=job_url,
            resume_used=resume_path,
            status='submitted',
            job_description=job_description,
            cover_letter=cover_letter
        )
        
        self.logger.info(f"✓ Applied to {job_title} at {company}")
        self.stats.current_stats.applied += 1
        applied_urls.add(job_url)
        
        # Handle popup
        self.handle_application_popup()
        
    def _extract_job_description(self) -> str:
        """Extracts the job description text"""
        try:
            desc_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-description__content"))
            )
            return desc_elem.text.strip()
        except Exception:
            return ""
            
    def _get_job_details(self) -> Tuple[str, str]:
        """Extract job title and company"""
        job_title = "Unknown Job"
        company = "Unknown Company"
        
        try:
            title_elem = WebDriverWait(self.browser.driver, 5).until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__job-title')
                )
            )
            job_title = title_elem.text.strip() or job_title
            
            company_elem = self.browser.driver.find_element(
                By.CSS_SELECTOR, '.job-details-jobs-unified-top-card__company-name'
            )
            company = company_elem.text.strip() or company
            
        except Exception:
            self.logger.warning("Could not extract job details")
            
        return job_title, company
        
    def _close_modal_if_present(self):
        """Close any open modals"""
        try:
            modal = self.browser.driver.find_element(
                By.CSS_SELECTOR, "div.jobs-easy-apply-modal"
            )
            
            if modal.is_displayed():
                close_buttons = [
                    "button[aria-label='Dismiss']",
                    "button.artdeco-modal__dismiss",
                    "//button[contains(@class, 'artdeco-modal__dismiss')]"
                ]
                
                for selector in close_buttons:
                    try:
                        if selector.startswith("//"):
                            close_btn = self.browser.driver.find_element(By.XPATH, selector)
                        else:
                            close_btn = self.browser.driver.find_element(By.CSS_SELECTOR, selector)
                            
                        close_btn.click()
                        time.sleep(1)
                        self.logger.info("Closed modal")
                        break
                    except:
                        continue
                        
        except NoSuchElementException:
            pass

# ===================== NETWORK EXPANSION =====================
class NetworkExpander:
    """Manages LinkedIn network expansion"""
    def __init__(self, browser_manager: BrowserManager, stats_manager: StatsManager,
                 data_manager: DataManager, config: LinkedInConfig, logger: EnhancedLogger):
        self.browser = browser_manager
        self.stats = stats_manager
        self.data = data_manager
        self.config = config
        self.logger = logger
        
    def _find_accept_buttons(self):
        """Find all 'Accept' buttons"""
        return self.browser.driver.find_elements(
            By.XPATH,
            "//span[text()='Accept']/ancestor::button"
        )
        
    def _find_connect_buttons(self):
        """Find all 'Connect' buttons"""
        return self.browser.driver.find_elements(
            By.XPATH,
            "//span[text()='Connect']/ancestor::button"
        )
        
    def _click_load_more(self):
        """Click the 'Load more' button if present"""
        try:
            btn = self.browser.driver.find_element(
                By.XPATH, "//button[normalize-space()='Load more']"
            )
            if self.browser.safe_click(btn):
                self.logger.info("Clicked 'Load more'")
                time.sleep(2)
        except Exception:
            pass
            
    def expand_network(self):
        """Main network expansion process"""
        self.logger.info("Starting network expansion")
        
        # Go to Grow page
        self.browser.driver.get("https://www.linkedin.com/mynetwork/grow/")
        time.sleep(5)
        
        # Load more suggestions
        self._click_load_more()
        
        connections_sent = 0
        invitations_accepted = 0
        
        max_retries = 5
        for attempt in range(max_retries):
            self.stats.network_stats.attempts += 1
            
            if attempt > 0:
                self._click_load_more()
                
            # Find Accept & Connect buttons
            accept_buttons = self._find_accept_buttons()
            connect_buttons = self._find_connect_buttons()
            
            total = len(accept_buttons) + len(connect_buttons)
            if total == 0:
                self.logger.info("No network actions found, refreshing...")
                self.browser.driver.refresh()
                time.sleep(5)
                continue
                
            self.logger.info(f"Found {total} network actions")
            
            # Accept invitations
            for btn in accept_buttons:
                if invitations_accepted >= self.config.network.max_invitations_accept:
                    break
                    
                if self.browser.safe_click(btn):
                    self.logger.info("Accepted an invitation")
                    self.stats.network_stats.actions_accepted += 1
                    invitations_accepted += 1
                    
                    # Log the action
                    self.data.log_network_action(
                        action_type="accept",
                        profile_name="Unknown",  # Could extract from page
                        profile_url=""
                    )
                    
                time.sleep(random.uniform(*self.config.delays.network_action_delay))
                
            # Send connections
            for btn in connect_buttons:
                if connections_sent >= self.config.network.max_connections_per_run:
                    break
                    
                if self.browser.safe_click(btn):
                    self.logger.info("Sent a connection request")
                    self.stats.network_stats.actions_sent += 1
                    connections_sent += 1
                    
                    # Log the action
                    self.data.log_network_action(
                        action_type="connect",
                        profile_name="Unknown",  # Could extract from page
                        profile_url=""
                    )
                    
                time.sleep(random.uniform(*self.config.delays.network_action_delay))
                
            # Check limits
            if (connections_sent >= self.config.network.max_connections_per_run and
                invitations_accepted >= self.config.network.max_invitations_accept):
                break
                
            time.sleep(3)
            
        self.logger.info(f"Network expansion complete: {connections_sent} connections sent, "
                        f"{invitations_accepted} invitations accepted")

# ===================== DASHBOARD =====================
class DashboardServer:
    """FastAPI dashboard server"""
    def __init__(self, config: LinkedInConfig):
        self.config = config
        self.app = FastAPI()
        self._setup_routes()
        
    def _setup_routes(self):
        """Setup API routes"""
        @self.app.get("/")
        def read_dashboard():
            try:
                dashboard_file = Path(self.config.paths.data_directory) / "dashboard_data.json"
                if dashboard_file.exists():
                    with open(dashboard_file, "r") as f:
                        return json.load(f)
                return {"error": "No data available"}
            except Exception as e:
                return {"error": str(e)}
                
        @self.app.get("/stats")
        def read_stats():
            """Get overall statistics"""
            stats_file = Path(self.config.paths.data_directory) / "overall_stats.json"
            if stats_file.exists():
                with open(stats_file, "r") as f:
                    return json.load(f)
            return {"error": "No statistics available"}
            
        @self.app.get("/applications")
        def get_applications(limit: int = 50):
            """Get recent applications"""
            apps = []
            log_dir = Path(self.config.paths.log_directory)
            
            # Get all application files from run directories
            for run_dir in sorted(log_dir.iterdir(), reverse=True):
                if run_dir.is_dir() and run_dir.name.startswith("run_"):
                    apps_file = run_dir / "applications.json"
                    if apps_file.exists():
                        with open(apps_file, 'r') as f:
                            run_apps = json.load(f)
                            apps.extend(run_apps)
                            
                if len(apps) >= limit:
                    break
                    
            return {"applications": apps[:limit], "total": len(apps)}
            
        @self.app.get("/config")
        def get_config():
            """Get configuration summary"""
            return {
                "username": self.config.username,
                "has_password": bool(self.config.password),
                "has_api_key": bool(self.config.ai.openai_api_key),
                "search_keywords": self.config.search.keywords,
                "browser": self.config.browser.browser_type,
                "auto_defaults": self.config.application.auto_use_defaults,
                "max_applications": self.config.application.max_applications_per_run
            }
            
    def run(self, host: str = "127.0.0.1", port: int = 8000):
        """Run the dashboard server"""
        uvicorn.run(self.app, host=host, port=port)

# ===================== CLI INTERFACE =====================
class LinkedInAutomationCLI:
    """Command-line interface for the automation"""
    def __init__(self):
        self.config = LinkedInConfig.load()
        self.config.create_directories()
        self.logger = EnhancedLogger(self.config)
        self.stats_manager = StatsManager(self.config)
        self.data_manager = DataManager(self.config, self.logger)
        self.browser_manager = None
        self.ai_provider = None
        
    def run(self):
        """Main entry point"""
        self.logger.start_run()
        
        try:
            if len(sys.argv) > 1:
                if sys.argv[1] == "--apply":
                    self.direct_apply_mode()
                elif sys.argv[1] == "--menu":
                    self.menu_mode()
                else:
                    print("Unknown parameter. Use --apply or --menu")
                    self.menu_mode()
            else:
                self.menu_mode()
        finally:
            self.logger.end_run()
            self.print_stats()
            self.logger.info("Exiting")
            
    def menu_mode(self):
        """Interactive menu mode"""
        while True:
            print("\n" + "="*50)
            print("LinkedIn Automation System")
            print("="*50)
            print("1. Apply for Jobs")
            print("2. Expand Network")
            print("3. View Application Log")
            print("4. View Statistics")
            print("5. Update Configuration")
            print("6. Launch Dashboard Server")
            print("7. View Enhanced Logs")
            print("8. Quit")
            print("="*50)
            
            choice = input("Choose an option (1-8): ")
            
            if choice == '1':
                self.job_application_mode()
            elif choice == '2':
                self.network_expansion_mode()
            elif choice == '3':
                self.view_application_log()
            elif choice == '4':
                self.print_stats()
            elif choice == '5':
                self.update_configuration()
            elif choice == '6':
                self.launch_dashboard()
            elif choice == '7':
                self.view_enhanced_logs()
            elif choice == '8':
                break
            else:
                print("Invalid choice. Please try again.")
                
    def direct_apply_mode(self):
        """Direct application mode with minimal interaction"""
        # Ask about auto-defaults
        auto_defaults = input(
            "Auto-use saved/prefilled answers? (Y/n): "
        ).strip().lower() != "n"
        self.config.application.auto_use_defaults = auto_defaults
        
        # Continuous application loop
        while True:
            self.job_application_mode()
            
            continue_prompt = input(
                "\nCompleted search cycle. Continue? (Y/n): "
            ).strip().lower()
            if continue_prompt == "n":
                break
                
    def job_application_mode(self):
        """Run job application process"""
        # Ensure credentials
        if not self.config.username or not self.config.password:
            self.update_credentials()
            
        # Ensure resume path
        if not self.config.paths.resume_path:
            self.config.paths.resume_path = input(
                "Enter full path to resume: "
            ).strip()
            self.config.save()
            
        # Initialize components
        self.browser_manager = BrowserManager(self.config, self.logger)
        self.ai_provider = ChatGPTProvider(self.config.ai)
        
        form_filler = FormFiller(
            self.browser_manager, self.ai_provider,
            self.data_manager, self.config, self.logger
        )
        
        job_manager = JobApplicationManager(
            self.browser_manager, form_filler, self.data_manager,
            self.stats_manager, self.config, self.logger
        )
        
        try:
            self.browser_manager.init_browser()
            if not self.browser_manager.login(self.config.username, self.config.password):
                self.logger.error("Login failed")
                return
                
            try:
                job_manager.apply_for_jobs(self.config.paths.resume_path)
            except InvalidSessionIdException as e:
                self.logger.error(f"Session lost ({e}); restarting and retrying...")
                self.browser_manager.quit()
                # One retry
                self.browser_manager.init_browser()
                if self.browser_manager.login(self.config.username, self.config.password):
                    job_manager.apply_for_jobs(self.config.paths.resume_path)
                    
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            if self.config.application.take_screenshots_on_error:
                try:
                    self.browser_manager.take_screenshot("error")
                except Exception:
                    pass
        finally:
            if self.browser_manager:
                self.browser_manager.quit()
                
    def network_expansion_mode(self):
        """Run network expansion process"""
        # Ensure credentials
        if not self.config.username or not self.config.password:
            self.update_credentials()
            
        # Initialize components
        self.browser_manager = BrowserManager(self.config, self.logger)
        network_expander = NetworkExpander(
            self.browser_manager, self.stats_manager,
            self.data_manager, self.config, self.logger
        )
        
        try:
            self.browser_manager.init_browser()
            
            if self.browser_manager.login(self.config.username, self.config.password):
                network_expander.expand_network()
            else:
                self.logger.error("Login failed")
                
        except Exception as e:
            self.logger.error(f"Network expansion error: {e}")
            
        finally:
            if self.browser_manager:
                self.browser_manager.quit()
                
    def view_application_log(self):
        """View application history"""
        apps = []
        log_dir = Path(self.config.paths.log_directory)
        
        # Get applications from all runs
        for run_dir in sorted(log_dir.iterdir(), reverse=True):
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        apps.extend(run_apps)
                        
        if not apps:
            print("No applications logged yet.")
            return
            
        print(f"\n{'='*100}")
        print(f"{'Date':<20} {'Job Title':<30} {'Company':<25} {'Status':<10}")
        print(f"{'='*100}")
        
        for app in apps[-20:]:  # Show last 20
            date = app['applied_date'][:19]
            title = app['job_title'][:29]
            company = app['company'][:24]
            status = app['status'][:9]
            print(f"{date:<20} {title:<30} {company:<25} {status:<10}")
            
        print(f"\nTotal applications: {len(apps)}")
        input("\nPress Enter to continue...")
        
    def view_enhanced_logs(self):
        """View enhanced logs with search functionality"""
        while True:
            print("\n" + "="*50)
            print("ENHANCED LOGS VIEWER")
            print("="*50)
            print("1. View Recent Applications (with descriptions)")
            print("2. View Applications by Company")
            print("3. View Applications by Job Title")
            print("4. View Error Summary")
            print("5. View Network Actions")
            print("6. View Run Summaries")
            print("7. Export Applications to CSV")
            print("8. Back to Main Menu")
            print("="*50)
            
            choice = input("Choose an option (1-8): ")
            
            if choice == '1':
                self.view_recent_applications_detailed()
            elif choice == '2':
                company = input("Enter company name to search: ")
                self.search_applications(company=company)
            elif choice == '3':
                title = input("Enter job title to search: ")
                self.search_applications(job_title=title)
            elif choice == '4':
                self.view_error_summary()
            elif choice == '5':
                self.view_network_actions()
            elif choice == '6':
                self.view_run_summaries()
            elif choice == '7':
                self.export_applications_csv()
            elif choice == '8':
                break
                
    def view_recent_applications_detailed(self):
        """View recent applications with details"""
        apps = []
        log_dir = Path(self.config.paths.log_directory)
        
        # Get most recent applications
        for run_dir in sorted(log_dir.iterdir(), reverse=True):
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        apps.extend(run_apps)
                        
            if len(apps) >= 10:
                break
                
        if not apps:
            print("No applications found.")
            return
            
        for i, app in enumerate(apps[:10], 1):
            print(f"\n{'='*60}")
            print(f"Application #{i}")
            print(f"{'='*60}")
            print(f"Date: {app['applied_date']}")
            print(f"Job Title: {app['job_title']}")
            print(f"Company: {app['company']}")
            print(f"Status: {app['status']}")
            print(f"URL: {app['job_url']}")
            print(f"\nJob Description Summary:")
            print(app.get('job_description_summary', 'N/A')[:300] + "...")
            
            if app.get('cover_letter'):
                print(f"\nCover Letter Preview:")
                print(app['cover_letter'][:200] + "...")
                
        input("\nPress Enter to continue...")
        
    def search_applications(self, company: str = None, job_title: str = None):
        """Search applications by criteria"""
        apps = []
        log_dir = Path(self.config.paths.log_directory)
        
        # Search all applications
        for run_dir in log_dir.iterdir():
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        for app in run_apps:
                            if company and company.lower() not in app.get('company', '').lower():
                                continue
                            if job_title and job_title.lower() not in app.get('job_title', '').lower():
                                continue
                            apps.append(app)
                            
        if not apps:
            print("No matching applications found.")
            return
            
        print(f"\nFound {len(apps)} matching applications:")
        print(f"{'='*100}")
        print(f"{'Date':<20} {'Job Title':<30} {'Company':<25}")
        print(f"{'='*100}")
        
        for app in apps:
            date = app['applied_date'][:19]
            title = app['job_title'][:29]
            company_name = app['company'][:24]
            print(f"{date:<20} {title:<30} {company_name:<25}")
            
        input("\nPress Enter to continue...")
        
    def view_error_summary(self):
        """View error summary across runs"""
        errors = []
        log_dir = Path(self.config.paths.log_directory)
        
        for run_dir in log_dir.iterdir():
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                # Check main log for errors
                main_log = run_dir / "main.log"
                if main_log.exists():
                    with open(main_log, 'r') as f:
                        for line in f:
                            if "ERROR" in line:
                                errors.append(line.strip())
                                
        if not errors:
            print("No errors found!")
            return
            
        print(f"\nFound {len(errors)} errors:")
        for error in errors[-20:]:  # Show last 20
            print(error)
            
        input("\nPress Enter to continue...")
        
    def view_network_actions(self):
        """View network expansion actions"""
        print("\nNetwork actions logging will be shown here once implemented.")
        # This would show detailed network actions from the enhanced logger
        input("\nPress Enter to continue...")
        
    def view_run_summaries(self):
        """View summaries of all runs"""
        log_dir = Path(self.config.paths.log_directory)
        summaries = []
        
        for run_dir in sorted(log_dir.iterdir(), reverse=True):
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                summary_file = run_dir / "run_summary.json"
                if summary_file.exists():
                    with open(summary_file, 'r') as f:
                        summary = json.load(f)
                        summaries.append(summary)
                        
        if not summaries:
            print("No run summaries found.")
            return
            
        print(f"\n{'='*80}")
        print(f"{'Run ID':<20} {'Start Time':<20} {'Applied':<10} {'Skipped':<10} {'Errors':<10}")
        print(f"{'='*80}")
        
        for summary in summaries[:10]:  # Show last 10 runs
            run_id = summary['run_id']
            start = summary['start_time'][:19]
            applied = summary['jobs_applied']
            skipped = summary['jobs_skipped']
            errors = summary['errors']
            print(f"{run_id:<20} {start:<20} {applied:<10} {skipped:<10} {errors:<10}")
            
        input("\nPress Enter to continue...")
        
    def export_applications_csv(self):
        """Export applications to CSV file"""
        import csv
        
        apps = []
        log_dir = Path(self.config.paths.log_directory)
        
        # Get all applications
        for run_dir in log_dir.iterdir():
            if run_dir.is_dir() and run_dir.name.startswith("run_"):
                apps_file = run_dir / "applications.json"
                if apps_file.exists():
                    with open(apps_file, 'r') as f:
                        run_apps = json.load(f)
                        apps.extend(run_apps)
                        
        if not apps:
            print("No applications to export.")
            return
            
        # Export to CSV
        csv_file = f"linkedin_applications_{datetime.now():%Y%m%d_%H%M%S}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'applied_date', 'job_title', 'company', 'location', 
                'job_url', 'status', 'resume_used', 'job_description_summary'
            ])
            writer.writeheader()
            writer.writerows(apps)
            
        print(f"Exported {len(apps)} applications to {csv_file}")
        input("\nPress Enter to continue...")
        
    def print_stats(self):
        """Print statistics"""
        # Current run stats
        current = self.stats_manager.current_stats
        
        print("\n" + "="*50)
        print("CURRENT RUN STATISTICS")
        print("="*50)
        print(f"Jobs Processed: {current.jobs_processed}")
        print(f"Jobs Applied: {current.applied}")
        print(f"Jobs Skipped: {current.skipped}")
        print(f"Errors: {current.errors}")
        print(f"Uptime: {time.time() - current.start_time:.0f} seconds")
        
        # Network stats
        network = self.stats_manager.network_stats
        print(f"\nNetwork Actions:")
        print(f"  Invitations Accepted: {network.actions_accepted}")
        print(f"  Connections Sent: {network.actions_sent}")
        print(f"  Total Attempts: {network.attempts}")
        
        # Enhanced stats from logger
        run_summary = self.logger.run_stats
        print(f"\nEnhanced Statistics:")
        print(f"  Run ID: {run_summary['run_id']}")
        print(f"  Start Time: {run_summary['start_time']}")
        
    def update_credentials(self):
        """Update login credentials"""
        print("\nUpdate LinkedIn Credentials")
        self.config.username = input("Username/Email: ").strip()
        self.config.password = getpass.getpass("Password: ")
        self.config.save()
        self.logger.info("Credentials updated")
        
    def update_configuration(self):
        """Update configuration with enhanced options"""
        while True:
            print("\n" + "="*50)
            print("CONFIGURATION MENU")
            print("="*50)
            print("1. Update Credentials")
            print("2. Update Search Settings")
            print("3. Update File Paths")
            print("4. Update AI Settings")
            print("5. Update Delays/Timing")
            print("6. Update Network Settings")
            print("7. Update Browser Settings")
            print("8. View Current Configuration")
            print("9. Back to Main Menu")
            print("="*50)
            
            choice = input("Choose an option (1-9): ")
            
            if choice == '1':
                self.update_credentials()
            elif choice == '2':
                self.update_search_settings()
            elif choice == '3':
                self.update_file_paths()
            elif choice == '4':
                self.update_ai_settings()
            elif choice == '5':
                self.update_delays()
            elif choice == '6':
                self.update_network_settings()
            elif choice == '7':
                self.update_browser_settings()
            elif choice == '8':
                self.view_current_config()
            elif choice == '9':
                break
                
    def update_search_settings(self):
        """Update job search settings"""
        print("\nCurrent Search Keywords:")
        for i, keyword in enumerate(self.config.search.keywords, 1):
            print(f"{i}. {keyword}")
            
        action = input("\n(A)dd keyword, (R)emove keyword, (L)ocations, or (B)ack: ").lower()
        
        if action == 'a':
            keyword = input("Enter new keyword: ")
            self.config.search.keywords.append(keyword)
            self.config.save()
            print(f"Added '{keyword}'")
        elif action == 'r':
            idx = int(input("Enter number to remove: ")) - 1
            if 0 <= idx < len(self.config.search.keywords):
                removed = self.config.search.keywords.pop(idx)
                self.config.save()
                print(f"Removed '{removed}'")
        elif action == 'l':
            print(f"\nCurrent locations: {', '.join(self.config.search.locations)}")
            new_location = input("Add location (or press Enter to skip): ")
            if new_location:
                self.config.search.locations.append(new_location)
                self.config.save()
                
    def update_file_paths(self):
        """Update file paths"""
        print("\nCurrent File Paths:")
        print(f"Resume: {self.config.paths.resume_path}")
        print(f"Logs: {self.config.paths.log_directory}")
        print(f"Screenshots: {self.config.paths.screenshot_directory}")
        
        resume = input(f"\nResume path [{self.config.paths.resume_path}]: ").strip()
        if resume:
            self.config.paths.resume_path = resume
            self.config.save()
            
    def update_ai_settings(self):
        """Update AI settings"""
        print("\nAI Settings:")
        print(f"API Key Set: {'Yes' if self.config.ai.openai_api_key else 'No'}")
        print(f"Model: {self.config.ai.model}")
        print(f"Use AI for Cover Letters: {self.config.ai.use_ai_for_cover_letters}")
        print(f"Use AI for Form Fields: {self.config.ai.use_ai_for_form_fields}")
        
        api_key = input("\nOpenAI API key (press Enter to skip): ").strip()
        if api_key:
            self.config.ai.openai_api_key = api_key
            self.config.save()
            
    def update_delays(self):
        """Update delay settings"""
        print("\nCurrent Delays (seconds):")
        print(f"Page Load: {self.config.delays.page_load}")
        print(f"Between Applications: {self.config.delays.between_applications}")
        print(f"Form Fill: {self.config.delays.form_fill_delay}")
        print(f"Click: {self.config.delays.click_delay}")
        
        page_load = input(f"\nPage load delay [{self.config.delays.page_load}]: ").strip()
        if page_load:
            self.config.delays.page_load = float(page_load)
            self.config.save()
            
    def update_network_settings(self):
        """Update network expansion settings"""
        print("\nNetwork Settings:")
        print(f"Max connections per run: {self.config.network.max_connections_per_run}")
        print(f"Max invitations to accept: {self.config.network.max_invitations_accept}")
        
        max_conn = input(f"\nMax connections [{self.config.network.max_connections_per_run}]: ").strip()
        if max_conn:
            self.config.network.max_connections_per_run = int(max_conn)
            self.config.save()
            
    def update_browser_settings(self):
        """Update browser settings"""
        print("\nBrowser Settings:")
        print(f"Browser: {self.config.browser.browser_type}")
        print(f"Headless: {self.config.browser.headless}")
        print(f"Chrome Binary: {self.config.browser.chrome_binary_path}")
        
        headless = input("\nRun headless? (y/N): ").strip().lower()
        if headless == 'y':
            self.config.browser.headless = True
            self.config.save()
            
    def view_current_config(self):
        """Display current configuration"""
        print("\nCurrent Configuration:")
        print(json.dumps(asdict(self.config), indent=2))
        input("\nPress Enter to continue...")
        
    def launch_dashboard(self):
        """Launch dashboard server"""
        dashboard = DashboardServer(self.config)
        
        # Run in separate thread
        server_thread = threading.Thread(
            target=dashboard.run,
            daemon=True
        )
        server_thread.start()
        
        # Open browser
        webbrowser.open("http://127.0.0.1:8000")
        self.logger.info("Dashboard launched at http://127.0.0.1:8000")
        
        input("Press Enter to continue...")

# ===================== MAIN ENTRY POINT =====================
if __name__ == '__main__':
    cli = LinkedInAutomationCLI()
    cli.run()
        