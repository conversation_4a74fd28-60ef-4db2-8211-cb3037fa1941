#!/usr/bin/env python3
"""
LinkedIn Automation Platform - Main Entry Point
Clean version with enhanced job application support
"""

import sys
import os
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Import vault for secure credential management
try:
    from setup.vault_setup import config as vault_config, get_secret
    VAULT_AVAILABLE = True
    print("🔐 Vault integration enabled")
except ImportError:
    VAULT_AVAILABLE = False
    print("⚠️  Vault not available, using config file credentials")


class SimpleBrowserManager:
    """Simple browser manager for modules that need it"""
    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
        self.driver = None
        
    def init_browser(self):
        """Initialize browser"""
        options = webdriver.ChromeOptions()
        
        # Get chrome binary from config
        if isinstance(self.config, dict):
            chrome_binary = self.config.get('browser', {}).get('chrome_binary_location')
            headless = self.config.get('browser', {}).get('headless', False)
        else:
            chrome_binary = getattr(self.config, 'chrome_binary_location', None)
            headless = getattr(self.config, 'headless', False)
            
        if chrome_binary and os.path.exists(chrome_binary):
            options.binary_location = chrome_binary
            
        if headless:
            options.add_argument('--headless')
            
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.maximize_window()
        return self.driver
        
    def login(self, username, password):
        """Login to LinkedIn"""
        try:
            if not self.driver:
                print("Error: Browser not initialized")
                return False
                
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(2)
            
            username_field = self.driver.find_element(By.ID, 'username')
            username_field.send_keys(username)
            
            password_field = self.driver.find_element(By.ID, 'password')
            password_field.send_keys(password)
            password_field.send_keys(Keys.RETURN)
            
            time.sleep(5)
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Login error: {e}")
            else:
                print(f"Login error: {e}")
            return False
            
    def safe_click(self, element, retries=3, delay=1):
        """Safe click implementation"""
        for attempt in range(retries):
            try:
                element.click()
                return True
            except:
                if attempt < retries - 1:
                    time.sleep(delay)
                    try:
                        if self.driver:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    except:
                        pass
        return False
        
    def quit(self):
        """Close browser"""
        if self.driver:
            self.driver.quit()


class SimpleDataManager:
    """Simple data manager for modules that need it"""
    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
        self.network_log = []
        self.content_log = []
        self.message_log = []
        
    def log_network_action(self, action_type, profile_name, profile_url, message=""):
        """Log network actions"""
        action = {
            'timestamp': datetime.now().isoformat(),
            'action_type': action_type,
            'profile_name': profile_name,
            'profile_url': profile_url,
            'message': message
        }
        self.network_log.append(action)
        self._save_log('network_actions.json', self.network_log)
        
    def log_content_published(self, content_type, content_text, post_url=""):
        """Log published content"""
        content = {
            'timestamp': datetime.now().isoformat(),
            'content_type': content_type,
            'content_text': content_text[:200] + '...' if len(content_text) > 200 else content_text,
            'post_url': post_url
        }
        self.content_log.append(content)
        self._save_log('content_published.json', self.content_log)
        
    def log_message_interaction(self, sender_name, message_type, action_taken):
        """Log message interactions"""
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'sender_name': sender_name,
            'message_type': message_type,
            'action_taken': action_taken
        }
        self.message_log.append(interaction)
        self._save_log('message_interactions.json', self.message_log)
        
    def _save_log(self, filename, data):
        """Save log to file"""
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving {filename}: {e}")


class LinkedInAutomationPlatform:
    """Main platform orchestrator with fixed handlers"""
    
    def __init__(self):
        """Initialize the platform"""
        self.config = None
        self.logger = None
        self._load_config()
        self._setup_logger()
        
    def _load_config(self):
        """Load configuration from JSON and vault"""
        config_file = 'linkedin_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                self.config = json.load(f)
            print(f"✓ Loaded configuration from {config_file}")
        else:
            print(f"Warning: {config_file} not found. Using default configuration.")
            self.config = {}
        
        # Load credentials from vault if available, otherwise fall back to config file
        if VAULT_AVAILABLE:
            try:
                self.username = get_secret('LINKEDIN_EMAIL') or self.config.get('linkedin', {}).get('email', '')  # type: ignore
                self.password = get_secret('LINKEDIN_PASSWORD') or self.config.get('linkedin', {}).get('password', '')  # type: ignore
                
                if self.username and self.password:
                    print("✓ Loaded LinkedIn credentials from vault")
                else:
                    print("⚠️  No credentials in vault, using config file")
                    self.username = self.config.get('linkedin', {}).get('email', '')
                    self.password = self.config.get('linkedin', {}).get('password', '')
            except Exception as e:
                print(f"⚠️  Vault error: {e}, using config file credentials")
                self.username = self.config.get('linkedin', {}).get('email', '')
                self.password = self.config.get('linkedin', {}).get('password', '')
        else:
            # No vault available, use config file
            self.username = self.config.get('linkedin', {}).get('email', '')
            self.password = self.config.get('linkedin', {}).get('password', '')
            
    def _setup_logger(self):
        """Setup basic logger"""
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def get_config_value(self, key, default=None):
        """Safely get config value"""
        if isinstance(self.config, dict):
            return self.config.get(key, default)
        return default
        
    def display_menu(self):
        """Display the main menu"""
        print("\n" + "="*50)
        print("LinkedIn Automation Platform - Main Menu")
        print("="*50)
        print("1. Apply to Jobs (Auto-Apply Mode)")
        print("2. Network Maintenance (Birthdays, Congrats, etc.)")
        print("3. Article Publishing (Create & Post Content)")
        print("4. Message Auto-Reply (Check & Respond)")
        print("5. Web Dashboard (Launch Browser Interface)")
        print("6. View Statistics")
        print("7. Configuration Settings")
        print("8. Exit")
        print("="*50)
        
    def apply_to_jobs_handler(self):
        """Handler for job application automation"""
        print("\n[Job Application Mode]")
        
        try:
            # Try to import enhanced version first
            try:
                if os.path.exists('enhanced_job_apply.py'):
                    import enhanced_job_apply  # type: ignore
                    applicator = enhanced_job_apply.EnhancedJobApplicator(self.config, self.logger)
                    applicator.run()
                    return
            except ImportError:
                pass
            
            # Try modular version
            try:
                from modular_job_apply import create_job_application_handler
                applicator = create_job_application_handler(self.config, self.logger)
                applicator.run()
                return
            except ImportError:
                pass
                
            # Fallback to simple browser
            print("Using simple browser mode...")
            browser = SimpleBrowserManager(self.config, self.logger)
            browser.init_browser()
            
            username = self.get_config_value('username')
            password = self.get_config_value('password')
            
            if browser.login(username, password):
                print("Logged in successfully!")
                print("Navigate to LinkedIn Jobs to search for positions.")
                input("\nPress Enter when done to close browser...")
            else:
                print("Login failed")
                
            browser.quit()
                
        except Exception as e:
            print(f"Error in job application: {e}")
            if self.logger:
                self.logger.error(f"Job application error: {e}")
            
    def network_maintenance_handler(self):
        """Handler for network maintenance features"""
        print("\n[Network Maintenance Mode]")
        
        try:
            from modules.network_maintenance_module import NetworkMaintainer
            
            # Create required managers
            browser_manager = SimpleBrowserManager(self.config, self.logger)
            data_manager = SimpleDataManager(self.config, self.logger)
            
            # Initialize browser
            print("Starting browser...")
            browser_manager.init_browser()
            
            # Login
            username = self.get_config_value('username')
            password = self.get_config_value('password')
            
            if not username or not password:
                print("Error: Username or password not configured")
                browser_manager.quit()
                return
                
            print(f"Logging in as {username}...")
            if not browser_manager.login(username, password):
                print("Login failed")
                browser_manager.quit()
                return
                
            # Create NetworkMaintainer with all required arguments
            maintainer = NetworkMaintainer(
                browser_manager=browser_manager,
                data_manager=data_manager,
                config=self.config,
                logger=self.logger
            )
            
            print("\nStarting network maintenance...")
            print("- Checking for birthdays")
            print("- Looking for job changes to congratulate")  
            print("- Finding work anniversaries")
            print("- Engaging with network content\n")
            
            # Run maintenance
            try:
                maintainer.maintain_network()
            finally:
                browser_manager.quit()
                print("\nNetwork maintenance completed!")
                
        except ImportError:
            print("Network maintenance module not available.")
        except Exception as e:
            print(f"Error during network maintenance: {e}")
            import traceback
            traceback.print_exc()
            
    def article_publishing_handler(self):
        """Handler for content creation and publishing"""
        print("\n[Article Publishing Mode]")
        
        try:
            from modules.article_publishing_module import ContentCreator
            
            # Create required managers
            browser_manager = SimpleBrowserManager(self.config, self.logger)
            data_manager = SimpleDataManager(self.config, self.logger)
            
            # Initialize browser
            print("Starting browser...")
            browser_manager.init_browser()
            
            # Login
            username = self.get_config_value('username')
            password = self.get_config_value('password')
            
            print(f"Logging in as {username}...")
            if not browser_manager.login(username, password):
                print("Login failed")
                browser_manager.quit()
                return
                
            # Create ContentCreator with required arguments
            creator = ContentCreator(  # type: ignore
                browser_manager=browser_manager,
                data_manager=data_manager,  # type: ignore[call-arg]
                config=self.config,
                logger=self.logger
            )
            
            print("\nContent creation options:")
            print("1. Create article from trending topics")
            print("2. Create industry insight post")
            print("3. Create poll")
            print("4. Auto-create best option")
            
            choice = input("\nSelect option (1-4): ").strip()
            
            try:
                if choice == '1' and hasattr(creator, 'create_trending_article'):
                    creator.create_trending_article()  # type: ignore
                elif choice == '2' and hasattr(creator, 'create_industry_post'):
                    creator.create_industry_post()  # type: ignore
                elif choice == '3' and hasattr(creator, 'create_poll'):
                    creator.create_poll()  # type: ignore
                elif hasattr(creator, 'create_and_publish_content'):
                    creator.create_and_publish_content()  # type: ignore
                elif hasattr(creator, 'run'):
                    creator.run()  # type: ignore
                else:
                    print("Creating a sample post...")
                    # Fallback to simple post
                    sample_post = "Excited to share insights from today's tech landscape! #Technology #Innovation"
                    print(f"Would post: {sample_post}")
            finally:
                browser_manager.quit()
                print("\nContent publishing completed!")
                
        except ImportError:
            print("Article publishing module not available.")
        except Exception as e:
            print(f"Error during content creation: {e}")
            import traceback
            traceback.print_exc()
            
    def message_auto_reply_handler(self):
        """Handler for message checking and auto-reply"""
        print("\n[Message Auto-Reply Mode]")
        
        try:
            from modules.message_autoreply_module import MessageAutoResponder
            
            # Create required managers
            browser_manager = SimpleBrowserManager(self.config, self.logger)
            data_manager = SimpleDataManager(self.config, self.logger)
            
            # Initialize browser
            print("Starting browser...")
            browser_manager.init_browser()
            
            # Login
            username = self.get_config_value('username')
            password = self.get_config_value('password')
            
            print(f"Logging in as {username}...")
            if not browser_manager.login(username, password):
                print("Login failed")
                browser_manager.quit()
                return
                
            # Create MessageAutoResponder
            responder = MessageAutoResponder(  # type: ignore
                browser_manager=browser_manager,
                data_manager=data_manager,  # type: ignore[call-arg]
                config=self.config,
                logger=self.logger
            )
            
            print("\nChecking for unread messages...")
            
            try:
                if hasattr(responder, 'check_and_respond_to_messages'):
                    responder.check_and_respond_to_messages()  # type: ignore
                elif hasattr(responder, 'run'):
                    responder.run()  # type: ignore
                else:
                    print("Message responder ready but no run method found")
                    # Simple fallback
                    if browser_manager.driver:
                        browser_manager.driver.get("https://www.linkedin.com/messaging/")  # type: ignore
                    print("Navigated to messages. Check manually.")
                    input("Press Enter when done...")
            finally:
                browser_manager.quit()
                print("\nMessage handling completed!")
                
        except ImportError:
            print("Message auto-reply module not available.")
        except Exception as e:
            print(f"Error during message handling: {e}")
            import traceback
            traceback.print_exc()
            
    def web_dashboard_handler(self):
        """Handler for launching web dashboard"""
        print("\n[Web Dashboard Mode]")
        
        try:
            from web_dashboard_backend import DashboardServer
            
            # Create server with config and logger
            server = DashboardServer(self.config, self.logger)
            
            print("Starting web dashboard...")
            print("Dashboard will be available at: http://localhost:8000/dashboard")
            print("Press Ctrl+C to stop the server")
            
            if hasattr(server, 'run'):
                server.run()  # type: ignore
            elif hasattr(server, 'start'):
                server.start()  # type: ignore
            else:
                print("Dashboard server found but no run method available")
                
        except ImportError:
            print("Web dashboard module not available.")
            
            # Try alternative approaches
            if os.path.exists('web_dashboard_backend.py'):
                print("Trying to run web_dashboard_backend.py directly...")
                import subprocess
                subprocess.run([sys.executable, 'web_dashboard_backend.py'])
            elif os.path.exists('dashboard.html'):
                print("Found HTML dashboard. Opening in browser...")
                import webbrowser
                webbrowser.open(f'file://{os.path.abspath("dashboard.html")}')
            else:
                print("No dashboard files found")
                
        except Exception as e:
            print(f"Error launching dashboard: {e}")
            import traceback
            traceback.print_exc()
            
    def view_statistics_handler(self):
        """Handler for viewing automation statistics"""
        print("\n[Statistics]")
        
        # Check various statistics files
        stats_files = [
            'overall_stats.json',
            'dashboard_data.json', 
            'applications_log.json',
            'network_actions.json',
            'content_published.json',
            'message_interactions.json'
        ]
        
        found_stats = False
        
        for stats_file in stats_files:
            if os.path.exists(stats_file):
                found_stats = True
                try:
                    with open(stats_file, 'r') as f:
                        stats = json.load(f)
                    
                    print(f"\n📊 Stats from {stats_file}:")
                    if isinstance(stats, dict):
                        for key, value in stats.items():
                            print(f"  - {key}: {value}")
                    elif isinstance(stats, list):
                        print(f"  - Total entries: {len(stats)}")
                        if stats and len(stats) > 0:
                            print(f"  - Latest entry: {stats[-1]}")
                            if len(stats) > 1:
                                print(f"  - First entry: {stats[0]}")
                except Exception as e:
                    print(f"Error reading {stats_file}: {e}")
                    
        if not found_stats:
            print("\nNo statistics files found yet.")
            print("Run some automation features to generate statistics!")
            
        # Check log directories
        if os.path.exists('logs'):
            log_files = os.listdir('logs')
            print(f"\n📁 Log files found: {len(log_files)}")
            
    def configuration_settings_handler(self):
        """Handler for viewing/editing configuration"""
        print("\n[Configuration Settings]")
        
        print("1. View current configuration")
        print("2. Edit configuration file")
        print("3. Test configuration")
        print("4. Reset to defaults")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '1':
            print("\n📋 Current Configuration:")
            if self.config:
                print(json.dumps(self.config, indent=2))
            else:
                print("No configuration loaded")
                
        elif choice == '2':
            config_file = 'linkedin_config.json'
            if os.path.exists(config_file):
                print(f"\nOpening {config_file}...")
                # Try to open in editor
                try:
                    import subprocess
                    if sys.platform == 'win32':
                        subprocess.call(['notepad', config_file])
                    elif sys.platform == 'darwin':
                        subprocess.call(['open', '-e', config_file])
                    else:
                        # Try common Linux editors
                        for editor in ['nano', 'vim', 'gedit', 'xdg-open']:
                            try:
                                subprocess.call([editor, config_file])
                                break
                            except:
                                continue
                except:
                    print(f"Could not open editor. Please edit {config_file} manually.")
            else:
                print("Configuration file not found")
                
        elif choice == '3':
            print("\n🔍 Testing configuration...")
            
            # Test credentials
            username = self.get_config_value('username', 'Not set')
            password = self.get_config_value('password', '')
            print(f"✓ Username: {username}")
            print(f"✓ Password: {'*' * len(password) if password else 'Not set'}")
            
            # Test paths
            resume_path = self.get_config_value('resume_path', '')
            if resume_path and os.path.exists(resume_path):
                print(f"✓ Resume found: {resume_path}")
            else:
                print(f"✗ Resume not found: {resume_path}")
                
            # Test browser
            browser_config = self.get_config_value('browser', {})
            chrome_binary = browser_config.get('chrome_binary_location', '') if browser_config else ''
            if chrome_binary and os.path.exists(chrome_binary):
                print(f"✓ Chrome binary found: {chrome_binary}")
            else:
                print(f"✗ Chrome binary not found: {chrome_binary}")
                
            # Test API key
            api_key = self.get_config_value('openai_api_key', '')
            if api_key:
                print(f"✓ OpenAI API key: {api_key[:20]}...")
            else:
                print("✗ OpenAI API key not set")
                
        elif choice == '4':
            confirm = input("\nAre you sure you want to reset to defaults? (yes/no): ")
            if confirm.lower() == 'yes':
                # Create default config
                default_config = {
                    "username": "",
                    "password": "",
                    "browser": {
                        "chrome_binary_location": "/usr/bin/google-chrome-stable",
                        "headless": False
                    },
                    "resume_path": "",
                    "openai_api_key": "",
                    "search": {
                        "keywords": ["Software Engineer", "Developer"],
                        "locations": ["Remote", "United States"]
                    }
                }
                
                with open('linkedin_config.json', 'w') as f:
                    json.dump(default_config, f, indent=2)
                    
                print("Configuration reset to defaults.")
                print("Please edit the configuration file with your details.")
                self.config = default_config
                
    def run(self):
        """Main execution loop"""
        username = self.get_config_value('username', 'User')
        print(f"\n🎉 Welcome to LinkedIn Automation Platform!")
        print(f"👤 Configured user: {username}")
        
        while True:
            try:
                self.display_menu()
                choice = input("\nSelect option (1-8): ").strip()
                
                handlers = {
                    '1': self.apply_to_jobs_handler,
                    '2': self.network_maintenance_handler,
                    '3': self.article_publishing_handler,
                    '4': self.message_auto_reply_handler,
                    '5': self.web_dashboard_handler,
                    '6': self.view_statistics_handler,
                    '7': self.configuration_settings_handler
                }
                
                if choice in handlers:
                    handlers[choice]()
                    if choice != '5':  # Don't pause for web dashboard
                        input("\nPress Enter to return to menu...")
                elif choice == '8':
                    print("\n👋 Exiting LinkedIn Automation Platform...")
                    print("Have a productive day!")
                    break
                else:
                    print("❌ Invalid option. Please try again.")
                    
            except KeyboardInterrupt:
                print("\n\n⚠️  Interrupted by user")
                continue
            except Exception as e:
                print(f"\n❌ An error occurred: {e}")
                import traceback
                traceback.print_exc()
                input("\nPress Enter to continue...")


def main():
    """Main entry point - launches web dashboard by default"""
    import webbrowser
    import threading
    import argparse
    
    parser = argparse.ArgumentParser(description='LinkedIn Automation Platform')
    parser.add_argument('--cli', action='store_true', 
                       help='Use command-line interface instead of web dashboard')
    parser.add_argument('--apply', action='store_true', 
                       help='Run job application mode directly')
    parser.add_argument('--network', action='store_true',
                       help='Run network maintenance directly')
    parser.add_argument('--publish', action='store_true',
                       help='Run article publishing directly')
    parser.add_argument('--messages', action='store_true',
                       help='Run message auto-reply directly')
    
    args = parser.parse_args()
    
    # Check if running in CLI mode or with specific module
    if args.cli or args.apply or args.network or args.publish or args.messages:
        # Run in CLI mode
        platform = LinkedInAutomationPlatform()
        
        if args.apply:
            platform.apply_to_jobs_handler()
        elif args.network:
            platform.network_maintenance_handler()
        elif args.publish:
            platform.article_publishing_handler()
        elif args.messages:
            platform.message_auto_reply_handler()
        else:
            # Run interactive menu
            platform.run()
    else:
        # Launch web dashboard by default
        print("\n🚀 Starting LinkedIn Automation Dashboard...")
        print("=" * 50)
        
        try:
            # Import and run the dashboard
            from web_dashboard_backend import DashboardServer
            
            server = DashboardServer()
            
            # Open browser after short delay
            def open_browser():
                time.sleep(2)
                url = 'http://localhost:8000/dashboard'
                print(f"\n🌐 Opening dashboard in browser: {url}")
                print("If browser doesn't open, manually visit: http://localhost:8000/dashboard")
                webbrowser.open(url)
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print("\n📊 Dashboard running at: http://localhost:8000")
            print("📌 API endpoints at: http://localhost:8000/docs")
            print("\nPress Ctrl+C to stop the server")
            print("=" * 50)
            
            # Run server
            server.run()
            
        except ImportError:
            print("\n⚠️  Dashboard module not found!")
            print("Falling back to CLI mode...")
            platform = LinkedInAutomationPlatform()
            platform.run()
        except KeyboardInterrupt:
            print("\n\n✋ Dashboard stopped by user")
        except Exception as e:
            print(f"\n❌ Dashboard error: {e}")
            print("Falling back to CLI mode...")
            platform = LinkedInAutomationPlatform()
            platform.run()


if __name__ == "__main__":
    main()