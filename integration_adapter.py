#!/usr/bin/env python3
"""
Integration adapter to connect enhanced modules with existing LinkedIn automation
"""
import time
import random
from typing import Optional, Dict, Any
from enhanced_logging import EnhancedLogger, JobApplication, NetworkAction

# enhanced_config is an optional external module in some deployments.
# Import it if available; otherwise provide a lightweight fallback so the
# adapter module can still be imported (useful for static analysis /
# unit tests where the full enhanced_config package may not be present).
try:
    from enhanced_config import LinkedInConfig  # type: ignore
except Exception:  # pragma: no cover - fallback for environments without enhanced_config
    class LinkedInConfig:  # type: ignore
        """Rich fallback stub of LinkedInConfig used when enhanced_config
        is not available. It exposes the attributes and methods that
        integration_adapter references so static analyzers (Pylance)
        and unit tests can import this module without errors."""
        def __init__(self) -> None:
            # Basic credentials
            self.username: str = ""
            self.password: str = ""

            # Browser configuration
            class _Browser:
                browser_type: str = "chrome"
                chrome_binary_path: Optional[str] = None
                chromedriver_path: Optional[str] = None
                headless: bool = False
                window_size: tuple = (1200, 800)
                user_agent: Optional[str] = None
                disable_images: bool = False

            # File paths
            class _Paths:
                resume_path: str = ""
                data_directory: str = "."
                log_directory: str = "."
                screenshot_directory: str = "."

            # AI settings
            class _AI:
                openai_api_key: str = ""

            # Application-related flags
            class _Application:
                auto_use_defaults: bool = False

            # Timing/delay settings
            class _Delays:
                page_load: float = 10.0
                click_delay: float = 0.5
                typing_delay: float = 0.05

            # Search/network/auto-reply placeholders used by sample config creator
            class _Search:
                keywords: list = []

            class _Network:
                connection_message_templates: list = []

            class _AutoReply:
                reply_templates: Dict[str, str] = {}

            # Assign instances
            self.browser = _Browser()
            self.paths = _Paths()
            self.ai = _AI()
            self.application = _Application()
            self.delays = _Delays()
            self.search = _Search()
            self.network = _Network()
            self.auto_reply = _AutoReply()

        def save(self, path: str) -> None:
            """Fallback save - noop for environments without enhanced_config."""
            return None

        @classmethod
        def load(cls, path: Optional[str] = None) -> "LinkedInConfig":
            """Return a default-initialized config in absence of real implementation."""
            return cls()

        def validate(self) -> list:
            """Return empty validation issues by default."""
            return []

class LoggerAdapter:
    """Adapter to make EnhancedLogger compatible with existing code"""
    
    def __init__(self, enhanced_logger: EnhancedLogger):
        self.enhanced = enhanced_logger
        
    def info(self, message: str):
        self.enhanced.info(message)
        
    def error(self, message: str):
        self.enhanced.log_error(message)
        
    def warning(self, message: str):
        self.enhanced.warning(message)
        
    def debug(self, message: str):
        self.enhanced.debug(message)


class ConfigAdapter:
    """Adapter to make LinkedInConfig compatible with existing code"""
    
    def __init__(self, config: LinkedInConfig):
        self.enhanced_config = config
        
        # Map old attributes to new structure
        self.username = config.username
        self.password = config.password
        self.browser = config.browser.browser_type
        self.resume_path = config.paths.resume_path
        self.openai_api_key = config.ai.openai_api_key
        self.auto_use_defaults = config.application.auto_use_defaults
        
        # File paths
        self.CONFIG_FILE = 'linkedin_config.json'
        self.APPLICATION_LOG = f"{config.paths.data_directory}/applications_log.json"
        self.QUESTIONS_LOG = f"{config.paths.data_directory}/questions_log.json"
        self.ERROR_LOG = f"{config.paths.log_directory}/error_log.txt"
        self.SKIPPED_LOG = f"{config.paths.data_directory}/skipped_log.json"
        self.OVERALL_STATS_FILE = f"{config.paths.data_directory}/overall_stats.json"
        self.DASHBOARD_FILE = f"{config.paths.data_directory}/dashboard_data.json"
        
    def save(self):
        """Save configuration"""
        # Update enhanced config with any changes
        self.enhanced_config.username = self.username
        self.enhanced_config.password = self.password
        self.enhanced_config.browser.browser_type = self.browser
        self.enhanced_config.paths.resume_path = self.resume_path
        self.enhanced_config.ai.openai_api_key = self.openai_api_key
        self.enhanced_config.application.auto_use_defaults = self.auto_use_defaults
        
        self.enhanced_config.save(self.CONFIG_FILE)


class EnhancedDataManager:
    """Enhanced data manager that uses new logging system"""
    
    def __init__(self, config: LinkedInConfig, logger: EnhancedLogger):
        self.config = config
        self.logger = logger
        self.config_adapter = ConfigAdapter(config)
        
    def log_application(self, job_title: str, company: str, job_url: str, 
                       resume_used: str, status: str, job_description: str = "",
                       cover_letter: str = ""):
        """Enhanced application logging"""
        # Extract job details
        job_app = JobApplication(
            job_id=job_url.split('/')[-1] if job_url else "unknown",
            job_title=job_title,
            company=company,
            location="",  # Can be extracted from page
            job_url=job_url,
            job_description_summary=self.logger.extract_job_description_summary(job_description),
            full_description=job_description,
            resume_used=resume_used,
            cover_letter=cover_letter,
            applied_date=time.strftime("%Y-%m-%d %H:%M:%S"),
            status=status
        )
        
        self.logger.log_job_application(job_app)
        
    def log_skipped(self, job_url: str, reason: str, job_title: str = "", 
                   company: str = ""):
        """Enhanced skipped job logging"""
        self.logger.log_skipped_job(job_url, reason, job_title, company)
        
    def log_network_action(self, action_type: str, profile_name: str, 
                          profile_url: str, message: str = ""):
        """Log network actions"""
        action = NetworkAction(
            action_type=action_type,
            profile_name=profile_name,
            profile_url=profile_url,
            message_sent=message
        )
        self.logger.log_network_action(action)
        
    def load_questions(self) -> Dict[str, str]:
        """Load saved questions - compatible with existing code"""
        import json
        import os
        
        if os.path.exists(self.config_adapter.QUESTIONS_LOG):
            with open(self.config_adapter.QUESTIONS_LOG, 'r') as f:
                return json.load(f)
        return {}
        
    def save_questions(self, questions: Dict[str, str]):
        """Save questions - compatible with existing code"""
        import json
        import os
        
        os.makedirs(os.path.dirname(self.config_adapter.QUESTIONS_LOG), exist_ok=True)
        with open(self.config_adapter.QUESTIONS_LOG, 'w') as f:
            json.dump(questions, f, indent=4)
            
    def get_applied_job_urls(self) -> set:
        """Get applied job URLs from enhanced logger"""
        applied_urls = set()
        
        # Get from enhanced logger
        apps = self.logger.get_recent_applications(limit=1000)
        for app in apps:
            if app.get('job_url'):
                applied_urls.add(app['job_url'])
                
        return applied_urls


class ModifiedBrowserManager:
    """Modified browser manager using enhanced config"""
    
    def __init__(self, config: LinkedInConfig, logger: LoggerAdapter):
        self.config = config
        self.logger = logger
        self.driver: Optional[Any] = None
        self.wait_time = config.delays.page_load
        
    def init_browser(self):
        """Initialize browser with enhanced config"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        browser_config = self.config.browser
        
        if browser_config.browser_type.lower() == 'chrome':
            options = webdriver.ChromeOptions()
            
            if browser_config.chrome_binary_path:
                options.binary_location = browser_config.chrome_binary_path
                
            # Add options from config
            if browser_config.headless:
                options.add_argument('--headless')
                
            if browser_config.window_size:
                options.add_argument(f'--window-size={browser_config.window_size[0]},{browser_config.window_size[1]}')
                
            if browser_config.user_agent:
                options.add_argument(f'user-agent={browser_config.user_agent}')
                
            if browser_config.disable_images:
                prefs = {"profile.managed_default_content_settings.images": 2}
                options.add_experimental_option("prefs", prefs)
                
            # Standard options
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument('--disable-blink-features=AutomationControlled')
            
            self.logger.info(f"Using browser binary at {browser_config.chrome_binary_path}")
            
            # Use chromedriver from config
            if browser_config.chromedriver_path:
                service = Service(browser_config.chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=options)
            else:
                self.driver = webdriver.Chrome(options=options)
                
        elif browser_config.browser_type.lower() == 'edge':
            self.driver = webdriver.Edge()
        else:
            self.driver = webdriver.Chrome()
            
        self.driver.implicitly_wait(self.wait_time)
        return self.driver
        
    def safe_click(self, element, retries: int = 3, delay: Optional[float] = None):
        """Click with configurable delay"""
        # Allow an explicit delay override, otherwise use config default.
        if delay is None:
            delay = float(self.config.delays.click_delay)

        for attempt in range(retries):
            try:
                element.click()
                time.sleep(delay)
                return True
            except Exception as e:
                if attempt < retries - 1:
                    time.sleep(delay)
                    # Guard driver usage; may be None in tests/fallback environments.
                    if self.driver:
                        try:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        except Exception:
                            # Best-effort; ignore errors from scrolling attempt.
                            pass
                else:
                    self.logger.error(f"Failed to click element: {e}")
        return False
        
    # Add other methods from original BrowserManager...
    def login(self, username: str, password: str) -> bool:
        """Login to LinkedIn"""
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.common.keys import Keys
        from selenium.common.exceptions import TimeoutException
        
        try:
            if not self.driver:
                self.logger.error("Browser driver is not initialized")
                return False
            self.driver.get("https://www.linkedin.com/login")
            WebDriverWait(self.driver, self.wait_time).until(
                EC.presence_of_element_located((By.ID, 'username'))
            )
            
            # Type with delay
            username_field = self.driver.find_element(By.ID, 'username')
            for char in username:
                username_field.send_keys(char)
                time.sleep(self.config.delays.typing_delay)
                
            password_field = self.driver.find_element(By.ID, 'password')
            for char in password:
                password_field.send_keys(char)
                time.sleep(self.config.delays.typing_delay)
                
            password_field.send_keys(Keys.RETURN)
            
            WebDriverWait(self.driver, 20).until(EC.url_contains('feed'))
            self.logger.info("Successfully logged in to LinkedIn")
            return True
            
        except TimeoutException:
            self.logger.error("Login failed: timeout")
            return False
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
            
    def take_screenshot(self, label: str = "error"):
        """Take screenshot with enhanced path"""
        if not self.driver:
            return
            
        import os
        from datetime import datetime
        
        os.makedirs(self.config.paths.screenshot_directory, exist_ok=True)
        filename = os.path.join(
            self.config.paths.screenshot_directory,
            f"screenshot_{label}_{datetime.now():%Y%m%d_%H%M%S}.png"
        )
        self.driver.save_screenshot(filename)
        self.logger.info(f"Screenshot saved: {filename}")
        
    def quit(self):
        """Quit browser"""
        if self.driver:
            self.driver.quit()


def integrate_with_existing_code(existing_code_path: str = "linkedin_automation.py"):
    """
    Function to help integrate enhanced modules with existing code.
    This shows how to modify the existing script to use new modules.
    """
    
    modifications = """
    # At the top of your existing script, add:
    from enhanced_logging import EnhancedLogger
    from enhanced_config import LinkedInConfig, ConfigManager
    from integration_adapter import (
        LoggerAdapter, ConfigAdapter, EnhancedDataManager, 
        ModifiedBrowserManager
    )
    
    # In LinkedInAutomationCLI.__init__, replace:
    # self.config = Config.load()
    # self.logger = Logger(self.config)
    
    # With:
    self.enhanced_config = LinkedInConfig.load()
    self.enhanced_logger = EnhancedLogger()
    self.config = ConfigAdapter(self.enhanced_config)
    self.logger = LoggerAdapter(self.enhanced_logger)
    
    # Replace DataManager with EnhancedDataManager:
    self.data_manager = EnhancedDataManager(self.enhanced_config, self.enhanced_logger)
    
    # Replace BrowserManager with ModifiedBrowserManager:
    self.browser_manager = ModifiedBrowserManager(self.enhanced_config, self.logger)
    
    # At the start of run():
    self.enhanced_logger.start_run()
    
    # At the end of run():
    self.enhanced_logger.end_run()
    
    # In job application logging, add job description:
    self.data_manager.log_application(
        job_title, company, job_url, resume_path, 
        'submitted', job_description=job_description, 
        cover_letter=cover_letter
    )
    """
    
    return modifications


# Utility functions for migration
def migrate_old_config(old_config_file: str = "linkedin_config.json"):
    """Migrate old configuration to new format"""
    import json
    import os
    
    if not os.path.exists(old_config_file):
        print("No old configuration found")
        return
        
    with open(old_config_file, 'r') as f:
        old_data = json.load(f)
        
    # Create new config
    new_config = LinkedInConfig()
    
    # Map old values
    new_config.username = old_data.get('username', '')
    new_config.password = old_data.get('password', '')
    new_config.browser.browser_type = old_data.get('browser', 'chrome')
    new_config.paths.resume_path = old_data.get('resume_path', '')
    new_config.ai.openai_api_key = old_data.get('openai_api_key', '')
    new_config.application.auto_use_defaults = old_data.get('auto_use_defaults', False)
    
    # Save as new format
    new_config.save("linkedin_config_enhanced.json")
    print("Configuration migrated to linkedin_config_enhanced.json")
    
    
def create_sample_config():
    """Create a sample configuration file"""
    config = LinkedInConfig()
    
    # Set some example values
    config.search.keywords = [
        "Python Developer",
        "Full Stack Developer", 
        "Software Engineer",
        "Backend Developer"
    ]
    
    config.network.connection_message_templates = [
        "Hi {name}, I noticed we're both in the tech industry. Would love to connect!",
        "Hello {name}, your experience in {field} is impressive. Let's connect!",
    ]
    
    config.auto_reply.reply_templates = {
        "job_inquiry": """Thank you for reaching out about this opportunity! 
        
I'm very interested in learning more. Could you please share:
- The full job description
- Required skills and experience
- Salary range and benefits
- Whether remote work is available

I'll be happy to send you a tailored resume once I have these details.

Best regards""",
        "default": "Thank you for your message. I'll review and respond shortly."
    }
    
    config.save("linkedin_config_sample.json")
    print("Sample configuration saved to linkedin_config_sample.json")


if __name__ == "__main__":
    # Create sample files for testing
    create_sample_config()
    migrate_old_config()