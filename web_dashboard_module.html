<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Automation Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #0077b5;
            --secondary-color: #00a0dc;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-bg: #1a1a1a;
            --card-bg: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-color: #404040;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Header */
        .header {
            background-color: var(--card-bg);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            background-color: rgba(45, 45, 45, 0.9);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-menu a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s;
            position: relative;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            color: var(--text-primary);
        }
        
        .nav-menu a.active::after {
            content: '';
            position: absolute;
            bottom: -1rem;
            left: 0;
            right: 0;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            border-color: var(--primary-color);
            box-shadow: 0 8px 16px rgba(0, 119, 181, 0.2);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transform: scaleX(0);
            transition: transform 0.3s;
        }
        
        .stat-card:hover::before {
            transform: scaleX(1);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--danger-color);
        }
        
        /* Charts Section */
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .chart-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        /* Activity Feed */
        .activity-section {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .activity-item {
            padding: 1rem;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
            transition: all 0.3s;
        }
        
        .activity-item:hover {
            background-color: rgba(0, 0, 0, 0.3);
            transform: translateX(4px);
        }
        
        .activity-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .activity-title {
            font-weight: 600;
            margin: 0.25rem 0;
        }
        
        .activity-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        /* Control Panel */
        .control-panel {
            position: fixed;
            right: 2rem;
            bottom: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .control-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(0, 119, 181, 0.3);
        }
        
        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 119, 181, 0.4);
        }
        
        .control-btn.play {
            background-color: var(--success-color);
        }
        
        .control-btn.stop {
            background-color: var(--danger-color);
        }
        
        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .close-btn:hover {
            color: var(--text-primary);
        }
        
        /* Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background-color: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            transition: border-color 0.3s;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .time-range-select {
            width: 120px;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 119, 181, 0.3);
        }
        
        .btn-secondary {
            background-color: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            border-color: var(--text-primary);
            color: var(--text-primary);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .control-panel {
                right: 1rem;
                bottom: 1rem;
            }
        }
        
        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Status Indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-indicator.online {
            background-color: var(--success-color);
            box-shadow: 0 0 8px var(--success-color);
        }
        
        .status-indicator.offline {
            background-color: var(--danger-color);
            box-shadow: 0 0 8px var(--danger-color);
        }
        
        .status-indicator.idle {
            background-color: var(--warning-color);
            box-shadow: 0 0 8px var(--warning-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z"/>
                </svg>
                LinkedIn Automation Dashboard
            </div>
            <nav>
                <ul class="nav-menu">
                    <li><a href="#overview" class="active">Overview</a></li>
                    <li><a href="#applications">Applications</a></li>
                    <li><a href="#network">Network</a></li>
                    <li><a href="#content">Content</a></li>
                    <li><a href="#messages">Messages</a></li>
                    <li><a href="#settings">Settings</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">Total Applications</div>
                <div class="stat-value" id="totalApplications">0</div>
                <div class="stat-change positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 14l5-5 5 5z"/>
                    </svg>
                    <span>12% from last week</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">Network Connections</div>
                <div class="stat-value" id="networkConnections">0</div>
                <div class="stat-change positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 14l5-5 5 5z"/>
                    </svg>
                    <span>8% growth</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">Messages Sent</div>
                <div class="stat-value" id="messagesSent">0</div>
                <div class="stat-change positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 14l5-5 5 5z"/>
                    </svg>
                    <span>25 today</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">Response Rate</div>
                <div class="stat-value" id="responseRate">0%</div>
                <div class="stat-change negative">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5z"/>
                    </svg>
                    <span>3% from average</span>
                </div>
            </div>
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Application Trend</h3>
                    <select class="form-select time-range-select" aria-label="Select time range for application trend">
                        <option>Last 7 days</option>
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                    </select>
                </div>
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="applicationChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">Activity Distribution</h3>
                    <span class="status-indicator online"></span>
                    <span style="font-size: 0.875rem;">Active</span>
                </div>
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Activity Feed -->
        <div class="activity-section">
            <div class="activity-header">
                <h3 class="chart-title">Recent Activity</h3>
                <button class="btn btn-secondary" onclick="refreshActivity()">Refresh</button>
            </div>
            <div class="activity-list" id="activityList">
                <div class="activity-item">
                    <div class="activity-time">2 minutes ago</div>
                    <div class="activity-title">Applied to Senior Developer at TechCorp</div>
                    <div class="activity-description">Successfully submitted application with tailored resume</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">15 minutes ago</div>
                    <div class="activity-title">New connection accepted</div>
                    <div class="activity-description">John Doe - CTO at StartupXYZ</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">1 hour ago</div>
                    <div class="activity-title">Message sent to recruiter</div>
                    <div class="activity-description">Responded to job inquiry from Amazon</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="control-panel">
        <button class="control-btn play" onclick="startAutomation()" title="Start Automation">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
            </svg>
        </button>
        <button class="control-btn stop" onclick="stopAutomation()" title="Stop Automation">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 6h12v12H6z"/>
            </svg>
        </button>
        <button class="control-btn" onclick="openSettings()" title="Settings">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.03-.32.05-.64.05-.97c0-.33-.02-.65-.05-.97l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.03.32-.05.64-.05.97c0 .33.02.65.05.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66z"/>
            </svg>
        </button>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Settings</h2>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">Search Keywords</label>
                <textarea class="form-textarea" id="searchKeywords" placeholder="Enter keywords separated by commas"></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">Max Applications per Day</label>
                <input type="number" class="form-input" id="maxApplications" value="50">
            </div>
            
            <div class="form-group">
                <label class="form-label">Auto-Reply Enabled</label>
                <select class="form-select" id="autoReplyEnabled">
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Network Expansion</label>
                <input type="number" class="form-input" id="maxConnections" placeholder="Max connections per day" value="20">
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeSettings()">Cancel</button>
                <button class="btn btn-primary" onclick="saveSettings()">Save Changes</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script>
        // API Configuration
        const API_BASE_URL = 'http://localhost:8000';
        
        // Global variables
        let applicationChart;
        let activityChart;
        let refreshInterval;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadDashboardData();
            startAutoRefresh();
        });
        
        // Initialize Charts
        function initializeCharts() {
            // Application Trend Chart
            const appCtx = document.getElementById('applicationChart').getContext('2d');
            applicationChart = new Chart(appCtx, {
                type: 'line',
                data: {
                    labels: getLastNDays(7),
                    datasets: [{
                        label: 'Applications',
                        data: [12, 19, 15, 25, 22, 30, 28],
                        borderColor: '#0077b5',
                        backgroundColor: 'rgba(0, 119, 181, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#a0a0a0'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#a0a0a0'
                            }
                        }
                    }
                }
            });
            
            // Activity Distribution Chart
            const actCtx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(actCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Applications', 'Network', 'Messages', 'Content'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            '#0077b5',
                            '#00a0dc',
                            '#10b981',
                            '#f59e0b'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#a0a0a0',
                                padding: 20
                            }
                        }
                    }
                }
            });
        }
        
        // Load Dashboard Data
        async function loadDashboardData() {
            try {
                // Fetch stats
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                
                // Update stat cards
                updateStatCards(data);
                
                // Fetch applications
                const appsResponse = await fetch(`${API_BASE_URL}/applications`);
                const appsData = await appsResponse.json();
                
                // Update activity feed
                updateActivityFeed(appsData.applications);
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // Use mock data if API is unavailable
                useMockData();
            }
        }
        
        // Update Stat Cards
        function updateStatCards(data) {
            animateValue('totalApplications', 0, data.applied || 127, 2000);
            animateValue('networkConnections', 0, data.network_stats?.actions_accepted || 43, 2000);
            animateValue('messagesSent', 0, data.network_stats?.actions_sent || 89, 2000);
            
            // Calculate response rate
            const responseRate = data.applied > 0 ? 
                Math.round((data.network_stats?.actions_accepted || 0) / data.applied * 100) : 0;
            document.getElementById('responseRate').textContent = responseRate + '%';
        }
        
        // Update Activity Feed
        function updateActivityFeed(applications) {
            const activityList = document.getElementById('activityList');
            activityList.innerHTML = '';
            
            // Convert applications to activity items
            const activities = applications.slice(0, 5).map(app => {
                const timeAgo = getTimeAgo(new Date(app.applied_date));
                return `
                    <div class="activity-item">
                        <div class="activity-time">${timeAgo}</div>
                        <div class="activity-title">Applied to ${app.job_title} at ${app.company}</div>
                        <div class="activity-description">Status: ${app.status}</div>
                    </div>
                `;
            }).join('');
            
            activityList.innerHTML = activities || '<div class="activity-item">No recent activity</div>';
        }
        
        // Utility Functions
        function getLastNDays(n) {
            const days = [];
            for (let i = n - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
            }
            return days;
        }
        
        function getTimeAgo(date) {
            const seconds = Math.floor((new Date() - date) / 1000);
            
            let interval = seconds / 31536000;
            if (interval > 1) return Math.floor(interval) + " years ago";
            
            interval = seconds / 2592000;
            if (interval > 1) return Math.floor(interval) + " months ago";
            
            interval = seconds / 86400;
            if (interval > 1) return Math.floor(interval) + " days ago";
            
            interval = seconds / 3600;
            if (interval > 1) return Math.floor(interval) + " hours ago";
            
            interval = seconds / 60;
            if (interval > 1) return Math.floor(interval) + " minutes ago";
            
            return Math.floor(seconds) + " seconds ago";
        }
        
        function animateValue(id, start, end, duration) {
            const obj = document.getElementById(id);
            const startTimestamp = Date.now();
            
            const step = () => {
                const timestamp = Date.now();
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                obj.textContent = Math.floor(progress * (end - start) + start);
                
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            
            window.requestAnimationFrame(step);
        }
        
        // Control Functions
        async function startAutomation() {
            try {
                const response = await fetch(`${API_BASE_URL}/start`, { method: 'POST' });
                if (response.ok) {
                    showNotification('Automation started successfully', 'success');
                }
            } catch (error) {
                showNotification('Failed to start automation', 'error');
            }
        }
        
        async function stopAutomation() {
            try {
                const response = await fetch(`${API_BASE_URL}/stop`, { method: 'POST' });
                if (response.ok) {
                    showNotification('Automation stopped', 'info');
                }
            } catch (error) {
                showNotification('Failed to stop automation', 'error');
            }
        }
        
        function openSettings() {
            document.getElementById('settingsModal').style.display = 'block';
            loadSettings();
        }
        
        function closeSettings() {
            document.getElementById('settingsModal').style.display = 'none';
        }
        
        async function loadSettings() {
            try {
                const response = await fetch(`${API_BASE_URL}/config`);
                const config = await response.json();
                
                document.getElementById('searchKeywords').value = config.search_keywords.join(', ');
                document.getElementById('maxApplications').value = config.max_applications;
                document.getElementById('autoReplyEnabled').value = config.auto_reply_enabled;
                document.getElementById('maxConnections').value = config.max_connections || 20;
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        }
        
        async function saveSettings() {
            const settings = {
                search_keywords: document.getElementById('searchKeywords').value.split(',').map(k => k.trim()),
                max_applications: parseInt(document.getElementById('maxApplications').value),
                auto_reply_enabled: document.getElementById('autoReplyEnabled').value === 'true',
                max_connections: parseInt(document.getElementById('maxConnections').value)
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });
                
                if (response.ok) {
                    showNotification('Settings saved successfully', 'success');
                    closeSettings();
                }
            } catch (error) {
                showNotification('Failed to save settings', 'error');
            }
        }
        
        function refreshActivity() {
            loadDashboardData();
            showNotification('Activity refreshed', 'info');
        }
        
        function showNotification(message, type) {
            // Simple notification (could be enhanced with a toast library)
            console.log(`${type}: ${message}`);
        }
        
        function startAutoRefresh() {
            refreshInterval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        // Mock data for demo
        function useMockData() {
            updateStatCards({
                applied: 127,
                network_stats: {
                    actions_accepted: 43,
                    actions_sent: 89
                }
            });
            
            const mockActivities = [
                {
                    job_title: 'Senior Full Stack Developer',
                    company: 'TechCorp',
                    applied_date: new Date(Date.now() - 120000).toISOString(),
                    status: 'submitted'
                },
                {
                    job_title: '.NET Developer',
                    company: 'Innovation Labs',
                    applied_date: new Date(Date.now() - 900000).toISOString(),
                    status: 'submitted'
                },
                {
                    job_title: 'Cloud Architect',
                    company: 'CloudFirst Inc',
                    applied_date: new Date(Date.now() - 3600000).toISOString(),
                    status: 'submitted'
                }
            ];
            
            updateActivityFeed(mockActivities);
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>