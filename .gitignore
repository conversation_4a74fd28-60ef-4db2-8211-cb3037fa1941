# Dependencies
node_modules/
*/node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
.pnpm-debug.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.pyc
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage
.pytest_cache/
cover/
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Environment variables and secrets
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local
*.env
.envrc
secrets.json
config.json
credentials.json
.secrets/
.credentials/

# API Keys and sensitive files
**/api_key*
**/secret*
**/credential*
**/password*
**/*key*.json
**/*secret*.json
**/*credential*.json
**/*token*.json

# LinkedIn specific secrets (actual files with passwords/keys)
linkedin_config.json
linkedin_config_old*.json
user-profile-config.json

# LinkedIn data files (logs, applications, etc.)
applications_log.json
questions_log.json
dashboard_data.json
error_log.txt

# BUT allow template/example files (no secrets)
!*.example.json
!*-template.json

# SSH Keys (never commit)
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub
*.pem
*.key

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs (but keep some important ones)
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Build outputs
build/
dist/
out/
target/
.next/
.nuxt/
.output/

# Cache directories
.cache/
.parcel-cache/
.sass-cache/

# Docker
.dockerignore
docker-compose.override.yml

# Miscellaneous
*.tgz
*.tar.gz
*.zip
.nyc_output/
coverage/

# Keep important files (override some exclusions)
!**/linkedin*.py
!**/linkedin*.js
!**/linkedin*.json
!**/LinkedIn*.pdf
!**/linkedin*.log
!**/questions*.pdf
!**/Questions*.pdf
!**/logs/important*.log
!**/logs/linkedin*.log
!README*.pdf
!CHANGELOG*.pdf
!DOCS*.pdf
!docs*.pdf
# === SECURITY: Prevent Secret Exposure ===
# Based on GitGuardian alerts - NEVER commit these patterns

# GitHub Personal Access Tokens
ghp_*
github_pat_*
gho_*
ghu_*

# Generic Private Keys (all formats)
*private*key*
*privatekey*
*.key
*.pem
*.p12
*.pfx
*.jks
*.keystore
*-key.pem
*_key.pem
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*

# Database Passwords and Connection Strings
*database*password*
*db*password*
*mysql*password*
*postgres*password*
*mongodb*password*
*redis*password*
DATABASE_URL*
DB_PASSWORD*
MONGO_URI*
REDIS_URL*

# Generic Passwords (all variations)
*password*
*passwd*
*pwd*
PASSWORD*
PASSWD*
PWD*

# Authentication Tokens
*auth*token*
*access*token*
*bearer*token*
*jwt*token*
*api*token*
AUTH_TOKEN*
ACCESS_TOKEN*
BEARER_TOKEN*
JWT_SECRET*

# API Keys (all major services)
*api*key*
*apikey*
API_KEY*
OPENAI_API_KEY*
GOOGLE_API_KEY*
AWS_ACCESS_KEY*
AWS_SECRET_KEY*
STRIPE_SECRET_KEY*

# OAuth and Client Secrets
*client*secret*
*oauth*secret*
CLIENT_SECRET*
OAUTH_SECRET*

# Session and Security Keys
*session*key*
*secret*key*
*security*key*
SESSION_SECRET*
SECRET_KEY*
SECURITY_KEY*

# Configuration files that often contain secrets
config/secrets/
config/private/
.env*
*.env
environment.prod*
environment.staging*
secrets.json
private.json
credentials.json

