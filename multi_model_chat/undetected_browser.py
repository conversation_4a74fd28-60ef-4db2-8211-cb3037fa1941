#!/usr/bin/env python3
"""
Undetected Browser Solution
Uses undetected-chromedriver to completely bypass browser detection
"""

import os
import time
import subprocess
import sys

def install_undetected_chromedriver():
    """Install undetected-chromedriver if not present"""
    try:
        import undetected_chromedriver
        print("✅ undetected-chromedriver already installed")
        return True
    except ImportError:
        print("📦 Installing undetected-chromedriver...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "undetected-chromedriver"])
            print("✅ undetected-chromedriver installed successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to install undetected-chromedriver: {e}")
            return False

def create_undetected_driver():
    """Create an undetected Chrome driver"""
    try:
        import undetected_chromedriver as uc
        
        options = uc.ChromeOptions()
        
        # Basic options
        options.add_argument('--no-first-run')
        options.add_argument('--no-service-autorun')
        options.add_argument('--no-default-browser-check')
        
        # Create driver with undetected-chromedriver
        driver = uc.Chrome(options=options, version_main=None)
        
        print("✅ Undetected Chrome driver created!")
        return driver
        
    except Exception as e:
        print(f"❌ Error creating undetected driver: {e}")
        return None

def test_google_services():
    """Test Google services with undetected browser"""
    if not install_undetected_chromedriver():
        return False
    
    driver = create_undetected_driver()
    if not driver:
        return False
    
    try:
        print("🧪 Testing Google services...")
        
        # Test Gemini
        print("\n🤖 Testing Gemini...")
        driver.get("https://gemini.google.com")
        time.sleep(5)
        
        if "this browser or app may not be secure" in driver.page_source.lower():
            print("❌ Gemini: Still showing security warning")
        elif "sign in" in driver.page_source.lower():
            print("✅ Gemini: Reached login page (no security block)")
        else:
            print("✅ Gemini: Accessible")
        
        # Test ChatGPT
        print("\n🤖 Testing ChatGPT...")
        driver.get("https://chat.openai.com")
        time.sleep(5)
        
        if "cloudflare" in driver.page_source.lower():
            print("❌ ChatGPT: Cloudflare challenge detected")
        elif "login" in driver.page_source.lower():
            print("✅ ChatGPT: Reached login page")
        else:
            print("✅ ChatGPT: Accessible")
            
        # Test Claude
        print("\n🤖 Testing Claude...")
        driver.get("https://claude.ai")
        time.sleep(5)
        
        if "cloudflare" in driver.page_source.lower():
            print("❌ Claude: Cloudflare challenge detected")
        elif "login" in driver.page_source.lower():
            print("✅ Claude: Reached login page")
        else:
            print("✅ Claude: Accessible")
        
        print("\n🎯 Test complete! Check results above.")
        input("Press Enter to close browser...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def create_undetected_client_class():
    """Create a new client class using undetected-chromedriver"""
    client_code = '''
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import random

class UndetectedWebClient:
    """Web client using undetected-chromedriver"""
    
    def __init__(self, url: str):
        self.url = url
        self.driver = None
        self.wait = None
        
    def setup(self):
        """Setup undetected driver"""
        try:
            options = uc.ChromeOptions()
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun')
            options.add_argument('--no-default-browser-check')
            
            self.driver = uc.Chrome(options=options, version_main=None)
            self.wait = WebDriverWait(self.driver, 30)
            return True
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def navigate(self):
        """Navigate to the service"""
        try:
            self.driver.get(self.url)
            time.sleep(3)
            
            # Check for blocks
            page_text = self.driver.page_source.lower()
            if "this browser or app may not be secure" in page_text:
                print("❌ Browser security block detected")
                return False
            elif "cloudflare" in page_text:
                print("🛡️ Cloudflare challenge detected")
                return "challenge"
            else:
                print("✅ Page loaded successfully")
                return True
                
        except Exception as e:
            print(f"❌ Navigation failed: {e}")
            return False
    
    def close(self):
        """Close the driver"""
        if self.driver:
            self.driver.quit()
'''

    with open("undetected_client.py", "w") as f:
        f.write(client_code)
    
    print("✅ Created undetected_client.py")

def manual_test_session():
    """Start a manual test session"""
    if not install_undetected_chromedriver():
        return
    
    driver = create_undetected_driver()
    if not driver:
        return
    
    try:
        print("🎯 Manual Test Session Started")
        print("=" * 50)
        
        services = {
            "1": ("Gemini", "https://gemini.google.com"),
            "2": ("ChatGPT", "https://chat.openai.com"),
            "3": ("Claude", "https://claude.ai")
        }
        
        while True:
            print("\nChoose service to test:")
            for key, (name, url) in services.items():
                print(f"{key}. {name}")
            print("q. Quit")
            
            choice = input("\nEnter choice: ").strip()
            
            if choice.lower() == 'q':
                break
            elif choice in services:
                name, url = services[choice]
                print(f"\n🌐 Opening {name}...")
                driver.get(url)
                
                print(f"✅ {name} opened. You can now:")
                print("- Complete any challenges manually")
                print("- Login to the service")
                print("- Test the interface")
                input("Press Enter when done testing...")
            else:
                print("❌ Invalid choice")
                
    except KeyboardInterrupt:
        print("\n👋 Session ended")
    finally:
        driver.quit()

if __name__ == "__main__":
    print("🚀 Undetected Browser Solution")
    print("=" * 40)
    
    choice = input("""
Choose option:
1. Install undetected-chromedriver
2. Test Google services
3. Manual test session
4. Create undetected client class
5. Exit

Enter choice (1-5): """)
    
    if choice == "1":
        install_undetected_chromedriver()
    elif choice == "2":
        test_google_services()
    elif choice == "3":
        manual_test_session()
    elif choice == "4":
        create_undetected_client_class()
        print("💡 You can now import and use UndetectedWebClient")
    else:
        print("👋 Goodbye!")