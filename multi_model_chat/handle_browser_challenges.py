#!/usr/bin/env python3
"""
Handle Browser Challenges (Cloudflare, CAPTCHA, etc.)
Provides strategies for dealing with anti-bot measures
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def create_challenge_resistant_browser():
    """Create a browser instance optimized to pass challenges"""
    opts = Options()
    
    # Maximum stealth configuration
    opts.add_argument('--disable-blink-features=AutomationControlled')
    opts.add_experimental_option("excludeSwitches", ["enable-automation"])
    opts.add_experimental_option('useAutomationExtension', False)
    opts.add_argument('--disable-web-security')
    opts.add_argument('--allow-running-insecure-content')
    opts.add_argument('--disable-features=VizDisplayCompositor')
    opts.add_argument('--disable-ipc-flooding-protection')
    
    # User agent and window settings
    opts.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    opts.add_argument('--window-size=1920,1080')
    opts.add_argument('--start-maximized')
    
    # Additional stealth options
    prefs = {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.managed_default_content_settings.images": 1,
        "profile.default_content_setting_values.plugins": 1,
        "profile.content_settings.plugin_whitelist.adobe-flash-player": 1,
        "profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player": 1
    }
    opts.add_experimental_option("prefs", prefs)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=opts)
    
    # Execute anti-detection scripts
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
    driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
    
    return driver

def test_chatgpt_manual():
    """Test ChatGPT with manual challenge handling"""
    print("🧪 Testing ChatGPT with Challenge Handling")
    print("=" * 50)
    
    driver = create_challenge_resistant_browser()
    
    try:
        print("🌐 Opening ChatGPT...")
        driver.get("https://chat.openai.com")
        
        print("⏱️  Waiting 5 seconds for page load...")
        time.sleep(5)
        
        # Check for challenges
        page_text = driver.page_source.lower()
        
        if "cloudflare" in page_text or "verify you are human" in page_text:
            print("🛡️  Challenge detected!")
            print("📋 Manual steps:")
            print("   1. Complete the 'Verify you are human' checkbox")
            print("   2. Wait for the page to reload")
            print("   3. Login to ChatGPT if needed")
            print("   4. Navigate to the chat interface")
            print()
            input("Press Enter when you've completed all steps and can see the chat interface...")
        else:
            print("✅ No challenges detected!")
        # Test if we can find chat elements
        try:
            chat_elements = driver.find_elements(By.CSS_SELECTOR, "textarea, div[contenteditable='true']")
            if chat_elements:
                print("✅ Chat interface found!")
                return True
            else:
                print("❌ Chat interface not found")
                return False
        except:
            print("❌ Error finding chat elements")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        input("Press Enter to close browser...")
        driver.quit()

def manual_setup_guide():
    """Provide manual setup guide for challenges"""
    print("🎯 Manual Setup Guide for AI Services")
    print("=" * 50)
    print()
    
    print("🤖 ChatGPT (chat.openai.com):")
    print("   1. Open browser normally")
    print("   2. Complete Cloudflare challenge")
    print("   3. Login to ChatGPT")
    print("   4. Keep browser open")
    print("   5. Run automation in separate window")
    print()
    
    print("🤖 Claude (claude.ai):")
    print("   1. Open browser and login")
    print("   2. Generally more automation-friendly")
    print("   3. Less likely to show challenges")
    print()
    
    print("🤖 Gemini (gemini.google.com):")
    print("   1. Most restrictive - often blocks automation")
    print("   2. Use regular browser for login")
    print("   3. Consider disabling for automated testing")
    print()
    
    print("💡 Pro Tips:")
    print("   • Start with Claude (most reliable)")
    print("   • Use ChatGPT after manual challenge completion")
    print("   • Keep browsers open between sessions")
    print("   • Test one model at a time first")

def quick_test_all_models():
    """Quick test to see which models work"""
    print("🔍 Quick Model Availability Test")
    print("=" * 40)
    
    models = {
        "ChatGPT": "https://chat.openai.com",
        "Claude": "https://claude.ai", 
        "Gemini": "https://gemini.google.com"
    }
    
    driver = create_challenge_resistant_browser()
    
    for name, url in models.items():
        try:
            print(f"\n🌐 Testing {name}...")
            driver.get(url)
            time.sleep(3)
            
            page_text = driver.page_source.lower()
            
            if "cloudflare" in page_text or "verify you are human" in page_text:
                print(f"   🛡️  {name}: Challenge detected")
            elif "login" in page_text or "sign in" in page_text:
                print(f"   🔐 {name}: Login required")
            else:
                print(f"   ✅ {name}: Accessible")
                
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    driver.quit()
    print("\n✅ Test complete!")

if __name__ == "__main__":
    print("🛡️  Browser Challenge Handler")
    print("=" * 40)
    
    choice = input("""
Choose an option:
1. Test ChatGPT with manual challenge handling
2. Quick test all models availability  
3. Show manual setup guide
4. Exit

Enter choice (1-4): """)
    
    if choice == "1":
        test_chatgpt_manual()
    elif choice == "2":
        quick_test_all_models()
    elif choice == "3":
        manual_setup_guide()
    else:
        print("👋 Goodbye!")