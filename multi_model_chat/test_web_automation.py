#!/usr/bin/env python3
"""
Test Web Automation for AI Models
Quick test to verify web automation clients work
"""

import time
from web_automation_clients import WebModelClientFactory


def test_single_model(model_name: str = "chatgpt"):
    """Test a single model's web automation"""
    print(f"🧪 Testing {model_name.upper()} Web Automation")
    print("=" * 50)
    
    config = {
        "models": {
            model_name: {
                "enabled": True,
                "personality": "Test assistant",
                "headless": False,
                "timeout": 60
            }
        }
    }
    
    factory = WebModelClientFactory(config)
    client = factory.get_client(model_name, headless=False)
    
    if not client:
        print(f"❌ Could not create {model_name} client")
        return False
        
    try:
        print(f"🌐 Opening {model_name} web interface...")
        print("⚠️  Please complete any login steps in the browser")
        
        if client.login():
            print(f"✅ {model_name} login successful!")
            
            # Test message
            test_message = "Hello! Please respond with just 'Hello back!' and nothing else."
            print(f"\n📤 Sending test message: {test_message}")
            
            response = client.send_message(test_message)
            print(f"📥 Response: {response}")
            
            # Keep browser open for a moment
            print("\n⏱️  Keeping browser open for 10 seconds...")
            time.sleep(10)
            
            return True
        else:
            print(f"❌ {model_name} login failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing {model_name}: {e}")
        return False
    finally:
        print(f"🧹 Closing {model_name} browser...")
        client.close()
        

def interactive_test():
    """Interactive test allowing user to choose model"""
    models = ["chatgpt", "claude", "gemini"]
    
    print("🧪 Web Automation Test")
    print("=" * 40)
    print("Available models:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model.upper()}")
        
    try:
        choice = int(input("\nSelect model to test (1-3): "))
        if 1 <= choice <= len(models):
            selected_model = models[choice - 1]
            test_single_model(selected_model)
        else:
            print("❌ Invalid choice")
    except ValueError:
        print("❌ Please enter a number")
    except KeyboardInterrupt:
        print("\n👋 Test cancelled")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Web Automation")
    parser.add_argument("--model", choices=["chatgpt", "claude", "gemini"], 
                       help="Model to test")
    parser.add_argument("--interactive", action="store_true", 
                       help="Interactive mode")
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_test()
    elif args.model:
        test_single_model(args.model)
    else:
        print("Usage:")
        print("  python test_web_automation.py --interactive")
        print("  python test_web_automation.py --model chatgpt")