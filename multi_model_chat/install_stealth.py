#!/usr/bin/env python3
"""
Install and setup stealth browser components
"""

import subprocess
import sys
import os

def install_undetected_chromedriver():
    """Install undetected-chromedriver"""
    print("📦 Installing undetected-chromedriver...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "undetected-chromedriver"
        ])
        print("✅ undetected-chromedriver installed successfully!")
        return True
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        return False

def test_installation():
    """Test if undetected-chromedriver works"""
    try:
        import undetected_chromedriver as uc
        print("✅ undetected-chromedriver import successful")
        
        # Quick test
        print("🧪 Testing driver creation...")
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-first-run')
        
        driver = uc.Chrome(options=options, version_main=None)
        driver.get("https://httpbin.org/user-agent")
        
        print("✅ Driver test successful!")
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def create_quick_test():
    """Create a quick test script"""
    test_script = '''#!/usr/bin/env python3
"""Quick test for stealth browser"""

import undetected_chromedriver as uc
import time

def quick_test():
    print("🚀 Quick Stealth Browser Test")
    
    options = uc.ChromeOptions()
    options.add_argument('--no-first-run')
    
    driver = uc.Chrome(options=options, version_main=None)
    
    try:
        # Test Google services
        services = [
            ("Gemini", "https://gemini.google.com"),
            ("ChatGPT", "https://chat.openai.com"),
            ("Claude", "https://claude.ai")
        ]
        
        for name, url in services:
            print(f"\\n🌐 Testing {name}...")
            driver.get(url)
            time.sleep(3)
            
            page_text = driver.page_source.lower()
            
            if "this browser or app may not be secure" in page_text:
                print(f"   ❌ {name}: Security block detected")
            elif "cloudflare" in page_text:
                print(f"   🛡️ {name}: Cloudflare challenge")
            elif "sign in" in page_text or "login" in page_text:
                print(f"   ✅ {name}: Login page reached")
            else:
                print(f"   ✅ {name}: Accessible")
        
        print("\\n✅ Test complete!")
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    quick_test()
'''
    
    with open("quick_stealth_test.py", "w") as f:
        f.write(test_script)
    
    print("✅ Created quick_stealth_test.py")

def main():
    print("🕵️ Stealth Browser Setup")
    print("=" * 30)
    
    # Check if already installed
    try:
        import undetected_chromedriver
        print("✅ undetected-chromedriver already installed")
        
        choice = input("\\nTest installation? (y/n): ")
        if choice.lower() == 'y':
            test_installation()
            
    except ImportError:
        print("📦 undetected-chromedriver not found")
        
        choice = input("Install now? (y/n): ")
        if choice.lower() == 'y':
            if install_undetected_chromedriver():
                print("\\n🧪 Testing installation...")
                test_installation()
        else:
            print("👋 Installation skipped")
            return
    
    # Create test script
    choice = input("\\nCreate quick test script? (y/n): ")
    if choice.lower() == 'y':
        create_quick_test()
        
        print("\\n🎯 Next steps:")
        print("1. Run: python quick_stealth_test.py")
        print("2. Or run: python stealth_clients.py")
        print("3. Or run: python undetected_browser.py")

if __name__ == "__main__":
    main()