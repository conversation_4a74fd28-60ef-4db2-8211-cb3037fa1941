#!/usr/bin/env python3
"""
Browser Attachment Solutions
Connect to existing browser sessions or use different browsers
"""

import os
import json
import subprocess
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager


def start_chrome_debug_mode():
    """Start Chrome in debug mode for attachment"""
    print("🚀 Starting Chrome in debug mode...")
    
    # Kill existing Chrome instances
    try:
        subprocess.run(["pkill", "-f", "chrome"], check=False)
        time.sleep(2)
    except:
        pass
    
    # Start Chrome with remote debugging
    chrome_cmd = [
        "google-chrome",
        "--remote-debugging-port=9222",
        "--user-data-dir=/tmp/chrome-debug",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-blink-features=AutomationControlled"
    ]
    
    try:
        subprocess.Popen(chrome_cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ Chrome started in debug mode (port 9222)")
        print("🌐 You can now manually:")
        print("   1. Go to ChatGPT or Claude")
        print("   2. Complete Cloudflare challenges") 
        print("   3. Login to the services")
        print("   4. Keep the browser open")
        return True
    except Exception as e:
        print(f"❌ Error starting Chrome: {e}")
        return False


def attach_to_existing_chrome():
    """Create driver that attaches to existing Chrome session"""
    try:
        opts = ChromeOptions()
        opts.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        
        # Don't add automation flags when attaching
        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=opts)
        
        print("✅ Attached to existing Chrome session!")
        return driver
        
    except Exception as e:
        print(f"❌ Error attaching to Chrome: {e}")
        print("Make sure Chrome is running with --remote-debugging-port=9222")
        return None


def use_firefox_instead():
    """Use Firefox instead of Chrome (often bypasses different checks)"""
    try:
        opts = FirefoxOptions()
        
        # Firefox-specific settings
        opts.set_preference("dom.webdriver.enabled", False)
        opts.set_preference("useAutomationExtension", False)
        opts.set_preference("general.useragent.override", 
                          "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0")
        
        service = FirefoxService(GeckoDriverManager().install())
        driver = webdriver.Firefox(service=service, options=opts)
        
        print("✅ Firefox driver created!")
        return driver
        
    except Exception as e:
        print(f"❌ Error creating Firefox driver: {e}")
        return None


def create_user_profile_chrome():
    """Create Chrome with a persistent user profile"""
    try:
        opts = ChromeOptions()
        
        # Use a persistent profile directory
        profile_dir = os.path.expanduser("~/chrome-ai-automation")
        opts.add_argument(f"--user-data-dir={profile_dir}")
        
        # Minimal flags for less detection
        opts.add_argument("--no-first-run")
        opts.add_argument("--no-default-browser-check")
        opts.add_argument("--disable-blink-features=AutomationControlled")
        opts.add_experimental_option("excludeSwitches", ["enable-automation"])
        opts.add_experimental_option('useAutomationExtension', False)
        
        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=opts)
        
        # Hide webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print(f"✅ Chrome with persistent profile: {profile_dir}")
        return driver
        
    except Exception as e:
        print(f"❌ Error creating Chrome with profile: {e}")
        return None


def test_browser_options():
    """Test all browser attachment options"""
    print("🧪 Testing Browser Options")
    print("=" * 50)
    
    options = [
        ("Attach to existing Chrome", attach_to_existing_chrome),
        ("Use Firefox instead", use_firefox_instead),
        ("Chrome with user profile", create_user_profile_chrome)
    ]
    
    for name, func in options:
        print(f"\n🔍 Testing: {name}")
        try:
            driver = func()
            if driver:
                driver.get("https://claude.ai")
                time.sleep(3)
                print(f"   ✅ {name}: Success!")
                
                # Quick challenge check
                if "cloudflare" in driver.page_source.lower():
                    print(f"   🛡️ Cloudflare detected")
                else:
                    print(f"   🎯 No challenges detected!")
                    
                driver.quit()
            else:
                print(f"   ❌ {name}: Failed to create")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")


def setup_attachment_workflow():
    """Setup the attachment workflow"""
    print("🔧 Browser Attachment Setup")
    print("=" * 40)
    
    choice = input("""
Choose browser strategy:
1. Attach to existing Chrome (Recommended)
2. Use Firefox instead of Chrome
3. Chrome with persistent profile
4. Test all options
5. Start Chrome debug mode

Enter choice (1-5): """)
    
    if choice == "1":
        print("\n📋 Attachment Workflow:")
        print("1. Run: python browser_attachment.py --start-debug")
        print("2. Manually login to AI services")
        print("3. Run: python web_chat_orchestrator.py --attach")
        
    elif choice == "2":
        driver = use_firefox_instead()
        if driver:
            print("🦊 Firefox ready! Navigate to AI services manually.")
            input("Press Enter to close...")
            driver.quit()
            
    elif choice == "3":
        driver = create_user_profile_chrome()
        if driver:
            print("👤 Chrome with profile ready! Login once and it will remember.")
            input("Press Enter to close...")
            driver.quit()
            
    elif choice == "4":
        test_browser_options()
        
    elif choice == "5":
        start_chrome_debug_mode()
        print("\n📋 Next steps:")
        print("1. Use the opened Chrome to login to AI services")
        print("2. Run: python web_chat_orchestrator.py --attach")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Browser Attachment Solutions")
    parser.add_argument("--start-debug", action="store_true", help="Start Chrome in debug mode")
    parser.add_argument("--test-attach", action="store_true", help="Test attachment")
    parser.add_argument("--firefox", action="store_true", help="Test Firefox")
    
    args = parser.parse_args()
    
    if args.start_debug:
        start_chrome_debug_mode()
    elif args.test_attach:
        driver = attach_to_existing_chrome()
        if driver:
            print("Testing attachment...")
            driver.get("https://claude.ai")
            input("Press Enter to close...")
            driver.quit()
    elif args.firefox:
        driver = use_firefox_instead()
        if driver:
            driver.get("https://claude.ai")
            input("Press Enter to close...")
            driver.quit()
    else:
        setup_attachment_workflow()