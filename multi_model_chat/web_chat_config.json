{"models": {"chatgpt": {"enabled": true, "type": "web", "url": "https://chat.openai.com", "personality": "Creative strategist who thinks outside the box", "headless": false, "timeout": 60}, "claude": {"enabled": true, "type": "web", "url": "https://claude.ai", "personality": "Thoughtful analyst who considers multiple perspectives", "headless": false, "timeout": 60}, "gemini": {"enabled": false, "type": "web", "url": "https://gemini.google.com", "personality": "Detail-oriented researcher with comprehensive analysis", "headless": false, "timeout": 60, "note": "Disabled due to Google's browser detection. Use alternative_gemini_setup.py for workarounds"}}, "chat_settings": {"max_rounds": 3, "include_previous_messages": true, "moderator_prompts": true, "save_conversations": true, "conversation_dir": "conversations", "delays": {"between_models": [2, 5], "typing_speed": [0.05, 0.15], "response_wait": [2, 4]}}, "browser_settings": {"headless": false, "window_size": "1920,1080", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "implicit_wait": 10}}