#!/usr/bin/env python3
"""
Stealth Web Clients using undetected-chromedriver
Specifically designed to bypass "This browser or app may not be secure" errors
"""

import time
import random
from typing import Dict, Optional
from abc import ABC, abstractmethod


class StealthWebClient(ABC):
    """Base class for stealth web clients using undetected-chromedriver"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.driver = None
        self.wait = None
        self.logged_in = False
        
    def _setup_stealth_driver(self):
        """Setup undetected Chrome driver"""
        try:
            import undetected_chromedriver as uc
            from selenium.webdriver.support.ui import WebDriverWait
            
            options = uc.ChromeOptions()
            
            # Minimal options for maximum stealth
            options.add_argument('--no-first-run')
            options.add_argument('--no-service-autorun') 
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-extensions')
            
            # Create undetected driver
            self.driver = uc.Chrome(options=options, version_main=None)
            self.wait = WebDriverWait(self.driver, 30)
            
            print("✅ Stealth driver created with undetected-chromedriver")
            return True
            
        except ImportError:
            print("❌ undetected-chromedriver not installed!")
            print("💡 Run: pip install undetected-chromedriver")
            return False
        except Exception as e:
            print(f"❌ Stealth driver setup failed: {e}")
            return False
    
    def _human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
            
    def _random_delay(self, min_sec: float = 1.0, max_sec: float = 3.0):
        """Add random delay to mimic human behavior"""
        time.sleep(random.uniform(min_sec, max_sec))
    
    @abstractmethod
    def login(self) -> bool:
        """Login to the service"""
        pass
        
    @abstractmethod 
    def send_message(self, message: str) -> str:
        """Send message and get response"""
        pass
        
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None


class StealthGeminiClient(StealthWebClient):
    """Stealth client specifically for Google Gemini"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        self.base_url = "https://gemini.google.com"
        
    def login(self) -> bool:
        """Login to Gemini using stealth driver"""
        try:
            if not self._setup_stealth_driver():
                return False
                
            print("🌐 Opening Gemini with stealth driver...")
            self.driver.get(self.base_url)
            self._random_delay(3, 6)
            
            # Check for security blocks
            page_text = self.driver.page_source.lower()
            
            if "this browser or app may not be secure" in page_text:
                print("❌ Still getting browser security block")
                print("💡 Try using a regular browser for Gemini")
                return False
            elif "sign in" in page_text or "login" in page_text:
                print("✅ Reached Gemini login page!")
                print("⚠️  Please complete login manually in the browser")
                input("Press Enter when logged in and ready...")
                return self._is_logged_in()
            else:
                print("✅ Gemini loaded successfully")
                return self._is_logged_in()
                
        except Exception as e:
            print(f"❌ Gemini stealth login error: {e}")
            return False
    
    def _is_logged_in(self) -> bool:
        """Check if logged in to Gemini"""
        try:
            # Look for chat interface elements
            from selenium.webdriver.common.by import By
            
            chat_indicators = [
                "rich-textarea",
                "[data-test-id='input-area']", 
                ".ql-editor",
                "textarea"
            ]
            
            for selector in chat_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            return False
        except:
            return False
    
    def send_message(self, message: str) -> str:
        """Send message to Gemini"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.webdriver.support import expected_conditions as EC
            
            if not self.logged_in:
                if not self.login():
                    return "[Gemini login required]"
            
            # Find input field
            input_selectors = [
                "rich-textarea",
                "[data-test-id='input-area']",
                ".ql-editor", 
                "textarea"
            ]
            
            message_input = None
            for selector in input_selectors:
                try:
                    message_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
            
            if not message_input:
                return "[Gemini input not found]"
            
            # Send message
            message_input.click()
            self._human_like_typing(message_input, message)
            self._random_delay(0.5, 1.5)
            message_input.send_keys(Keys.RETURN)
            
            return self._get_response()
            
        except Exception as e:
            return f"[Gemini error: {e}]"
    
    def _get_response(self) -> str:
        """Get response from Gemini"""
        try:
            from selenium.webdriver.common.by import By
            
            self._random_delay(2, 4)
            
            response_selectors = [
                "[data-test-id='model-response']",
                ".model-response-text",
                ".response-container"
            ]
            
            max_wait = 60
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                for selector in response_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            last_response = elements[-1].text.strip()
                            if last_response and len(last_response) > 10:
                                return last_response
                    except:
                        continue
                        
                time.sleep(2)
                
            return "[Gemini: Response timeout]"
            
        except Exception as e:
            return f"[Gemini response error: {e}]"


class StealthChatGPTClient(StealthWebClient):
    """Stealth client for ChatGPT"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        self.base_url = "https://chat.openai.com"
        
    def login(self) -> bool:
        """Login to ChatGPT using stealth driver"""
        try:
            if not self._setup_stealth_driver():
                return False
                
            print("🌐 Opening ChatGPT with stealth driver...")
            self.driver.get(self.base_url)
            self._random_delay(3, 6)
            
            # Check for challenges
            page_text = self.driver.page_source.lower()
            
            if "cloudflare" in page_text or "verify you are human" in page_text:
                print("🛡️ Cloudflare challenge detected")
                print("⚠️  Please complete the challenge manually")
                input("Press Enter when challenge is complete...")
            
            print("⚠️  Please complete login manually in the browser")
            input("Press Enter when logged in and ready...")
            
            self.logged_in = self._is_logged_in()
            return self.logged_in
            
        except Exception as e:
            print(f"❌ ChatGPT stealth login error: {e}")
            return False
    
    def _is_logged_in(self) -> bool:
        """Check if logged in to ChatGPT"""
        try:
            from selenium.webdriver.common.by import By
            
            chat_indicators = [
                "textarea[placeholder*='message']",
                "#prompt-textarea",
                ".composer-text-input textarea"
            ]
            
            for selector in chat_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            return False
        except:
            return False
    
    def send_message(self, message: str) -> str:
        """Send message to ChatGPT"""
        # Implementation similar to regular ChatGPT client
        # but using stealth driver
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            from selenium.webdriver.support import expected_conditions as EC
            
            if not self.logged_in:
                if not self.login():
                    return "[ChatGPT login required]"
            
            # Find and use input field (similar to regular client)
            input_selectors = [
                "textarea[placeholder*='message']",
                "#prompt-textarea",
                ".composer-text-input textarea"
            ]
            
            message_input = None
            for selector in input_selectors:
                try:
                    message_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
            
            if not message_input:
                return "[ChatGPT input not found]"
            
            self._human_like_typing(message_input, message)
            message_input.send_keys(Keys.RETURN)
            
            return "[ChatGPT response - implement response capture]"
            
        except Exception as e:
            return f"[ChatGPT error: {e}]"


def test_stealth_clients():
    """Test stealth clients"""
    print("🧪 Testing Stealth Clients")
    print("=" * 40)
    
    # Test Gemini stealth client
    print("\n🤖 Testing Stealth Gemini Client...")
    config = {"personality": "Test assistant"}
    
    gemini_client = StealthGeminiClient(config)
    
    try:
        if gemini_client.login():
            print("✅ Stealth Gemini login successful!")
            
            # Test message
            response = gemini_client.send_message("Hello! Please say hi back.")
            print(f"📥 Response: {response}")
        else:
            print("❌ Stealth Gemini login failed")
    except Exception as e:
        print(f"❌ Gemini test error: {e}")
    finally:
        gemini_client.close()


if __name__ == "__main__":
    print("🕵️ Stealth Web Clients")
    print("=" * 30)
    
    choice = input("""
Choose option:
1. Test stealth Gemini client
2. Test stealth ChatGPT client  
3. Install undetected-chromedriver
4. Exit

Enter choice (1-4): """)
    
    if choice == "1":
        test_stealth_clients()
    elif choice == "2":
        print("ChatGPT stealth client - coming soon!")
    elif choice == "3":
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "undetected-chromedriver"])
        print("✅ undetected-chromedriver installed!")
    else:
        print("👋 Goodbye!")