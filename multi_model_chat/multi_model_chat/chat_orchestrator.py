#!/usr/bin/env python3
"""
Multi-Model Chat Orchestrator
Facilitates group conversations between different AI models
"""

import json
import os
import time
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass
from model_clients import ModelClientFactory


@dataclass
class ChatMessage:
    """Represents a message in the chat"""
    model: str
    content: str
    timestamp: datetime
    round_number: int
    
    def to_dict(self) -> Dict:
        return {
            "model": self.model,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "round": self.round_number
        }


class MultiModelChatOrchestrator:
    """Orchestrates conversations between multiple AI models"""
    
    def __init__(self, config_path: str = "chat_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.client_factory = ModelClientFactory(self.config)
        self.conversation_history: List[ChatMessage] = []
        self.current_topic = ""
        
        # Create conversations directory
        conv_dir = self.config.get('chat_settings', {}).get('conversation_dir', 'conversations')
        os.makedirs(conv_dir, exist_ok=True)
        
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Config file {self.config_path} not found!")
            return {}
            
    def get_enabled_models(self) -> List[str]:
        """Get list of enabled models"""
        enabled = []
        for model_name, model_config in self.config.get('models', {}).items():
            if model_config.get('enabled', False):
                enabled.append(model_name)
        return enabled
        
    def start_discussion(self, topic: str, participants: Optional[List[str]] = None):
        """Start a new discussion on the given topic"""
        self.current_topic = topic
        self.conversation_history.clear()
        
        if participants is None:
            participants = self.get_enabled_models()
            
        if not participants:
            print("❌ No enabled models found! Please check your configuration.")
            return
            
        print(f"\n🎯 Starting Group Discussion")
        print(f"📋 Topic: {topic}")
        print(f"👥 Participants: {', '.join(participants)}")
        print("=" * 60)
        
        max_rounds = self.config.get('chat_settings', {}).get('max_rounds', 3)
        
        for round_num in range(1, max_rounds + 1):
            print(f"\n🔄 Round {round_num}")
            print("-" * 40)
            
            for model_name in participants:
                try:
                    response = self._get_model_response(model_name, topic, round_num)
                    if response:
                        self._add_message(model_name, response, round_num)
                        self._display_message(model_name, response)
                        time.sleep(1)  # Brief pause between responses
                except Exception as e:
                    print(f"❌ Error getting response from {model_name}: {e}")
                    
        # Save conversation
        if self.config.get('chat_settings', {}).get('save_conversations', True):
            self._save_conversation()
            
    def _get_model_response(self, model_name: str, topic: str, round_num: int) -> str:
        """Get response from a specific model"""
        client = self.client_factory.get_client(model_name)
        if not client:
            return ""
            
        # Build context
        context = self._build_context(model_name, topic, round_num)
        
        return client.get_response(context)
        
    def _build_context(self, model_name: str, topic: str, round_num: int) -> str:
        """Build context for the model including conversation history"""
        model_config = self.config.get('models', {}).get(model_name, {})
        personality = model_config.get('personality', 'Helpful AI assistant')
        
        context = f"""You are participating in a group discussion as: {personality}

Topic: {topic}

This is round {round_num} of the discussion."""
        
        # Include previous messages if enabled
        if self.config.get('chat_settings', {}).get('include_previous_messages', True):
            if self.conversation_history:
                context += "\n\nPrevious discussion:\n"
                for msg in self.conversation_history[-6:]:  # Last 6 messages
                    context += f"{msg.model.upper()}: {msg.content}\n"
                    
        context += f"\nPlease provide your perspective on the topic. Keep your response focused and under 200 words."
        
        return context
        
    def _add_message(self, model_name: str, content: str, round_num: int):
        """Add a message to the conversation history"""
        message = ChatMessage(
            model=model_name,
            content=content,
            timestamp=datetime.now(),
            round_number=round_num
        )
        self.conversation_history.append(message)
        
    def _display_message(self, model_name: str, content: str):
        """Display a message in the terminal"""
        print(f"\n🤖 {model_name.upper()}:")
        print(f"💬 {content}")
        
    def _save_conversation(self):
        """Save the conversation to a JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversation_{timestamp}.json"
        conv_dir = self.config.get('chat_settings', {}).get('conversation_dir', 'conversations')
        filepath = os.path.join(conv_dir, filename)
        
        conversation_data = {
            "topic": self.current_topic,
            "timestamp": datetime.now().isoformat(),
            "messages": [msg.to_dict() for msg in self.conversation_history]
        }
        
        with open(filepath, 'w') as f:
            json.dump(conversation_data, f, indent=2)
            
        print(f"\n💾 Conversation saved to: {filepath}")
        
    def interactive_mode(self):
        """Start interactive mode for continuous discussions"""
        print("\n🎭 Multi-Model Chat - Interactive Mode")
        print("Type 'quit' to exit, 'models' to see available models")
        print("=" * 60)
        
        while True:
            topic = input("\n🎯 Enter discussion topic (or command): ").strip()
            
            if topic.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif topic.lower() == 'models':
                enabled = self.get_enabled_models()
                print(f"🤖 Enabled models: {', '.join(enabled) if enabled else 'None'}")
                continue
            elif not topic:
                continue
                
            self.start_discussion(topic)


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Multi-Model Chat Orchestrator")
    parser.add_argument("--topic", help="Discussion topic")
    parser.add_argument("--config", default="chat_config.json", help="Config file path")
    parser.add_argument("--interactive", action="store_true", help="Start interactive mode")
    
    args = parser.parse_args()
    
    orchestrator = MultiModelChatOrchestrator(args.config)
    
    if args.interactive:
        orchestrator.interactive_mode()
    elif args.topic:
        orchestrator.start_discussion(args.topic)
    else:
        print("Please provide a topic with --topic or use --interactive mode")


if __name__ == "__main__":
    main()