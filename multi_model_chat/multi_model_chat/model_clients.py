#!/usr/bin/env python3
"""
Model Clients for Multi-Model Chat
Handles communication with different AI model APIs
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, Optional


class ModelClient(ABC):
    """Abstract base class for model clients"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    @abstractmethod
    def get_response(self, prompt: str) -> str:
        """Get response from the model"""
        pass


class ClaudeClient(ModelClient):
    """Client for Anthropic Claude models"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        try:
            import anthropic
            api_key = config.get('api_key') or os.getenv('ANTHROPIC_API_KEY')
            if not api_key or api_key == 'your-anthropic-api-key':
                print(f"⚠️  No valid Anthropic API key found for <PERSON>")
                self.client = None
            else:
                self.client = anthropic.Anthropic(api_key=api_key)
        except ImportError:
            print("❌ anthropic package not installed. Run: pip install anthropic")
            self.client = None
            
    def get_response(self, prompt: str) -> str:
        """Get response from <PERSON>"""
        if not self.client:
            return "[<PERSON> unavailable - check API key and installation]"
            
        try:
            response = self.client.messages.create(
                model=self.config.get('model', 'claude-3-sonnet-20240229'),
                max_tokens=self.config.get('max_tokens', 4000),
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            # Extract text from TextBlock in response content
            for block in response.content:
                if block.type == 'text':
                    return block.text
            return "[Claude returned no text content]"
        except Exception as e:
            return f"[Claude error: {str(e)}]"


class OpenAIClient(ModelClient):
    """Client for OpenAI GPT models"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        try:
            import openai
            api_key = config.get('api_key') or os.getenv('OPENAI_API_KEY')
            if not api_key or api_key == 'your-openai-api-key':
                print(f"⚠️  No valid OpenAI API key found")
                self.client = None
            else:
                self.client = openai.OpenAI(api_key=api_key)
        except ImportError:
            print("❌ openai package not installed. Run: pip install openai")
            self.client = None
            
    def get_response(self, prompt: str) -> str:
        """Get response from GPT"""
        if not self.client:
            return "[GPT unavailable - check API key and installation]"
            
        try:
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'gpt-4'),
                max_tokens=self.config.get('max_tokens', 4000),
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            content = response.choices[0].message.content
            return content if content is not None else "[GPT returned no content]"
        except Exception as e:
            return f"[GPT error: {str(e)}]"


class GeminiClient(ModelClient):
    """Client for Google Gemini models"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        try:
            import google.generativeai as genai
            from google.generativeai.generative_models import GenerativeModel
            api_key = config.get('api_key') or os.getenv('GOOGLE_API_KEY')
            if not api_key or api_key == 'your-google-api-key':
                print(f"⚠️  No valid Google API key found")
                self.client = None
            else:
                genai.configure(api_key=api_key)
                self.client = GenerativeModel(self.config.get('model', 'gemini-pro'))
        except ImportError:
            print("❌ google-generativeai package not installed. Run: pip install google-generativeai")
            self.client = None
            
    def get_response(self, prompt: str) -> str:
        """Get response from Gemini"""
        if not self.client:
            return "[Gemini unavailable - check API key and installation]"
            
        try:
            response = self.client.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"[Gemini error: {str(e)}]"


class MockClient(ModelClient):
    """Mock client for testing without API keys"""
    
    def __init__(self, config: Dict, model_name: str):
        super().__init__(config)
        self.model_name = model_name
        
    def get_response(self, prompt: str) -> str:
        """Get mock response"""
        personality = self.config.get('personality', 'AI assistant')
        return f"[{self.model_name.upper()} MOCK]: As a {personality}, I would analyze this topic and provide insights. This is a mock response since no API key is configured."


class ModelClientFactory:
    """Factory for creating model clients"""
    
    def __init__(self, config: Dict):
        self.config = config
        self._clients = {}
        
    def get_client(self, model_name: str) -> Optional[ModelClient]:
        """Get or create a client for the specified model"""
        if model_name in self._clients:
            return self._clients[model_name]
            
        model_config = self.config.get('models', {}).get(model_name, {})
        if not model_config.get('enabled', False):
            return None
            
        # Create appropriate client based on model name
        if 'claude' in model_name.lower():
            client = ClaudeClient(model_config)
        elif 'gpt' in model_name.lower():
            client = OpenAIClient(model_config)
        elif 'gemini' in model_name.lower():
            client = GeminiClient(model_config)
        else:
            print(f"⚠️  Unknown model type: {model_name}")
            client = MockClient(model_config, model_name)
            
        self._clients[model_name] = client
        return client


# Test function
def test_clients():
    """Test function to verify client setup"""
    test_config = {
        "models": {
            "claude_sonnet": {
                "enabled": True,
                "model": "claude-3-5-sonnet-20241022",
                "personality": "Test assistant",
                "max_tokens": 100
            }
        }
    }
    
    factory = ModelClientFactory(test_config)
    client = factory.get_client("claude_sonnet")
    
    if client:
        response = client.get_response("Say hello!")
        print(f"Test response: {response}")
    else:
        print("No client available for testing")


if __name__ == "__main__":
    test_clients()