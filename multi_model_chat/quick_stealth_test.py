#!/usr/bin/env python3
"""Quick test for stealth browser"""

import undetected_chromedriver as uc
import time

def quick_test():
    print("🚀 Quick Stealth Browser Test")
    
    options = uc.ChromeOptions()
    options.add_argument('--no-first-run')
    
    driver = uc.Chrome(options=options, version_main=None)
    
    try:
        # Test Google services
        services = [
            ("Gemini", "https://gemini.google.com"),
            ("ChatGPT", "https://chat.openai.com"),
            ("<PERSON>", "https://claude.ai")
        ]
        
        for name, url in services:
            print(f"\n🌐 Testing {name}...")
            driver.get(url)
            time.sleep(3)
            
            page_text = driver.page_source.lower()
            
            if "this browser or app may not be secure" in page_text:
                print(f"   ❌ {name}: Security block detected")
            elif "cloudflare" in page_text:
                print(f"   🛡️ {name}: Cloudflare challenge")
            elif "sign in" in page_text or "login" in page_text:
                print(f"   ✅ {name}: Login page reached")
            else:
                print(f"   ✅ {name}: Accessible")
        
        print("\n✅ Test complete!")
        input("Press Enter to close...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    quick_test()
