#!/usr/bin/env python3
"""
Alternative Setup for Gemini (Bypass "Browser not secure" error)
This script provides workarounds for Google's browser detection
"""

import os
import subprocess

def setup_gemini_workaround():
    """Setup workaround for Gemini browser detection"""
    print("🔧 Gemini Browser Detection Workaround")
    print("=" * 50)
    print()
    
    print("Google services often block automated browsers. Here are alternatives:")
    print()
    
    print("Option 1: Use existing Chrome profile")
    print("- Open Chrome normally and login to Gemini")
    print("- Keep Chrome open")
    print("- Run the automation in a separate Chrome instance")
    print()
    
    print("Option 2: Use Chrome with specific flags")
    print("Run Chrome manually with these flags:")
    print("google-chrome --disable-web-security --user-data-dir=/tmp/chrome-test --disable-features=VizDisplayCompositor")
    print()
    
    print("Option 3: Use different URL")
    print("Try using: https://bard.google.com (if still available)")
    print("Or: https://gemini.google.com/app")
    print()
    
    print("Option 4: Focus on ChatGPT and Claude")
    print("These are more automation-friendly:")
    print("- ChatGPT: Generally works well with automation")
    print("- <PERSON>: Anthropic's interface is automation-friendly")
    print()
    
    choice = input("Would you like to update the config to disable Gemini for now? (y/n): ")
    if choice.lower() == 'y':
        update_config_disable_gemini()

def update_config_disable_gemini():
    """Update config to disable Gemini temporarily"""
    import json
    
    config_file = "web_chat_config.json"
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        if 'models' in config and 'gemini' in config['models']:
            config['models']['gemini']['enabled'] = False
            
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
            
        print(f"✅ Updated {config_file} - Gemini disabled")
        print("🎯 You can now test with ChatGPT and Claude:")
        print("python web_chat_orchestrator.py --topic 'Test topic' --models 'chatgpt,claude'")
        
    except Exception as e:
        print(f"❌ Error updating config: {e}")

def test_chrome_flags():
    """Test Chrome with anti-detection flags"""
    print("🧪 Testing Chrome with anti-detection flags...")
    
    chrome_cmd = [
        "google-chrome",
        "--disable-web-security",
        "--user-data-dir=/tmp/chrome-test",
        "--disable-features=VizDisplayCompositor",
        "--disable-blink-features=AutomationControlled",
        "https://gemini.google.com"
    ]
    
    try:
        subprocess.Popen(chrome_cmd)
        print("✅ Chrome launched with anti-detection flags")
        print("Try logging into Gemini in this window")
    except Exception as e:
        print(f"❌ Error launching Chrome: {e}")

if __name__ == "__main__":
    setup_gemini_workaround()
    
    test_flags = input("\nWould you like to test Chrome with anti-detection flags? (y/n): ")
    if test_flags.lower() == 'y':
        test_chrome_flags()