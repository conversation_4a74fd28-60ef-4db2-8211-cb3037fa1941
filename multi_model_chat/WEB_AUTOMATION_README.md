# Web-Based Multi-Model Chat System

🎭 **Selenium-powered group discussions between <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>**

## 🌟 Features

- 🤖 **Web Automation**: Controls actual web browsers (just like your LinkedIn automation!)
- 🔐 **Manual Login**: Uses your existing accounts and subscriptions  
- 💬 **Group Discussions**: Models respond to each other's messages
- 🎯 **Custom Topics**: Discuss anything you want
- 💾 **Conversation Logging**: Save all discussions as JSON
- 🎭 **Unique Personalities**: Each model has distinct discussion style

## 🚀 Quick Start

### 1. **Test Single Model**
```bash
cd multi_model_chat
python test_web_automation.py --interactive
```
Choose a model (ChatGPT/Claude/Gemini) and it will:
- Open browser window
- Wait for you to login
- Send test message
- Show response

### 2. **Run Group Discussion**
```bash
python web_chat_orchestrator.py --interactive
```
Enter a topic and select models for group discussion.

### 3. **Quick Topic Test**
```bash
python web_chat_orchestrator.py --topic "Future of AI development" --models "chatgpt,claude"
```

## 🔧 How It Works

### Just Like Your LinkedIn Automation:
1. **Opens browser windows** for each AI service
2. **Waits for manual login** (uses your existing accounts)
3. **Automates message sending** using Selenium
4. **Captures responses** from the web interface
5. **Orchestrates conversations** between models

### Example Flow:
```
🎯 Topic: "Best coding practices"
🌐 Opens: ChatGPT + Claude + Gemini browsers
🔐 You login to each (one-time per session)
💬 Sends contextual prompts to each
📥 Captures responses automatically
🔄 Each model sees others' responses in next round
🔁 Repeats for 3 rounds
💾 Saves complete conversation
```

## 📁 File Structure

```
multi_model_chat/
├── web_automation_clients.py    # Selenium automation for each AI service
├── web_chat_orchestrator.py     # Main orchestration logic
├── web_chat_config.json         # Configuration
├── test_web_automation.py       # Test individual models
├── quick_demo.py                # System overview
└── conversations/               # Saved discussions
```

## ⚙️ Configuration

Edit `web_chat_config.json`:

```json
{
  "models": {
    "chatgpt": {
      "enabled": true,
      "url": "https://chat.openai.com",
      "personality": "Creative strategist",
      "headless": false
    },
    "claude": {
      "enabled": true, 
      "url": "https://claude.ai",
      "personality": "Thoughtful analyst"
    },
    "gemini": {
      "enabled": true,
      "url": "https://gemini.google.com",
      "personality": "Detail-oriented researcher"
    }
  }
}
```

## 🎮 Usage Examples

### Interactive Mode
```bash
python web_chat_orchestrator.py --interactive

# Then enter:
🎯 Topic: "Best strategies for remote team management"
Models: chatgpt,claude
```

### Direct Topic
```bash
python web_chat_orchestrator.py \
  --topic "Ethics in AI development" \
  --models "claude,gemini"
```

### Test Single Model
```bash
python test_web_automation.py --model chatgpt
```

## 🤖 Model Personalities

- **ChatGPT**: Creative strategist who thinks outside the box
- **Claude**: Thoughtful analyst who considers multiple perspectives  
- **Gemini**: Detail-oriented researcher with comprehensive analysis

## 💡 Benefits vs API Approach

### ✅ Web Automation Advantages:
- **No API costs** - uses your existing subscriptions
- **Access to latest models** (GPT-4, Claude 3.5 Sonnet, Gemini Pro)
- **Same interface you use** - familiar and reliable
- **No rate limits** beyond normal web usage
- **Handles login/auth automatically**

### ⚠️ Considerations:
- Requires browser windows (can run headless)
- Slower than direct API calls
- Dependent on web interface stability
- Manual login required initially

## 🔧 Technical Details

### Selenium Automation:
- **ChromeDriver** with WebDriverManager
- **Human-like typing** with random delays
- **Smart element detection** with fallback selectors
- **Response capturing** from web interfaces
- **Error handling** and retry logic

### Similar to LinkedIn Automation:
- Uses same Selenium patterns as your job application bot
- Handles dynamic web content
- Manages multiple browser instances
- Robust error handling and recovery

## 🚀 Get Started

1. **Run the demo** to see overview:
   ```bash
   python quick_demo.py
   ```

2. **Test one model** first:
   ```bash
   python test_web_automation.py --interactive
   ```

3. **Start group discussion**:
   ```bash
   python web_chat_orchestrator.py --interactive
   ```

**Ready to facilitate AI group discussions using web automation!** 🎭