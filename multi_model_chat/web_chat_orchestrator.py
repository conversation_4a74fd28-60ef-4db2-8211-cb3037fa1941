#!/usr/bin/env python3
"""
Web-Based Multi-Model Chat Orchestrator
Uses Selenium to automate ChatGP<PERSON>, <PERSON>, and Gemini web interfaces
"""

import json
import os
import time
import random
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass
from web_automation_clients import WebModelClientFactory, WebModelClient


@dataclass
class WebChatMessage:
    """Represents a message in the web-based chat"""
    model: str
    content: str
    timestamp: datetime
    round_number: int
    
    def to_dict(self) -> Dict:
        return {
            "model": self.model,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "round": self.round_number
        }


class WebMultiModelChatOrchestrator:
    """Orchestrates web-based conversations between multiple AI models"""
    
    def __init__(self, config_path: str = "web_chat_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.client_factory = WebModelClientFactory(self.config)
        self.conversation_history: List[WebChatMessage] = []
        self.current_topic = ""
        self.active_clients: Dict[str, WebModelClient] = {}
        
        # Create conversations directory
        conv_dir = self.config.get('chat_settings', {}).get('conversation_dir', 'conversations')
        os.makedirs(conv_dir, exist_ok=True)
        
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Config file {self.config_path} not found!")
            return {}
            
    def get_enabled_models(self) -> List[str]:
        """Get list of enabled models"""
        enabled = []
        for model_name, model_config in self.config.get('models', {}).items():
            if model_config.get('enabled', False):
                enabled.append(model_name)
        return enabled
        
    def setup_models(self, participants: List[str], attach_mode: bool = False, browser_type: str = "chrome") -> bool:
        """Setup and login to all model web interfaces"""
        mode_desc = "attachment mode" if attach_mode else f"{browser_type} browser mode"
        print(f"\n🔧 Setting up web automation for {len(participants)} models ({mode_desc})...")
        
        headless = self.config.get('browser_settings', {}).get('headless', False)
        
        for model_name in participants:
            print(f"\n🤖 Setting up {model_name.upper()}...")
            
            try:
                client = self.client_factory.get_client(
                    model_name, 
                    headless=headless, 
                    attach_mode=attach_mode, 
                    browser_type=browser_type
                )
                if not client:
                    print(f"❌ Could not create client for {model_name}")
                    continue
                    
                if attach_mode:
                    print(f"🔗 Attaching to existing {model_name} session...")
                else:
                    print(f"🌐 Opening {model_name} web interface...")
                    
                if client.login():
                    self.active_clients[model_name] = client
                    print(f"✅ {model_name} ready!")
                else:
                    print(f"❌ {model_name} login failed")
                    client.close()
                    
            except Exception as e:
                print(f"❌ Error setting up {model_name}: {e}")
                
        successful_logins = len(self.active_clients)
        print(f"\n📊 Setup complete: {successful_logins}/{len(participants)} models ready")
        
        return successful_logins > 0
        
    def start_discussion(self, topic: str, participants: Optional[List[str]] = None, attach_mode: bool = False, browser_type: str = "chrome"):
        """Start a new web-based discussion on the given topic"""
        self.current_topic = topic
        self.conversation_history.clear()
        
        if participants is None:
            participants = self.get_enabled_models()
            
        if not participants:
            print("❌ No enabled models found! Please check your configuration.")
            return
            
        print(f"\n🎯 Starting Web-Based Group Discussion")
        print(f"📋 Topic: {topic}")
        print(f"👥 Participants: {', '.join(participants)}")
        print("=" * 80)
        
        # Setup all models
        if not self.setup_models(participants, attach_mode, browser_type):
            print("❌ Could not setup any models. Exiting.")
            return
            
        # Run the discussion
        try:
            self._run_discussion_rounds(topic)
        except KeyboardInterrupt:
            print("\n⚠️  Discussion interrupted by user")
        except Exception as e:
            print(f"\n❌ Discussion error: {e}")
        finally:
            self._cleanup()
            
    def _run_discussion_rounds(self, topic: str):
        """Run the actual discussion rounds"""
        max_rounds = self.config.get('chat_settings', {}).get('max_rounds', 3)
        
        for round_num in range(1, max_rounds + 1):
            print(f"\n🔄 Round {round_num}")
            print("-" * 60)
            
            for model_name in self.active_clients.keys():
                try:
                    print(f"\n🤖 Getting response from {model_name.upper()}...")
                    response = self._get_model_response(model_name, topic, round_num)
                    
                    if response and not response.startswith('['):  # Skip error messages
                        self._add_message(model_name, response, round_num)
                        self._display_message(model_name, response)
                        
                        # Random delay between models
                        delay_range = self.config.get('chat_settings', {}).get('delays', {}).get('between_models', [2, 5])
                        time.sleep(random.uniform(*delay_range))
                    else:
                        print(f"⚠️  {model_name} response: {response}")
                        
                except Exception as e:
                    print(f"❌ Error getting response from {model_name}: {e}")
                    
        # Save conversation
        if self.config.get('chat_settings', {}).get('save_conversations', True):
            self._save_conversation()
            
    def _get_model_response(self, model_name: str, topic: str, round_num: int) -> str:
        """Get response from a specific web model"""
        client = self.active_clients.get(model_name)
        if not client:
            return f"[{model_name} not available]"
            
        # Build context message
        context = self._build_context_message(model_name, topic, round_num)
        
        # Send message and get response
        return client.send_message(context)
        
    def _build_context_message(self, model_name: str, topic: str, round_num: int) -> str:
        """Build context message for the model"""
        model_config = self.config.get('models', {}).get(model_name, {})
        personality = model_config.get('personality', 'Helpful AI assistant')
        
        # Base prompt
        context = f"""You are participating in a group discussion with other AI models.
Your role: {personality}
Topic: {topic}
This is round {round_num} of the discussion."""
        
        # Include previous messages if enabled
        if self.config.get('chat_settings', {}).get('include_previous_messages', True) and self.conversation_history:
            context += "\n\nPrevious discussion:"
            
            # Show last few messages from other models
            recent_messages = [msg for msg in self.conversation_history[-6:] if msg.model != model_name]
            for msg in recent_messages:
                context += f"\n{msg.model.upper()}: {msg.content[:200]}{'...' if len(msg.content) > 200 else ''}"
                
        context += f"\n\nPlease provide your perspective on the topic. Keep your response focused and under 300 words."
        
        return context
        
    def _add_message(self, model_name: str, content: str, round_num: int):
        """Add a message to the conversation history"""
        message = WebChatMessage(
            model=model_name,
            content=content,
            timestamp=datetime.now(),
            round_number=round_num
        )
        self.conversation_history.append(message)
        
    def _display_message(self, model_name: str, content: str):
        """Display a message in the terminal"""
        print(f"\n🤖 {model_name.upper()}:")
        print(f"💬 {content}")
        
    def _save_conversation(self):
        """Save the conversation to a JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"web_conversation_{timestamp}.json"
        conv_dir = self.config.get('chat_settings', {}).get('conversation_dir', 'conversations')
        filepath = os.path.join(conv_dir, filename)
        
        conversation_data = {
            "topic": self.current_topic,
            "timestamp": datetime.now().isoformat(),
            "type": "web_automation",
            "participants": list(self.active_clients.keys()),
            "messages": [msg.to_dict() for msg in self.conversation_history]
        }
        
        with open(filepath, 'w') as f:
            json.dump(conversation_data, f, indent=2)
            
        print(f"\n💾 Conversation saved to: {filepath}")
        
    def _cleanup(self):
        """Cleanup: close all browser instances"""
        print("\n🧹 Cleaning up browser instances...")
        
        for model_name, client in self.active_clients.items():
            try:
                print(f"Closing {model_name}...")
                client.close()
            except Exception as e:
                print(f"Error closing {model_name}: {e}")
                
        self.active_clients.clear()
        self.client_factory.close_all()
        print("✅ Cleanup complete!")
        
    def interactive_mode(self):
        """Start interactive mode for continuous discussions"""
        print("\n🎭 Web Multi-Model Chat - Interactive Mode")
        print("Type 'quit' to exit, 'models' to see available models")
        print("⚠️  Warning: This will open multiple browser windows!")
        print("=" * 80)
        
        try:
            while True:
                topic = input("\n🎯 Enter discussion topic (or command): ").strip()
                
                if topic.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                elif topic.lower() == 'models':
                    enabled = self.get_enabled_models()
                    print(f"🤖 Enabled models: {', '.join(enabled) if enabled else 'None'}")
                    continue
                elif not topic:
                    continue
                    
                # Ask which models to include
                enabled_models = self.get_enabled_models()
                print(f"\nAvailable models: {', '.join(enabled_models)}")
                selection = input("Select models (comma-separated, or Enter for all): ").strip()
                
                if selection:
                    try:
                        selected = [m.strip() for m in selection.split(',') if m.strip() in enabled_models]
                        participants = selected if selected else enabled_models
                    except:
                        participants = enabled_models
                else:
                    participants = enabled_models
                    
                self.start_discussion(topic, participants)
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        finally:
            self._cleanup()


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Web-Based Multi-Model Chat Orchestrator")
    parser.add_argument("--topic", help="Discussion topic")
    parser.add_argument("--config", default="web_chat_config.json", help="Config file path")
    parser.add_argument("--interactive", action="store_true", help="Start interactive mode")
    parser.add_argument("--models", help="Comma-separated list of models to use")
    parser.add_argument("--attach", action="store_true", help="Attach to existing Chrome debug session")
    parser.add_argument("--firefox", action="store_true", help="Use Firefox instead of Chrome")
    parser.add_argument("--browser", choices=["chrome", "firefox"], default="chrome", help="Browser type")
    
    args = parser.parse_args()
    
    # Determine browser settings
    attach_mode = args.attach
    if args.firefox:
        browser_type = "firefox"
    else:
        browser_type = args.browser
    
    orchestrator = WebMultiModelChatOrchestrator(args.config)
    
    if attach_mode:
        print("🔗 Attachment mode: Make sure Chrome is running with debug port 9222")
        print("   Run: python browser_attachment.py --start-debug")
    
    if args.interactive:
        orchestrator.interactive_mode()
    elif args.topic:
        participants = None
        if args.models:
            participants = [m.strip() for m in args.models.split(',')]
        
        # Pass browser settings to start_discussion
        orchestrator.start_discussion(args.topic, participants, attach_mode, browser_type)
    else:
        print("Please provide a topic with --topic or use --interactive mode")
        print("\nExample usage:")
        print("  python web_chat_orchestrator.py --interactive")
        print("  python web_chat_orchestrator.py --topic 'Future of AI' --models 'chatgpt,claude'")
        print("  python web_chat_orchestrator.py --topic 'Test' --models 'claude' --attach")
        print("  python web_chat_orchestrator.py --topic 'Test' --models 'claude' --firefox")


if __name__ == "__main__":
    main()