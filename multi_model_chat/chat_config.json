{"models": {"claude_opus": {"enabled": true, "api_key": "your-anthropic-api-key", "model": "claude-3-opus-20240229", "personality": "Thoughtful analyst who considers multiple perspectives", "max_tokens": 4000}, "claude_sonnet": {"enabled": true, "api_key": "your-anthropic-api-key", "model": "claude-3-5-sonnet-20241022", "personality": "Practical problem-solver focused on actionable solutions", "max_tokens": 4000}, "gpt4": {"enabled": false, "api_key": "your-openai-api-key", "model": "gpt-4", "personality": "Creative strategist who thinks outside the box", "max_tokens": 4000}, "gemini": {"enabled": false, "api_key": "your-google-api-key", "model": "gemini-pro", "personality": "Detail-oriented researcher who provides comprehensive analysis", "max_tokens": 4000}}, "chat_settings": {"max_rounds": 3, "include_previous_messages": true, "moderator_prompts": true, "save_conversations": true, "conversation_dir": "conversations"}}