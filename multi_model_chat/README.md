# Multi-Model Chat System

A system for facilitating group conversations between different AI models (<PERSON>, <PERSON>, GPT-4, <PERSON>).

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API keys** in `chat_config.json`:
   ```json
   {
     "models": {
       "claude_opus": {
         "enabled": true,
         "api_key": "your-anthropic-api-key"
       }
     }
   }
   ```

3. **Run interactive mode:**
   ```bash
   python chat_orchestrator.py --interactive
   ```

4. **Or run with a specific topic:**
   ```bash
   python chat_orchestrator.py --topic "What's the future of AI?"
   ```

## Features

- 🤖 **Multiple AI Models**: Claude Opus/Sonnet, GPT-4, Gemini
- 💬 **Group Discussions**: Models respond to each other
- 🎭 **Unique Personalities**: Each model has a distinct discussion style
- 📚 **Conversation History**: Models see previous responses
- 💾 **Save Conversations**: All chats saved as JSON
- 🔧 **Configurable**: Customize models, personalities, rounds

## Configuration

Edit `chat_config.json` to:
- Enable/disable models
- Set API keys
- Customize personalities
- Adjust discussion settings

## API Keys

Set your API keys either in config file or environment variables:
- `ANTHROPIC_API_KEY` for Claude models
- `OPENAI_API_KEY` for GPT models  
- `GOOGLE_API_KEY` for Gemini

## Example Usage

```bash
# Interactive mode
python chat_orchestrator.py --interactive

# Single discussion
python chat_orchestrator.py --topic "Best programming practices"

# Custom config
python chat_orchestrator.py --config my_config.json --topic "AI Ethics"
```

## Model Personalities

- **Claude Opus**: Thoughtful analyst who considers multiple perspectives
- **Claude Sonnet**: Practical problem-solver focused on actionable solutions
- **GPT-4**: Creative strategist who thinks outside the box
- **Gemini**: Detail-oriented researcher with comprehensive analysis

## File Structure

```
multi_model_chat/
├── chat_orchestrator.py    # Main orchestration logic
├── model_clients.py        # API client implementations
├── chat_config.json        # Configuration file
├── conversations/          # Saved conversation logs
└── requirements.txt        # Python dependencies
```