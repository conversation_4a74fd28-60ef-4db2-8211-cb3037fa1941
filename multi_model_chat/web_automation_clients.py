#!/usr/bin/env python3
"""
Web Automation Clients for Multi-Model Chat
Uses Selenium to interact with ChatGP<PERSON>, <PERSON>, and Gemini web interfaces
"""

import time
import random
from abc import ABC, abstractmethod
from typing import Dict, Optional, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager


class WebModelClient(ABC):
    """Abstract base class for web-based model clients"""
    
    def __init__(self, config: Dict, headless: bool = False, attach_mode: bool = False, browser_type: str = "chrome"):
        self.config = config
        self.headless = headless
        self.attach_mode = attach_mode
        self.browser_type = browser_type
        self.driver = None
        self.wait = None
        self.logged_in = False
        
    def _setup_driver(self):
        """Setup driver with options (Chrome or Firefox, new or attach)"""
        if self.driver:
            return
            
        if self.attach_mode:
            self._setup_attach_driver()
        elif self.browser_type == "firefox":
            self._setup_firefox_driver()
        else:
            self._setup_chrome_driver()
            
    def _setup_attach_driver(self):
        """Attach to existing Chrome debug session"""
        try:
            from selenium.webdriver.chrome.options import Options
            opts = Options()
            opts.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=opts)
            self.wait = WebDriverWait(self.driver, 30)
            
            print("✅ Attached to existing Chrome session")
            
        except Exception as e:
            print(f"❌ Attachment failed: {e}")
            print("💡 Run: python browser_attachment.py --start-debug")
            raise
            
    def _setup_firefox_driver(self):
        """Setup Firefox driver"""
        try:
            from selenium.webdriver.firefox.options import Options as FirefoxOptions
            from selenium.webdriver.firefox.service import Service as FirefoxService
            from webdriver_manager.firefox import GeckoDriverManager
            
            opts = FirefoxOptions()
            if self.headless:
                opts.add_argument('--headless')
                
            # Firefox anti-detection
            opts.set_preference("dom.webdriver.enabled", False)
            opts.set_preference("useAutomationExtension", False)
            opts.set_preference("general.useragent.override", 
                              "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0")
            
            service = FirefoxService(GeckoDriverManager().install())
            self.driver = webdriver.Firefox(service=service, options=opts)
            self.wait = WebDriverWait(self.driver, 30)
            
            print("🦊 Firefox driver created")
            
        except Exception as e:
            print(f"❌ Firefox setup failed: {e}")
            raise
            
    def _setup_chrome_driver(self):
        """Setup new Chrome driver"""
        from selenium.webdriver.chrome.options import Options
        
        opts = Options()
        if self.headless:
            opts.add_argument('--headless=new')
        
        # Anti-detection measures
        opts.add_argument('--disable-gpu')
        opts.add_argument('--no-sandbox')
        opts.add_argument('--disable-dev-shm-usage')
        opts.add_argument('--disable-blink-features=AutomationControlled')
        opts.add_argument('--disable-extensions')
        opts.add_argument('--disable-plugins')
        opts.add_argument('--window-size=1920,1080')
        opts.add_argument('--start-maximized')
        opts.add_argument('--disable-web-security')
        opts.add_argument('--allow-running-insecure-content')
        opts.add_argument('--disable-features=VizDisplayCompositor')
        
        # Reduce console warnings
        opts.add_argument('--autoplay-policy=no-user-gesture-required')
        opts.add_argument('--disable-background-timer-throttling')
        opts.add_argument('--disable-backgrounding-occluded-windows')
        opts.add_argument('--disable-renderer-backgrounding')
        opts.add_argument('--disable-logging')
        opts.add_argument('--log-level=3')
        
        # Remove automation indicators
        opts.add_experimental_option("excludeSwitches", ["enable-automation"])
        opts.add_experimental_option('useAutomationExtension', False)
        
        # Realistic user agent
        opts.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Additional preferences
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2
        }
        opts.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=opts)
        
        # Execute script to hide webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        self.wait = WebDriverWait(self.driver, 30)
        
    def _human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
            
    def _random_delay(self, min_sec: float = 1.0, max_sec: float = 3.0):
        """Add random delay to mimic human behavior"""
        time.sleep(random.uniform(min_sec, max_sec))
        
    @abstractmethod
    def login(self) -> bool:
        """Login to the service"""
        pass
        
    @abstractmethod
    def send_message(self, message: str) -> str:
        """Send message and get response"""
        pass
        
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None


class ChatGPTWebClient(WebModelClient):
    """Selenium client for ChatGPT web interface"""
    
    def __init__(self, config: Dict, headless: bool = False, attach_mode: bool = False, browser_type: str = "chrome"):
        super().__init__(config, headless, attach_mode, browser_type)
        self.base_url = "https://chat.openai.com"
        
    def login(self) -> bool:
        """Login to ChatGPT"""
        try:
            self._setup_driver()
            self.driver.get(self.base_url)
            self._random_delay(3, 6)
            
            # Check for Cloudflare challenge first
            if self._handle_cloudflare_challenge():
                self._random_delay(2, 4)
            
            # Check if already logged in
            if self._is_logged_in():
                self.logged_in = True
                return True
                
            # Look for login elements
            print("🔍 Looking for ChatGPT login options...")
            
            # Try to find and click login button
            login_selectors = [
                "button[data-testid='login-button']",
                "a[href*='login']", 
                "button:contains('Log in')",
                ".btn-primary",
                "a[href='/auth/login']"
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed():
                        break
                except:
                    continue
                    
            if login_button:
                print("🔐 Found login button, clicking...")
                login_button.click()
                self._random_delay(3, 5)
            
            # Wait for manual completion
            print("⚠️  ChatGPT: Please complete any verification and login steps.")
            print("   - Handle Cloudflare challenge if present")
            print("   - Login with your credentials")
            print("   - Navigate to the main chat interface")
            print("   Press Enter when you see the message input box...")
            input()
            
            self.logged_in = self._is_logged_in()
            return self.logged_in
            
        except Exception as e:
            print(f"❌ ChatGPT login error: {e}")
            return False
    
    def _handle_cloudflare_challenge(self) -> bool:
        """Handle Cloudflare challenge if present"""
        try:
            # Check for Cloudflare challenge indicators
            cloudflare_indicators = [
                "div[class*='cf-browser-verification']",
                "div[class*='cf-challenge-form']", 
                "#cf-challenge-running",
                ".cf-wrapper",
                "input[name='cf_challenge_response']"
            ]
            
            for selector in cloudflare_indicators:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        print("🛡️  Cloudflare challenge detected!")
                        print("   Please complete the verification in the browser")
                        print("   This may include clicking 'Verify you are human'")
                        return True
                except:
                    continue
                    
            # Check for "Verify you are human" text
            if "verify you are human" in self.driver.page_source.lower():
                print("🛡️  Human verification challenge detected!")
                return True
                
            return False
            
        except Exception as e:
            print(f"⚠️  Error checking for Cloudflare: {e}")
            return False
            
    def _is_logged_in(self) -> bool:
        """Check if logged in to ChatGPT"""
        try:
            # Look for chat interface elements
            chat_indicators = [
                "textarea[placeholder*='message']",
                "[data-testid='send-button']",
                ".composer-text-input",
                "#prompt-textarea"
            ]
            
            for selector in chat_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            return False
        except:
            return False
            
    def send_message(self, message: str) -> str:
        """Send message to ChatGPT and get response"""
        try:
            if not self.logged_in:
                if not self.login():
                    return "[ChatGPT login required]"
                    
            # Find message input
            input_selectors = [
                "textarea[placeholder*='message']",
                "#prompt-textarea",
                ".composer-text-input textarea",
                "[data-testid='prompt-textarea']"
            ]
            
            message_input = None
            for selector in input_selectors:
                try:
                    message_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
                    
            if not message_input:
                return "[ChatGPT input not found]"
                
            # Type message
            self._human_like_typing(message_input, message)
            self._random_delay(0.5, 1.5)
            
            # Send message
            send_selectors = [
                "[data-testid='send-button']",
                "button[aria-label='Send message']",
                ".composer-send-button"
            ]
            
            for selector in send_selectors:
                try:
                    send_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if send_button.is_enabled():
                        send_button.click()
                        break
                except:
                    continue
            else:
                # Fallback: press Enter
                message_input.send_keys(Keys.RETURN)
                
            # Wait for response
            return self._get_response()
            
        except Exception as e:
            return f"[ChatGPT error: {e}]"
            
    def _get_response(self) -> str:
        """Get the latest response from ChatGPT"""
        try:
            # Wait for response to appear
            self._random_delay(2, 4)
            
            # Look for response elements
            response_selectors = [
                ".markdown.prose",
                "[data-message-author-role='assistant']",
                ".conversation-turn",
                ".message-content"
            ]
            
            max_wait = 60  # Maximum wait time
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                for selector in response_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            # Get the last response
                            last_response = elements[-1].text.strip()
                            if last_response and len(last_response) > 10:
                                return last_response
                    except:
                        continue
                        
                time.sleep(2)
                
            return "[ChatGPT: Response timeout]"
            
        except Exception as e:
            return f"[ChatGPT response error: {e}]"


class ClaudeWebClient(WebModelClient):
    """Selenium client for Claude web interface"""
    
    def __init__(self, config: Dict, headless: bool = False, attach_mode: bool = False, browser_type: str = "chrome"):
        super().__init__(config, headless, attach_mode, browser_type)
        self.base_url = "https://claude.ai"
        
    def login(self) -> bool:
        """Login to Claude"""
        try:
            self._setup_driver()
            self.driver.get(self.base_url)
            self._random_delay(3, 6)
            
            # Check for Cloudflare challenge first
            if self._handle_cloudflare_challenge():
                self._random_delay(2, 4)
            
            # Check if already logged in
            if self._is_logged_in():
                self.logged_in = True
                return True
                
            print("🔍 Looking for Claude login options...")
            
            # Look for login elements
            login_selectors = [
                "a[href*='login']",
                "button:contains('Login')",
                ".login-button",
                "[data-testid='login']"
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed():
                        break
                except:
                    continue
                    
            if login_button:
                print("🔐 Found login button, clicking...")
                login_button.click()
                self._random_delay(3, 5)
            
            # Wait for manual completion
            print("⚠️  Claude: Please complete any verification and login steps.")
            print("   - Handle Cloudflare challenge if present")
            print("   - Login with your credentials")
            print("   - Navigate to the main chat interface")
            print("   Press Enter when you see the message input area...")
            input()
            
            self.logged_in = self._is_logged_in()
            return self.logged_in
            
        except Exception as e:
            print(f"❌ Claude login error: {e}")
            return False
    
    def _handle_cloudflare_challenge(self) -> bool:
        """Handle Cloudflare challenge if present"""
        try:
            # Check for Cloudflare challenge indicators
            cloudflare_indicators = [
                "div[class*='cf-browser-verification']",
                "div[class*='cf-challenge-form']", 
                "#cf-challenge-running",
                ".cf-wrapper",
                "input[name='cf_challenge_response']"
            ]
            
            for selector in cloudflare_indicators:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        print("🛡️  Cloudflare challenge detected!")
                        print("   Please complete the verification in the browser")
                        print("   This may include clicking 'Verify you are human'")
                        return True
                except:
                    continue
                    
            # Check for "Verify you are human" text
            if "verify you are human" in self.driver.page_source.lower():
                print("🛡️  Human verification challenge detected!")
                return True
                
            # Check for Claude-specific challenge text
            if "claude.ai needs to review" in self.driver.page_source.lower():
                print("🛡️  Claude security check detected!")
                return True
                
            return False
            
        except Exception as e:
            print(f"⚠️  Error checking for Cloudflare: {e}")
            return False
            
    def _is_logged_in(self) -> bool:
        """Check if logged in to Claude"""
        try:
            # Look for chat interface
            chat_indicators = [
                "[data-testid='chat-input']",
                ".ProseMirror",
                "div[contenteditable='true']"
            ]
            
            for selector in chat_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            return False
        except:
            return False
            
    def send_message(self, message: str) -> str:
        """Send message to Claude and get response"""
        try:
            if not self.logged_in:
                if not self.login():
                    return "[Claude login required]"
                    
            # Find message input
            input_selectors = [
                "[data-testid='chat-input']",
                ".ProseMirror",
                "div[contenteditable='true']",
                "textarea"
            ]
            
            message_input = None
            for selector in input_selectors:
                try:
                    message_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
                    
            if not message_input:
                return "[Claude input not found]"
                
            # Type message
            message_input.click()
            self._human_like_typing(message_input, message)
            self._random_delay(0.5, 1.5)
            
            # Send message (usually Ctrl+Enter or Enter)
            message_input.send_keys(Keys.CONTROL + Keys.RETURN)
            
            # Wait for response
            return self._get_response()
            
        except Exception as e:
            return f"[Claude error: {e}]"
            
    def _get_response(self) -> str:
        """Get the latest response from Claude"""
        try:
            # Wait for response
            self._random_delay(2, 4)
            
            response_selectors = [
                "[data-testid='conversation-turn']",
                ".font-claude-message",
                ".prose"
            ]
            
            max_wait = 60
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                for selector in response_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            last_response = elements[-1].text.strip()
                            if last_response and len(last_response) > 10:
                                return last_response
                    except:
                        continue
                        
                time.sleep(2)
                
            return "[Claude: Response timeout]"
            
        except Exception as e:
            return f"[Claude response error: {e}]"


class GeminiWebClient(WebModelClient):
    """Selenium client for Google Gemini web interface"""
    
    def __init__(self, config: Dict, headless: bool = False, attach_mode: bool = False, browser_type: str = "chrome"):
        super().__init__(config, headless, attach_mode, browser_type)
        self.base_url = "https://gemini.google.com"
        
    def login(self) -> bool:
        """Login to Gemini"""
        try:
            self._setup_driver()
            self.driver.get(self.base_url)
            self._random_delay(2, 4)
            
            # Check if already logged in
            if self._is_logged_in():
                self.logged_in = True
                return True
                
            print("⚠️  Gemini: Please manually log in. Press Enter when ready...")
            input()
            
            self.logged_in = self._is_logged_in()
            return self.logged_in
            
        except Exception as e:
            print(f"❌ Gemini login error: {e}")
            return False
            
    def _is_logged_in(self) -> bool:
        """Check if logged in to Gemini"""
        try:
            chat_indicators = [
                "rich-textarea",
                "[data-test-id='input-area']",
                ".ql-editor"
            ]
            
            for selector in chat_indicators:
                try:
                    self.driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except:
                    continue
            return False
        except:
            return False
            
    def send_message(self, message: str) -> str:
        """Send message to Gemini and get response"""
        try:
            if not self.logged_in:
                if not self.login():
                    return "[Gemini login required]"
                    
            # Find message input
            input_selectors = [
                "rich-textarea",
                "[data-test-id='input-area']",
                ".ql-editor",
                "textarea"
            ]
            
            message_input = None
            for selector in input_selectors:
                try:
                    message_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
                    
            if not message_input:
                return "[Gemini input not found]"
                
            # Type message
            message_input.click()
            self._human_like_typing(message_input, message)
            self._random_delay(0.5, 1.5)
            
            # Send message
            send_selectors = [
                "[aria-label='Send message']",
                "button[type='submit']",
                ".send-button"
            ]
            
            for selector in send_selectors:
                try:
                    send_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if send_button.is_enabled():
                        send_button.click()
                        break
                except:
                    continue
            else:
                # Fallback: press Enter
                message_input.send_keys(Keys.RETURN)
                
            return self._get_response()
            
        except Exception as e:
            return f"[Gemini error: {e}]"
            
    def _get_response(self) -> str:
        """Get the latest response from Gemini"""
        try:
            self._random_delay(2, 4)
            
            response_selectors = [
                "[data-test-id='model-response']",
                ".model-response-text",
                ".response-container"
            ]
            
            max_wait = 60
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                for selector in response_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            last_response = elements[-1].text.strip()
                            if last_response and len(last_response) > 10:
                                return last_response
                    except:
                        continue
                        
                time.sleep(2)
                
            return "[Gemini: Response timeout]"
            
        except Exception as e:
            return f"[Gemini response error: {e}]"


class WebModelClientFactory:
    """Factory for creating web-based model clients"""
    
    def __init__(self, config: Dict):
        self.config = config
        self._clients = {}
        
    def get_client(self, model_name: str, headless: bool = False, attach_mode: bool = False, browser_type: str = "chrome") -> Optional[WebModelClient]:
        """Get or create a web client for the specified model"""
        client_key = f"{model_name}_{attach_mode}_{browser_type}"
        if client_key in self._clients:
            return self._clients[client_key]
            
        model_config = self.config.get('models', {}).get(model_name, {})
        if not model_config.get('enabled', False):
            return None
            
        # Create appropriate client based on model name
        if 'chatgpt' in model_name.lower() or 'gpt' in model_name.lower():
            client = ChatGPTWebClient(model_config, headless, attach_mode, browser_type)
        elif 'claude' in model_name.lower():
            client = ClaudeWebClient(model_config, headless, attach_mode, browser_type)
        elif 'gemini' in model_name.lower():
            client = GeminiWebClient(model_config, headless, attach_mode, browser_type)
        else:
            print(f"⚠️  Unknown web model type: {model_name}")
            return None
            
        self._clients[client_key] = client
        return client
        
    def close_all(self):
        """Close all web clients"""
        for client in self._clients.values():
            try:
                client.close()
            except:
                pass
        self._clients.clear()


# Test function
def test_web_clients():
    """Test function for web clients"""
    test_config = {
        "models": {
            "chatgpt": {
                "enabled": True,
                "personality": "Creative assistant"
            },
            "claude": {
                "enabled": True, 
                "personality": "Thoughtful analyst"
            }
        }
    }
    
    factory = WebModelClientFactory(test_config)
    
    # Test ChatGPT
    chatgpt = factory.get_client("chatgpt", headless=False)
    if chatgpt:
        print("Testing ChatGPT client...")
        if chatgpt.login():
            response = chatgpt.send_message("Hello! Can you say hi back?")
            print(f"ChatGPT response: {response}")
        chatgpt.close()


if __name__ == "__main__":
    test_web_clients()