#!/usr/bin/env python3
"""
Unit tests for job application utilities - Fixed version
"""
import unittest
from unittest.mock import Mock, <PERSON>Mock, patch, PropertyMock
from selenium.common.exceptions import ElementClickInterceptedException
from selenium.webdriver.common.by import By

from job_application_utils import (
    ElementInteractor,
    FormAnalyzer,
    FieldMatcher,
    ApplicationValidator,
    LinkedInSelectors,
    ApplicationConfig
)


class TestElementInteractor(unittest.TestCase):
    """Test ElementInteractor functionality"""
    
    def setUp(self):
        self.mock_driver = Mock()
        self.config = ApplicationConfig()
        self.interactor = ElementInteractor(self.mock_driver, self.config)
        
    def test_safe_click_success(self):
        """Test successful click"""
        mock_element = Mock()
        mock_element.click.return_value = None
        
        # Mock the wait condition
        mock_wait = Mock()
        mock_wait.until.return_value = True
        
        with patch('selenium.webdriver.support.ui.WebDriverWait', return_value=mock_wait):
            result = self.interactor.safe_click(mock_element)
        
        self.assertTrue(result)
        mock_element.click.assert_called_once()
        
    def test_safe_click_with_interception(self):
        """Test click with element interception"""
        mock_element = Mock()
        # First click fails, second succeeds
        mock_element.click.side_effect = [ElementClickInterceptedException(), None]
        
        # Mock WebDriverWait
        mock_wait = Mock()
        mock_wait.until.return_value = True
        
        # Mock find_elements to return empty list (no blocking elements)
        self.mock_driver.find_elements.return_value = []
        
        with patch('selenium.webdriver.support.ui.WebDriverWait', return_value=mock_wait):
            with patch.object(self.interactor, '_handle_click_interception'):
                result = self.interactor.safe_click(mock_element)
            
        self.assertTrue(result)
        
    def test_safe_send_keys(self):
        """Test sending keys to element"""
        mock_element = Mock()
        test_text = "test input"
        
        result = self.interactor.safe_send_keys(mock_element, test_text)
        
        self.assertTrue(result)
        mock_element.clear.assert_called_once()
        self.assertEqual(mock_element.send_keys.call_count, len(test_text))
        
    def test_find_clickable_button_by_text(self):
        """Test finding button by text"""
        mock_button = Mock()
        mock_button.is_displayed.return_value = True
        mock_button.is_enabled.return_value = True
        
        self.mock_driver.find_elements.return_value = [mock_button]
        
        result = self.interactor.find_clickable_button(['Submit'])
        
        self.assertEqual(result, mock_button)


class TestFormAnalyzer(unittest.TestCase):
    """Test FormAnalyzer functionality"""
    
    def setUp(self):
        self.mock_driver = Mock()
        self.analyzer = FormAnalyzer(self.mock_driver)
        
    def test_get_field_label_by_aria(self):
        """Test getting field label from aria-label"""
        mock_element = Mock()
        mock_element.get_attribute.side_effect = lambda attr: {
            'aria-label': 'First Name',
            'id': None,
            'placeholder': None
        }.get(attr)
        
        label = self.analyzer.get_field_label(mock_element)
        
        self.assertEqual(label, 'First Name')
        
    def test_get_field_label_by_placeholder(self):
        """Test getting field label from placeholder"""
        mock_element = Mock()
        mock_element.get_attribute.side_effect = lambda attr: {
            'aria-label': None,
            'id': None,
            'placeholder': 'Enter your email'
        }.get(attr)
        
        label = self.analyzer.get_field_label(mock_element)
        
        self.assertEqual(label, 'Enter your email')
        
    def test_analyze_form_structure(self):
        """Test form structure analysis"""
        # Mock text input
        mock_input = Mock()
        mock_input.is_displayed.return_value = True
        mock_input.get_attribute.side_effect = lambda attr: {
            'type': 'text',
            'required': 'true',
            'value': ''
        }.get(attr)
        
        # Mock dropdown with options
        mock_dropdown = Mock()
        mock_dropdown.is_displayed.return_value = True
        
        # Mock option elements
        mock_option1 = Mock()
        mock_option1.text = "Option 1"
        mock_option2 = Mock()
        mock_option2.text = "Option 2"
        
        # Set up find_elements to return options
        mock_dropdown.find_elements.return_value = [mock_option1, mock_option2]
        
        # Set up driver to return inputs and dropdowns
        def find_elements_side_effect(by, selector):
            if selector in ["input[type='text']", "input[type='email']", "input[type='tel']", 
                          "input[type='number']", "textarea"]:
                return [mock_input]
            elif selector == "select":
                return [mock_dropdown]
            else:
                return []
        
        self.mock_driver.find_elements.side_effect = find_elements_side_effect
        
        with patch.object(self.analyzer, 'get_field_label', return_value='Test Field'):
            form_data = self.analyzer.analyze_form_structure()
            
        self.assertEqual(len(form_data['text_fields']), 5)  # 5 selectors return same input
        self.assertEqual(form_data['text_fields'][0]['label'], 'Test Field')
        self.assertEqual(len(form_data['dropdowns']), 1)
        self.assertEqual(form_data['dropdowns'][0]['options'], ['Option 1', 'Option 2'])


class TestFieldMatcher(unittest.TestCase):
    """Test FieldMatcher functionality"""
    
    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'phone': '555-1234',
            'user_profile': {
                'name': 'John Doe',
                'location': 'New York, NY',
                'years_experience': '5'
            }
        }
        self.questions_db = {
            'What is your preferred work location?': 'Remote',
            'Are you authorized to work in the US?': 'Yes'
        }
        self.matcher = FieldMatcher(self.user_data, self.questions_db)
        
    def test_match_field_from_questions_db(self):
        """Test matching from questions database"""
        answer = self.matcher.match_field('What is your preferred work location?')
        self.assertEqual(answer, 'Remote')
        
    def test_match_field_email(self):
        """Test matching email field"""
        answer = self.matcher.match_field('Email Address')
        self.assertEqual(answer, '<EMAIL>')
        
    def test_match_field_phone(self):
        """Test matching phone field"""
        answer = self.matcher.match_field('Phone Number')
        self.assertEqual(answer, '555-1234')
        
    def test_match_field_first_name(self):
        """Test extracting first name"""
        answer = self.matcher.match_field('First Name')
        self.assertEqual(answer, 'John')
        
    def test_match_field_last_name(self):
        """Test extracting last name"""
        answer = self.matcher.match_field('Last Name')
        self.assertEqual(answer, 'Doe')
        
    def test_match_field_years_experience(self):
        """Test matching years of experience"""
        answer = self.matcher.match_field('How many years of experience do you have?')
        self.assertEqual(answer, '5')
        
    def test_match_field_relocation(self):
        """Test matching relocation question"""
        answer = self.matcher.match_field('Are you willing to relocate?')
        self.assertEqual(answer, 'No')
        
    def test_match_field_not_found(self):
        """Test field not found"""
        answer = self.matcher.match_field('Random Unknown Field')
        self.assertIsNone(answer)


class TestApplicationValidator(unittest.TestCase):
    """Test ApplicationValidator functionality"""
    
    def setUp(self):
        self.mock_driver = Mock()
        self.validator = ApplicationValidator(self.mock_driver)
        
    def test_is_application_complete_success(self):
        """Test detecting successful application"""
        mock_body = Mock()
        mock_body.text = "Thank you for applying to this position"
        self.mock_driver.find_element.return_value = mock_body
        
        result = self.validator.is_application_complete()
        
        self.assertTrue(result)
        
    def test_is_application_complete_not_complete(self):
        """Test detecting incomplete application"""
        mock_body = Mock()
        mock_body.text = "Please fill in all required fields"
        self.mock_driver.find_element.return_value = mock_body
        
        result = self.validator.is_application_complete()
        
        self.assertFalse(result)
        
    def test_has_errors_detected(self):
        """Test error detection"""
        mock_body = Mock()
        mock_body.text = "This field is required. Please complete all fields."
        self.mock_driver.find_element.return_value = mock_body
        
        # Mock error elements
        mock_error = Mock()
        mock_error.is_displayed.return_value = True
        mock_error.text = "Email is invalid"
        self.mock_driver.find_elements.return_value = [mock_error]
        
        has_errors, error_msgs = self.validator.has_errors()
        
        self.assertTrue(has_errors)
        self.assertIn('this field is required', error_msgs)
        self.assertIn('Email is invalid', error_msgs)
        
    def test_check_for_warnings(self):
        """Test warning detection"""
        mock_modal = Mock()
        mock_modal.is_displayed.return_value = True
        mock_modal.text = "Are you sure you want to proceed?"
        
        # Mock find_element to return modal for first selector, raise exception for others
        def find_element_side_effect(by, selector):
            if selector == LinkedInSelectors.WARNING_MODALS[0]:
                return mock_modal
            raise Exception("No element found")
        
        self.mock_driver.find_element.side_effect = find_element_side_effect
        
        has_warning, warning_text = self.validator.check_for_warnings()
        
        self.assertTrue(has_warning)
        self.assertEqual(warning_text, "Are you sure you want to proceed?")


class TestApplicationConfig(unittest.TestCase):
    """Test ApplicationConfig functionality"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = ApplicationConfig()
        
        self.assertEqual(config.click_delay, (0.5, 1.0))
        self.assertEqual(config.max_click_retries, 3)
        self.assertTrue(config.dismiss_modals)
        
    def test_from_dict(self):
        """Test creating config from dictionary"""
        config_dict = {
            'delays': {
                'click': [1.0, 2.0],
                'typing': [0.1, 0.2],
                'page_load': 5.0
            },
            'retries': {
                'click': 5,
                'form': 3
            },
            'features': {
                'dismiss_modals': False,
                'auto_scroll': False
            }
        }
        
        config = ApplicationConfig.from_dict(config_dict)
        
        self.assertEqual(config.click_delay, (1.0, 2.0))
        self.assertEqual(config.max_click_retries, 5)
        self.assertFalse(config.dismiss_modals)
        self.assertFalse(config.auto_scroll)


class TestLinkedInSelectors(unittest.TestCase):
    """Test LinkedInSelectors constants"""
    
    def test_selectors_defined(self):
        """Test that all selector categories are defined"""
        selectors = LinkedInSelectors()
        
        self.assertTrue(hasattr(selectors, 'EASY_APPLY_BUTTONS'))
        self.assertTrue(hasattr(selectors, 'NEXT_BUTTONS'))
        self.assertTrue(hasattr(selectors, 'SUBMIT_BUTTONS'))
        self.assertTrue(hasattr(selectors, 'TEXT_INPUTS'))
        self.assertTrue(hasattr(selectors, 'SUCCESS_INDICATORS'))
        self.assertTrue(hasattr(selectors, 'ERROR_INDICATORS'))
        
    def test_selectors_not_empty(self):
        """Test that selector lists are not empty"""
        selectors = LinkedInSelectors()
        
        self.assertGreater(len(selectors.EASY_APPLY_BUTTONS), 0)
        self.assertGreater(len(selectors.SUCCESS_INDICATORS), 0)
        self.assertGreater(len(selectors.ERROR_INDICATORS), 0)


if __name__ == '__main__':
    unittest.main()