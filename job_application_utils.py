#!/usr/bin/env python3
"""
Job Application Utilities Module
Centralized utilities for form filling, element interaction, and common patterns
"""
import time
import random
from typing import Dict, List, Optional, Tuple, Any, Union
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    ElementClickInterceptedException,
    StaleElementReferenceException,
    TimeoutException,
    NoSuchElementException
)
from dataclasses import dataclass
from enum import Enum


class ElementType(Enum):
    """Types of form elements"""
    TEXT_INPUT = "text_input"
    DROPDOWN = "dropdown"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    FILE_UPLOAD = "file_upload"
    BUTTON = "button"


@dataclass
class LinkedInSelectors:
    """Centralized selectors for LinkedIn elements"""
    # Easy Apply button selectors (based on actual LinkedIn UI)
    EASY_APPLY_BUTTONS = [
        "button[aria-label*='Easy Apply']",
        "button[data-control-name='jobdetails_topcard_inapply']", 
        ".jobs-unified-top-card__content button[aria-label*='Easy Apply']",
        "div.jobs-apply-button button[aria-label*='Easy Apply']",
        "button.artdeco-button--primary[aria-label*='Easy Apply']"
    ]
    
    # Navigation button selectors (based on screenshots)
    NEXT_BUTTONS = [
        "button[data-easy-apply-next-button]",
        "button[aria-label='Continue to next step']", 
        "button[data-control-name*='continue']",
        "button[data-live-test-easy-apply-next-button]",
        "button.artdeco-button.artdeco-button--2.artdeco-button--primary.ember-view",
        "button:has(span[class*='artdeco-button__text'])",
        "button.artdeco-button--primary"
    ]
    
    SUBMIT_BUTTONS = [
        "button[data-easy-apply-submit-button]",
        "button[data-live-test-easy-apply-submit-button]",
        "button[aria-label='Submit application']",
        "button[data-control-name*='submit']", 
        "button.artdeco-button.artdeco-button--2.artdeco-button--primary.ember-view",
        "button.artdeco-button--primary"
    ]
    
    # Form field selectors
    TEXT_INPUTS = [
        "input[type='text']",
        "input[type='email']",
        "input[type='tel']",
        "input[type='number']",
        "textarea"
    ]
    
    DROPDOWNS = [
        "select",
        "[data-test-form-select]",
        ".fb-form-element__dropdown"
    ]
    
    # Warning/Error selectors
    WARNING_MODALS = [
        "[data-test-modal]",
        ".artdeco-modal",
        "[role='alertdialog']"
    ]
    
    BLOCKING_OVERLAYS = [
        ".artdeco-entity-lockup",
        ".msg-overlay-list-bubble",
        "[data-test-modal]",
        ".artdeco-modal"
    ]
    
    # Success indicators
    SUCCESS_INDICATORS = [
        "application sent",
        "application submitted",
        "thank you for applying",
        "your application was sent",
        "application received"
    ]
    
    # Error indicators
    ERROR_INDICATORS = [
        "required field",
        "please complete",
        "this field is required",
        "invalid entry",
        "please enter"
    ]


@dataclass
class ApplicationConfig:
    """Configuration for application behavior"""
    # Delays (in seconds)
    click_delay: Tuple[float, float] = (0.5, 1.0)
    typing_delay: Tuple[float, float] = (0.05, 0.15)
    page_load_delay: float = 3.0
    form_submit_delay: float = 2.0
    
    # Retry settings
    max_click_retries: int = 3
    max_form_retries: int = 2
    
    # Element interaction timeouts
    element_timeout: int = 10
    page_timeout: int = 30
    
    # Feature flags
    dismiss_modals: bool = True
    auto_scroll: bool = True
    use_javascript_fallback: bool = True
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ApplicationConfig':
        """Create config from dictionary"""
        return cls(
            click_delay=tuple(config_dict.get('delays', {}).get('click', [0.5, 1.0])),
            typing_delay=tuple(config_dict.get('delays', {}).get('typing', [0.05, 0.15])),
            page_load_delay=config_dict.get('delays', {}).get('page_load', 3.0),
            form_submit_delay=config_dict.get('delays', {}).get('form_submit', 2.0),
            max_click_retries=config_dict.get('retries', {}).get('click', 3),
            max_form_retries=config_dict.get('retries', {}).get('form', 2),
            element_timeout=config_dict.get('timeouts', {}).get('element', 10),
            page_timeout=config_dict.get('timeouts', {}).get('page', 30),
            dismiss_modals=config_dict.get('features', {}).get('dismiss_modals', True),
            auto_scroll=config_dict.get('features', {}).get('auto_scroll', True),
            use_javascript_fallback=config_dict.get('features', {}).get('use_js_fallback', True)
        )


class ElementInteractor:
    """Handles all element interactions with retry logic"""
    
    def __init__(self, driver, config: Optional[ApplicationConfig] = None, logger=None):
        self.driver = driver
        self.config = config or ApplicationConfig()
        self.logger = logger
        
    def log(self, level: str, message: str):
        """Log with appropriate method"""
        if self.logger:
            getattr(self.logger, level, print)(f"[{level.upper()}] {message}")
        else:
            print(f"[{level.upper()}] {message}")
    
    def safe_click(self, element, retries: Optional[int] = None) -> bool:
        """
        Enhanced click handler with multiple strategies and retry logic
        """
        retries = retries or self.config.max_click_retries
        
        for attempt in range(retries):
            try:
                # Scroll into view
                if self.config.auto_scroll:
                    self.driver.execute_script(
                        "arguments[0].scrollIntoView({block: 'center', inline: 'center'});",
                        element
                    )
                    time.sleep(0.5)
                
                # Wait for clickability
                WebDriverWait(self.driver, self.config.element_timeout).until(
                    EC.element_to_be_clickable(element)
                )
                
                # Try click methods in order
                click_methods = [
                    lambda: element.click(),
                    lambda: ActionChains(self.driver).move_to_element(element).click().perform(),
                    lambda: self.driver.execute_script("arguments[0].click();", element)
                ]
                
                for method in click_methods:
                    try:
                        method()
                        time.sleep(random.uniform(*self.config.click_delay))
                        return True
                    except ElementClickInterceptedException:
                        if attempt < retries - 1:
                            self._handle_click_interception()
                        continue
                    except Exception:
                        continue
                        
            except Exception as e:
                self.log('debug', f"Click attempt {attempt + 1} failed: {str(e)}")
                
        return False
    
    def _handle_click_interception(self):
        """Handle elements blocking clicks"""
        if not self.config.dismiss_modals:
            return
            
        for selector in LinkedInSelectors.BLOCKING_OVERLAYS:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for el in elements:
                    if self.config.use_javascript_fallback:
                        self.driver.execute_script("arguments[0].style.display='none';", el)
                    else:
                        # Try to close properly
                        close_btn = el.find_element(By.CSS_SELECTOR, "[aria-label*='Dismiss']")
                        close_btn.click()
            except:
                pass
                
    def safe_send_keys(self, element, text: str, clear_first: bool = True) -> bool:
        """
        Safely send keys to an element with human-like typing
        """
        try:
            if clear_first:
                element.clear()
                
            # Type with random delays
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(*self.config.typing_delay))
                
            return True
        except Exception as e:
            self.log('error', f"Failed to send keys: {str(e)}")
            return False
    
    def find_clickable_button(self, button_texts: List[str], 
                            selectors: Optional[List[str]] = None) -> Optional[Any]:
        """
        Find a clickable button by text or selector
        Prioritize buttons within Easy Apply modal if it exists
        """
        # Try to find the Easy Apply modal first with updated selectors from screenshots
        modal_selectors = [
            "div[data-test-modal-id='easy-apply-modal']",
            ".artdeco-modal[data-test-modal-id='easy-apply-modal']",
            "div.jobs-easy-apply-modal",
            ".artdeco-modal",
            "[data-test-modal]",
            "div[role='dialog']"
        ]
        
        modal_found = False
        
        for modal_selector in modal_selectors:
            try:
                modal = self.driver.find_element(By.CSS_SELECTOR, modal_selector)
                if modal.is_displayed():
                    modal_found = True
                    # Try modal-scoped search first
                    for text in button_texts:
                        try:
                            # Find buttons within this specific modal using relative xpath
                            buttons = modal.find_elements(By.XPATH, f".//button[normalize-space()='{text}']")
                            
                            if not buttons:
                                buttons = modal.find_elements(By.XPATH, f".//button[contains(., '{text}')]")
                            
                            for button in buttons:
                                if button.is_displayed() and button.is_enabled():
                                    return button
                        except:
                            continue
                    break
            except:
                continue
            
        # Fallback to global search
        for text in button_texts:
            try:
                # XPath for exact text match
                xpath = f"//button[normalize-space()='{text}']"
                buttons = self.driver.find_elements(By.XPATH, xpath)
                
                # Also try contains
                if not buttons:
                    xpath = f"//button[contains(., '{text}')]"
                    buttons = self.driver.find_elements(By.XPATH, xpath)
                
                for button in buttons:
                    if button.is_displayed() and button.is_enabled():
                        return button
            except:
                continue
        
        # Try by selector
        if selectors:
            for selector in selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            return button
                except:
                    continue
                    
        return None


class FormAnalyzer:
    """Analyzes and extracts information from forms"""
    
    def __init__(self, driver, logger=None):
        self.driver = driver
        self.logger = logger
        
    def get_field_label(self, element) -> str:
        """Extract field label using multiple strategies"""
        strategies = [
            # Strategy 1: Associated label
            lambda: self._get_label_by_for(element),
            # Strategy 2: Aria label
            lambda: element.get_attribute("aria-label"),
            # Strategy 3: Placeholder
            lambda: element.get_attribute("placeholder"),
            # Strategy 4: Preceding text
            lambda: self._get_preceding_text(element),
            # Strategy 5: Parent label
            lambda: self._get_parent_label(element)
        ]
        
        for strategy in strategies:
            try:
                label = strategy()
                if label and label.strip():
                    return label.strip()
            except:
                continue
                
        return ""
    
    def _get_label_by_for(self, element) -> Optional[str]:
        """Get label using 'for' attribute"""
        element_id = element.get_attribute("id")
        if not element_id:
            return None
            
        try:
            label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{element_id}']")
            return label.text
        except:
            return None
    
    def _get_preceding_text(self, element) -> Optional[str]:
        """Get text from preceding sibling"""
        try:
            script = """
            var el = arguments[0];
            var prev = el.previousElementSibling;
            while (prev && prev.tagName !== 'LABEL' && prev.tagName !== 'SPAN') {
                prev = prev.previousElementSibling;
            }
            return prev ? prev.textContent : null;
            """
            return self.driver.execute_script(script, element)
        except:
            return None
    
    def _get_parent_label(self, element) -> Optional[str]:
        """Get label from parent element"""
        try:
            parent = element.find_element(By.XPATH, "./..")
            label_elem = parent.find_element(By.TAG_NAME, "label")
            return label_elem.text
        except:
            return None
    
    def analyze_form_structure(self) -> Dict[str, List[Dict]]:
        """
        Analyze current form and return structured field information
        Focus on the Easy Apply modal if it exists
        """
        form_data = {
            "text_fields": [],
            "dropdowns": [],
            "radio_groups": [],
            "checkboxes": [],
            "file_uploads": []
        }
        
        # Try to find the Easy Apply modal first with updated selectors from screenshots  
        modal_selectors = [
            "div[data-test-modal-id='easy-apply-modal']",
            ".artdeco-modal.artdeco-modal-overlay.artdeco-modal-overlay--layer-default.jobs-easy-apply-modal",
            "div.jobs-easy-apply-modal",
            ".artdeco-modal[aria-labelledby*='easy-apply']",
            ".artdeco-modal.ember-view",
            "[data-test-modal]",
            "div[role='dialog']"
        ]
        
        search_context = self.driver
        modal_found = False
        
        # Wait a bit for modal to load
        import time
        time.sleep(1)
        
        for selector in modal_selectors:
            try:
                modal = self.driver.find_element(By.CSS_SELECTOR, selector)
                if modal.is_displayed():
                    search_context = modal
                    modal_found = True
                    if self.logger:
                        self.logger.info(f"Found modal using selector: {selector}")
                    break
            except:
                continue
        
        if not modal_found:
            if self.logger:
                self.logger.info("No modal found with any selector, analyzing entire page")
        
        # Analyze text inputs within the context (modal or full page)
        # Enhanced selectors for LinkedIn's current form structure
        text_input_selectors = [
            "input[type='text']",
            "input[type='email']", 
            "input[type='tel']",
            "input[type='number']",
            "textarea",
            "input.artdeco-text-input--input",
            "input[id*='email']",
            "input[id*='phone']",
            "input[name*='phoneNumber']",
            "input[name*='email']"
        ]
        
        for selector in text_input_selectors:
            elements = search_context.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed():
                    # Skip hidden or system inputs
                    if elem.get_attribute('type') == 'hidden':
                        continue
                        
                    form_data["text_fields"].append({
                        "element": elem,
                        "label": self.get_field_label(elem),
                        "type": elem.get_attribute("type") or "text",
                        "required": elem.get_attribute("required") is not None or 'required' in (elem.get_attribute('class') or ''),
                        "value": elem.get_attribute("value") or ""
                    })
        
        # Analyze dropdowns within the context
        dropdown_selectors = [
            "select",
            "[data-test-form-select]",
            ".fb-form-element__dropdown",
            "select.artdeco-dropdown__select",
            "[role='combobox']"
        ]
        
        for selector in dropdown_selectors:
            dropdowns = search_context.find_elements(By.CSS_SELECTOR, selector)
            for dropdown in dropdowns:
                if not dropdown.is_displayed():
                    continue

                # options
                try:
                    if dropdown.tag_name == 'select':
                        opts = [opt.text for opt in dropdown.find_elements(By.TAG_NAME, "option") if opt.text.strip()]
                    else:
                        # For custom dropdowns, try to find options
                        opts = []
                except (TypeError, Exception):
                    opts = []

                # selected
                try:
                    if dropdown.tag_name == 'select':
                        sel_els = dropdown.find_elements(By.CSS_SELECTOR, "option[selected]")
                        selected = sel_els[0].text if sel_els else None
                    else:
                        selected = dropdown.get_attribute('value') or None
                except (TypeError, Exception):
                    selected = None

                form_data["dropdowns"].append({
                    "element": dropdown,
                    "label": self.get_field_label(dropdown),
                    "options": opts,
                    "selected": selected
                })

        return form_data


class FieldMatcher:
    """Matches form fields to user data"""
    
    def __init__(self, user_data: Dict[str, Any], questions_db: Optional[Dict[str, str]] = None):
        self.user_data = user_data
        self.questions_db = questions_db or {}
        
    def match_field(self, field_label: str, field_type: str = "text") -> Optional[str]:
        """
        Match a field label to appropriate user data
        """
        if not field_label:
            return None
            
        field_lower = field_label.lower().strip()
        
        # Check questions database first
        if field_label in self.questions_db:
            return self.questions_db[field_label]
        
        # Check exact matches in questions DB (case-insensitive)
        for question, answer in self.questions_db.items():
            if question.lower().strip() == field_lower:
                return answer
        
        # Field mapping strategies
        field_mappings = {
            'phone': ['phone', 'mobile', 'cell', 'telephone', 'contact number'],
            'email': ['email', 'e-mail', 'email address'],
            'first_name': ['first name', 'given name', 'forename'],
            'last_name': ['last name', 'surname', 'family name'],
            'full_name': ['full name', 'name', 'your name'],
            'city': ['city', 'location', 'town'],
            'state': ['state', 'province'],
            'country': ['country', 'nation'],
            'years_experience': ['years of experience', 'experience', 'years', 'how many years'],
            'salary': ['salary', 'compensation', 'pay', 'wage', 'expected salary'],
            'notice_period': ['notice period', 'availability', 'when can you start'],
            'linkedin': ['linkedin', 'linkedin profile', 'linkedin url'],
            'website': ['website', 'portfolio', 'personal website'],
            'github': ['github', 'github profile']
        }
        
        # Check each mapping
        for data_key, keywords in field_mappings.items():
            if any(keyword in field_lower for keyword in keywords):
                return self._get_user_value(data_key)
        
        # Special handling for yes/no questions
        if any(word in field_lower for word in ['willing', 'able', 'available', 'interested']):
            if 'relocate' in field_lower:
                return self.user_data.get('willing_to_relocate', 'No')
            elif 'remote' in field_lower:
                return 'Yes'
            elif 'sponsor' in field_lower or 'visa' in field_lower:
                return self.user_data.get('requires_sponsorship', 'No')
        
        return None
    
    def _get_user_value(self, key: str) -> Optional[str]:
        """Get value from user data with fallbacks"""
        # Direct access
        if key in self.user_data:
            return str(self.user_data[key])
        
        # Check user_profile
        user_profile = self.user_data.get('user_profile', {})
        if key in user_profile:
            return str(user_profile[key])
        
        # Special cases
        if key == 'full_name' and 'name' in user_profile:
            return user_profile['name']
        elif key == 'first_name' and 'name' in user_profile:
            return user_profile['name'].split()[0] if user_profile['name'] else ''
        elif key == 'last_name' and 'name' in user_profile:
            return user_profile['name'].split()[-1] if user_profile['name'] else ''
        
        return None


class ApplicationValidator:
    """Validates application state and checks for errors"""
    
    def __init__(self, driver, selectors: Optional[LinkedInSelectors] = None):
        self.driver = driver
        self.selectors = selectors or LinkedInSelectors()
        
    def is_application_complete(self) -> bool:
        """Check if application was successfully submitted"""
        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            return any(indicator in page_text for indicator in self.selectors.SUCCESS_INDICATORS)
        except:
            return False
    
    def has_errors(self) -> Tuple[bool, List[str]]:
        """Check for form validation errors"""
        errors = []
        
        try:
            # Check page text for error indicators
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            
            for indicator in self.selectors.ERROR_INDICATORS:
                if indicator in page_text:
                    errors.append(indicator)
            
            # Check for error elements
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='alert'], .error, .field-error")
            for elem in error_elements:
                if elem.is_displayed() and elem.text:
                    errors.append(elem.text)
                    
        except:
            pass
            
        return len(errors) > 0, errors
    
    def check_for_warnings(self) -> Tuple[bool, Optional[str]]:
        """Check for warning modals or messages"""
        for selector in self.selectors.WARNING_MODALS:
            try:
                modal = self.driver.find_element(By.CSS_SELECTOR, selector)
                if modal.is_displayed():
                    # Try to get warning text
                    warning_text = modal.text
                    return True, warning_text
            except:
                continue
                
        return False, None